"use client"

import React from "react"
import { useState, useRef, use<PERSON><PERSON>back, useMemo, useEffect } from "react"
import StatsCards from "@/components/mf/StatsCard"
import { Globe, Twitter, Facebook, Instagram, Linkedin, Youtube, Loader2, Search, Newspaper } from "lucide-react"
import {
  BarChart,
  Bar,
  LineChart,/*  */
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { FaTwitter, FaFacebook, FaInstagram, FaLinkedin, FaYoutube } from "react-icons/fa6"
import { useQuery } from "react-query"
import axios from "axios"
import Endpoint from "@/common/endpoint"
import { format, subMonths, startOfMonth } from "date-fns"
import { AxiosRequestConfig } from "axios"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Donut<PERSON>hart from "@/components/mf/DonutChart"
import { onExpand, downloadURI, handleCSVDownloadFromResponse } from "@/lib/utils"
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import domToImage from "dom-to-image"
import { StackedBarWithLine } from "@/components/mf/charts/StackedBarwithLine";
import HeaderRow from "@/components/mf/HeaderRow"
import CustomCategoryCard from "@/components/mf/CustomCategoryCard"
import { DateRange } from "react-day-picker";
import { FilterPill } from "@/components/mf/Filters/FilterPill";
import { useDateRange } from "@/components/mf/DateRangeContext"
import { formatNumber } from '@/lib/utils';
import { usePackage } from "@/components/mf/PackageContext";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import HorizontalVerticalBarChart from "@/components/mf/HorizontalVerticalBarChart"
import ProgressBarChart1 from "@/components/mf/charts/ProgressBar"

interface TrafficTrendData {
  date: string;
  Active: number;
  "In Progress": number;
  Closed: number;
  trend?: number;
}

interface YAxis1 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string;
  tickFormatter?: (value: number) => string;
}

interface YAxis2 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string;
  tickFormatter?: (value: number) => string;
}



// Add frequency options
const selectOptionsV = ["Daily", "Weekly", "Monthly", "Yearly"];


interface IncidentStatsResponse {
  data: {
    total: number;
    active: {
      count: number;
      percentage: number;
    };
    in_progress: {
      count: number;
      percentage: number;
    };
    closed: {
      count: number;
      percentage: number;
    };
    closed_takedown_completed: {
      count: number;
      percentage: number;
    };
    closed_takedown_sticky_incident: {
      count: number;
      percentage: number;
    };
    closed_recommend_to_legal: {
      count: number;
      percentage: number;
    };
  };
  status: boolean;
}

interface IncidentStats {
  title: string;
  value: string;
  change: string;
  colorClass: string;
}


const incidentsChartConfig = {
  Active: {
    label: "Under Brand Review",
    color: "#EE4B2B"
  },
  "In Progress": {
    label: "Takedown Initiated",
    // label: "Takedown Initiated",
    color: "#FFDB58"
  },
  Closed: {
    label: "Closed Incidents",
    color: "#2E8B57",
    stack: "a"
  },
  trend: {
    label: "Incidents Reported",
    color: "#540094",
    type: "line"
  }
};

interface XAxisConfig {
  dataKey: string;
  tickFormatter: (value: string) => string;
  dy: number;
}

type ChannelName = string;

// Add chart config for Type of Case (Category)
const caseCategoryConfig = {
  Today: {
    label: "Count",
    color: "#540094",
    hideInLegend: true,
    tooltipFormatter: (value: number, name: string, props: any) => {
      return [`Count: ${value}`, `Category: ${props.payload.date}`];
    }
  }
};



interface AnalyticsCardData {
  id: string;
  title: string;
  type: string;
  data: any;
  config?: any;
  isLoading?: boolean;
}

interface HeaderRowProps {
  title: string;
  onExport?: () => void;
  onExpand?: () => void;
  isRadioButton?: boolean;
  isSelect?: boolean;
  placeholder?: string;
  selectoptions?: { value: string; label: string; }[];
  handleFrequencyChange?: (value: string) => void;
  selectedFrequency?: string;
}


//Function to transform API response to the required format
const transformStatsData = (response: IncidentStatsResponse | undefined) => {
  // Add default values in case response is undefined
  const defaultData = {
    total: 0,
    active: { count: 0, percentage: 0 },
    in_progress: { count: 0, percentage: 0 },
    closed: { count: 0, percentage: 0 },
    closed_takedown_completed: { count: 0, percentage: 0 },
    closed_takedown_sticky_incident: { count: 0, percentage: 0 },
    closed_recommend_to_legal: { count: 0, percentage: 0 }
  };

  const data = response?.data || defaultData;

  return {
    Total: {
      count: data.total,
      title: "Incidents Reported",
      color: "#540094" // Purple
    },
    Active: {
      count: data.active.count,
      title: "Under Brand Review",
      color: "#FF0000"
    },
    InProgress: {
      count: data.in_progress.count,
      title: "Takedown Initiated",
      // title: "Takedown Initiated",
      color: "#FFDB58"
    },
    Closed: {
      count: data.closed.count,
      title: "Closed Incidents",
      color: "#2E8B57",
      breakdown: [
        {
          label: "Take Down",
          value: data.closed_takedown_completed?.count || 0
        },
        {
          label: "No Action Required",
          value: data.closed_takedown_sticky_incident?.count || 0
        },
        {
          label: "Recommend to Legal",
          value: data.closed_recommend_to_legal?.count || 0
        }
      ]
    }
  };
};

// Add interface for Category data response
interface CategoryDataResponse {
  data: {
    fake_handles: {
      count: number;
      percentage: number;
    };
    fake_website: {
      count: number;
      percentage: number;
    };
    fake_care_number: {
      count: number;
      percentage: number;
    };
    fake_job_promotions: {
      count: number;
      percentage: number;
    };
    fake_offers: {
      count: number;
      percentage: number;
    };
    sponsored_ads: {
      count: number;
      percentage: number;
    };
  };
  status: boolean;
}

// Add interface for transformed category data
interface TransformedCategoryData {
  title: string;
  count: string;
  percentage: string;
  bgColor: string;
  link: string;
  icon?: string;
}

// Helper function to transform API response to our UI format
const transformCategoryData = (response: CategoryDataResponse): TransformedCategoryData[] => {
  const categoryMapping: Record<string, { title: string; link: string; bgColor: string; icon?: string }> = {
    fake_handles: {
      title: "Social Media",
      link: "https://duwhu243zraf9.cloudfront.net/icon/Social_Media.jpg",
      bgColor: "bg-gradient-to-r from-blue-100 to-blue-200"
    },
    fake_website: {
      title: "Website/App",
      link: "https://duwhu243zraf9.cloudfront.net/icon/Website_App.jpg",
      bgColor: "bg-gradient-to-r from-purple-100 to-purple-200"
    },
    fake_care_number: {
      title: "Customer Care No",
      link: "https://duwhu243zraf9.cloudfront.net/icon/Customer_Care_Number.jpg",
      bgColor: "bg-gradient-to-r from-red-100 to-red-200"
    },
    fake_job_promotions: {
      title: "Job Promotions",
      link: "https://duwhu243zraf9.cloudfront.net/icon/Job_Promotions.png",
      bgColor: "bg-gradient-to-r from-green-100 to-green-200"
    },
    fake_offers: {
      title: "Offers",
      link: "https://duwhu243zraf9.cloudfront.net/icon/Offers.jpg",
      bgColor: "bg-gradient-to-r from-yellow-100 to-yellow-200"
    },
    sponsored_ads: {
      title: "Sponsored Ads",
      link: "https://duwhu243zraf9.cloudfront.net/icon/Sponsored_Ad.jpg",
      bgColor: "bg-gradient-to-r from-orange-100 to-orange-200"
    }
  };

  // Define the desired order of categories
  const categoryOrder = ['fake_website', 'fake_handles', 'fake_care_number', 'fake_job_promotions', 'fake_offers', 'sponsored_ads'];

  try {
    const data = response.data;
    const transformedData = Object.entries(data).map(([key, value]) => {
      const category = categoryMapping[key as keyof typeof categoryMapping];
      if (!value || !category) return null;

      const transformed: TransformedCategoryData = {
        title: category.title,
        count: value.count.toString(),
        percentage: `${value.percentage.toFixed(2)}%`,
        bgColor: category.bgColor,
        link: category.link
      };
      if (category.icon) {
        transformed.icon = category.icon;
      }
      return transformed;
    }).filter((item): item is TransformedCategoryData => item !== null);

    // Reorder the cards to put Website/App first, then Social Media, followed by remaining categories
    const reorderedData = [...transformedData];
    
    // Define the desired order
    const desiredOrder = ["Website/App", "Social Media", "Customer Care No", "Job Promotions", "Offers", "Sponsored Ads"];
    
    // Sort the data according to the desired order
    reorderedData.sort((a, b) => {
      const aIndex = desiredOrder.indexOf(a.title);
      const bIndex = desiredOrder.indexOf(b.title);
      
      // If both items are in the desired order, sort by their position
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      
      // If only one item is in the desired order, prioritize it
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      
      // If neither item is in the desired order, maintain original order
      return 0;
    });

    return reorderedData;
  } catch (error) {
    console.error('Error transforming category data:', error);
    return [];
  }
};

// Interface for Channel data response
interface ChannelDataResponse {
  status: boolean;
  data: {
    channel: string;
    count: number;
    percentage: number;
  }[];
  total_count?: number; // Add total_count field from API response
}

// Interface for Platform Wise data response
interface PlatformWiseResponse {
  status: boolean;
  data: {
    sub_channel: string;
    count: number;
    percentage: number;
    color?: string; // Color from API response
    icon?: string; // Icon from API response
  }[];
}

// Interface for transformed platform wise data
interface PlatformHandle {
  handle: string;
  platform: string;
  influenceScore: number;
  icon?: string | any; // string for URL, any for component fallback
}

interface TransformedPlatformWiseData {
  [key: string]: string | number | PlatformHandle[] | undefined;
  label: string;
  visit: number;
  fill: string;
  handles: PlatformHandle[];
}

// Interface for the API response
interface IncidentReportItem {
  inserted_date: string;
  total: number;
  active: number;
  in_progress: number;
  closed: number;
}

interface IncidentReportResponse {
  data: IncidentReportItem[];
  status: boolean;
}

// Interface for transformed channel data
interface TransformedChannelData {
  [key: string]: string | number | undefined;
  label: string;
  visit: number;
  fill: string;
}

// Interface for Contact Numbers API response
interface ContactNumberResponse {
  data: {
    publisher: string;
    count: number;
    percentage: number;
  }[];
  status: boolean;
}

// Interface for TOP_HANDLES API response
interface TopHandlesResponse {
  data: {
    handles: string;
    count: number;
  }[];
  status: boolean;
}

// Function to transform the API response for the chart
const transformTopHandlesData = (response: TopHandlesResponse) => {
  if (!response?.data) return [];

  return response.data
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)
    .map(item => ({
      number: item.handles,
      value: item.count
    }));
};

// Add interface for contact numbers data
interface ContactNumberData {
  number: string;
  value: number;
}


{/* FILTER */ }
interface FilterResponse {
  data: string[];
  status: boolean;
}


interface FilterItem {
  label: string;
  checked: boolean;
}

interface FilterGroupState {
  filters: FilterItem[];
  is_select_all: boolean;
  selected_count: number;
}

interface FilterStateMap {
  brand: FilterGroupState;
  priority: FilterGroupState;
  country: FilterGroupState;
}

// Add hardcoded data for donut charts with better colors
const contactNumbersDonutData = [
  { number: "Customer Support", value: 35, percentage: 35, color: "#4B0082" }, // Indigo
  { number: "Sales Inquiries", value: 25, percentage: 25, color: "#00A86B" }, // Jade
  { number: "Technical Support", value: 20, percentage: 20, color: "#1E90FF" }, // Dodger Blue
  { number: "Fraud Reports", value: 15, percentage: 15, color: "#DC143C" }, // Crimson
  { number: "General Inquiries", value: 5, percentage: 5, color: "#FFD700" } // Gold
];

const topHandlesDonutData = [
  { number: "Twitter Official", value: 30, percentage: 30, color: "#1DA1F2" }, // Twitter Blue
  { number: "Facebook Page", value: 25, percentage: 25, color: "#4267B2" }, // Facebook Blue
  { number: "Instagram Business", value: 20, percentage: 20, color: "#E4405F" }, // Instagram Pink
  { number: "LinkedIn Corporate", value: 15, percentage: 15, color: "#0A66C2" }, // LinkedIn Blue
  { number: "YouTube Channel", value: 10, percentage: 10, color: "#FF0000" } // YouTube Red
];

// Add interface for Case Category response
interface CaseCategoryResponse {
  status: boolean;
  data: {
    category: string;
    count: number;
  }[];
}

interface CaseCategoryData {
  date: string;
  Today: number;
}

// Update the ChartData interface to match chartData from HorizontalVerticalBarChart
interface ChartData {
  label: string;
  value: number;
  [key: string]: string | number;
}

const BrandInfringementDashboard = () => {
  // Add state for selected platform (must be at the very top before any usage)
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const cardRefs = useRef<HTMLElement[]>([])
  const [expandedCard, setExpandedCard] = useState<number | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const incidentsChartRef = useRef<HTMLDivElement>(null)
  const [selectedChannel, setSelectedChannel] = useState<ChannelName | null>(null);
  const [selectedFrequencyV, setSelectedFrequencyV] = useState("daily");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFrequencyIncidents, setSelectedFrequencyIncidents] = useState("Daily");
  const [dueDateRange, setDueDateRange] = useState<DateRange | undefined>(undefined);
  const [selectedType, setSelectedType] = useState<string>("");
  const [ExistingEpublisher, setExistingEPublisher] = useState<any>([]);
  const [chartConfigep, setChartConfigep] = useState<any>({});


  const [selectedPublisher, setSelectedPublisher] = useState<string | undefined>(undefined);
  const [selectedSubPublisher, setSelectedSubPublisher] = useState<string>("all");
  const [selectedCampaign, setSelectedCampaign] = useState<string>("all");
  const [selectedChannelFilter, setSelectedChannelFilter] = useState<string>("all");
  const [selectedEventType, setSelectedEventType] = useState<string>("all");


  const [selectedBrand, setSelectedBrand] = useState<string[]>(["all"]);
  const [selectedpriority, setSelectedpriority] = useState<string[]>(["all"]);
  const [selectedcountry, setSelectedcountry] = useState<string[]>(["all"]);


  const [selectedTopHandle, setSelectedTopHandle] = useState<string | undefined>(undefined);


  const [tempBrand, setTempBrand] = useState<string[]>([]);
  const [temppriority, setTemppriority] = useState<string[]>([]);
  const [tempcountry, setTempcountry] = useState<string[]>([]);

  const visitEventOptions = [
    { value: "visit", label: "Visit" },
    { value: "event", label: "Event" },
  ];

  const handleTypeChange = (value: string) => {
    setSelectedType(value);
  };

  const yAxisConfigE = {
    dataKey: "label",
    title: "Event Publisher Name",
  };

  // Replace dateRange state with context
  const { startDate: fromDate, endDate: toDate } = useDateRange();

  // Calculate the minimum and maximum allowed dates
  const getMinMaxDates = () => {
    const today = new Date();
    const currentMonth = today.getMonth();
    const quarterStartMonth = Math.floor(currentMonth / 3) * 3;
    const minDate = new Date(today.getFullYear(), quarterStartMonth - 3, 1);
    return {
      minDate,
      maxDate: today
    };
  };

  // Helper function to get icon for platform
  const getIconForPlatform = (platform: string) => {
    const iconMap: { [key: string]: any } = {
      'Twitter': Twitter,
      'Facebook': Facebook,
      'Instagram': Instagram,
      'LinkedIn': Linkedin,
      'YouTube': Youtube
    };
    return iconMap[platform] || Twitter; // Default to Twitter icon
  };

  const { selectedPackage } = usePackage();

  // Common query params for all APIs
  const queryParams = useMemo(() => {
    const getFilterArray = (selected: string[]) => {
      // If no filters are selected or only "all" is selected, return ["all"]
      if (selected.length === 0 || (selected.length === 1 && selected.includes("all"))) {
        return ["all"];
      }
      // If "all" is included along with other selections, remove "all" and return only specific selections
      return selected.filter(item => item !== "all");
    };

    return {
      package_name: selectedPackage,
      fromDate: fromDate,
      toDate: toDate,
      brand: getFilterArray(selectedBrand),
      priority: getFilterArray(selectedpriority),
      country: getFilterArray(selectedcountry)
    };
  }, [fromDate, toDate, selectedBrand, selectedpriority, selectedcountry, selectedPackage]);

  // Separate query params for incident report
  const incidentReportQueryParams = useMemo(() => {
    return {
      ...queryParams,
      frequency: selectedFrequencyIncidents.toLowerCase()
    };
  }, [queryParams, selectedFrequencyIncidents]);

  const { data: incidentStatsData, isLoading: incidentStatsLoading, isFetching: incidentStatsFetching } = useQuery<IncidentStatsResponse>(
    ['incidentStatsDashboard', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.INCIDENTS_STATS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
      gcTime: 0,
      keepPreviousData: false,
      retry: 1,
      enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
    }
  );

  // Update category data query
  const categoryQueryParams = useMemo(() => ({
    ...queryParams,
    // channel: selectedChannel && selectedChannel !== "all" ? selectedChannel : undefined,
  }), [queryParams]);

  const { data: incidentCategoryData, isLoading: incidentCategoryLoading } = useQuery<CategoryDataResponse>({
    queryKey: ['categoryData', categoryQueryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.INCIDENCE_PERCENTAGE,
        categoryQueryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
  });

  // Update platform wise data query - requires channel as mandatory field
  const { data: platformWiseResponse, isLoading: isPlatformWiseLoading } = useQuery<PlatformWiseResponse>(
    ['platformWiseData', selectedChannel, queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      // Platform Distribution requires channel as mandatory field
      const payload = {
        ...queryParams,
        channel: selectedChannel || "all" // Always include channel, pass "all" if no channel is selected
      };

      console.log('Platform-wise API payload:', payload);

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_SUB_CHANNEL,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
      cacheTime: 0,
      keepPreviousData: false,
      retry: 1,
      enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
    }
  );

  // Transform platform wise data for display
  const transformedPlatformWiseData = useMemo<TransformedPlatformWiseData[]>(() => {
    console.log('Platform Wise Response in transform:', platformWiseResponse);
    console.log('Selected Channel:', selectedChannel);
    if (!platformWiseResponse?.data || platformWiseResponse.data.length === 0) {
      console.log('No platform wise data available');
      return [];
    }

    // Platform icon mapping (colors will come from API)
    const platformConfig: { [key: string]: { icon: any } } = {
      // Social Media Platforms
      "Facebook": { icon: FaFacebook },
      "Instagram": { icon: FaInstagram },
      "LinkedIn": { icon: FaLinkedin },
      "X": { icon: FaTwitter },
      "Threads": { icon: FaInstagram },
      "Sharechat": { icon: FaFacebook },
      "Twitter": { icon: FaTwitter },
      "YouTube": { icon: FaYoutube },
      "Pinterest": { icon: FaTwitter },

      // Search Engines
      "Google": { icon: Search },
      "Bing": { icon: Search },
      "Yahoo": { icon: Search },
      "DuckDuckGo": { icon: Search },

      // Blogging Platforms
      "Blogger": { icon: Newspaper },
      "WordPress": { icon: Newspaper },
      "Medium": { icon: Newspaper },
      "Tumblr": { icon: Newspaper },

      // Default for any other platform
      "default": { icon: Globe }
    };

    // Map channels to their corresponding platforms
    const channelToSubChannels: { [key: string]: string[] } = {
      "Social Media Platforms": ["Facebook", "Instagram", "LinkedIn", "X", "Threads", "Sharechat", "Pinterest"],
      "Messaging Channel Platforms": ["WhatsApp", "Telegram", "Signal"],
      "YouTube": ["YouTube"],
      "Bloggingsites": ["Blogger", "WordPress", "Medium", "Tumblr"],
      "Rogue Apps": ["Android Apps", "iOS Apps"],
      "Organic Search": ["Google", "Bing", "Yahoo", "DuckDuckGo"]
    };

    // Filter data based on selected channel if one is selected
    let dataToTransform = platformWiseResponse.data;
    if (selectedChannel && channelToSubChannels[selectedChannel]) {
      dataToTransform = platformWiseResponse.data;
      console.log('Filtered data for channel:', selectedChannel, dataToTransform);
    }

    // Transform the filtered/unfiltered data
    const transformed: TransformedPlatformWiseData[] = dataToTransform.map((item) => {
      // Use icon from API if available, otherwise fallback to hardcoded icon
      const platform = platformConfig[item.sub_channel] || platformConfig.default;
      
      // Debug logging to see what colors are being used
      console.log(`Platform: ${item.sub_channel}, API Color: ${item.color}`);
      
      return {
        label: item.sub_channel,
        visit: item.count,
        fill: item.color || "#540094", // Use API color only, fallback to default if not provided
        handles: [{
          handle: item.sub_channel,
          platform: item.sub_channel,
          influenceScore: item.percentage,
          icon: item.icon || platform.icon // Use API icon if present, else fallback
        }]
      };
    });

    // Sort by count in descending order
    return transformed.sort((a, b) => b.visit - a.visit);
  }, [platformWiseResponse, selectedChannel]);

  // Transform platform data for ProgressBar component
  const transformedPlatformDataForProgressBar = useMemo(() => {
    if (!transformedPlatformWiseData || transformedPlatformWiseData.length === 0) {
      console.log('No platform data available for ProgressBar');
      return [];
    }

    const transformed = transformedPlatformWiseData.map((item) => ({
      label: item.label,
      visit: item.visit,
      percentage: `${item.handles[0].influenceScore}%`, // Pass raw percentage without rounding
      fill: item.fill // This will now use the API color, platform color, or default color
    }));

    console.log('Transformed platform data for ProgressBar:', transformed);
    return transformed;
  }, [transformedPlatformWiseData]);

  // Add React Query hook for case category data - requires both channel and platform as mandatory fields
  const caseCategoryQueryParams = useMemo(() => {
    const params: any = { ...queryParams };

    // Categories requires both channel and platform as mandatory fields
    // Allowed combinations:
    // 1. channel = all, platform = all
    // 2. channel = <value>, platform = all  
    // 3. channel = all, platform = <value>
    // 4. channel = <value>, platform = <value>

    // Always include channel (mandatory)
    params.channel = selectedChannel || "all";
    
    // Always include platform (mandatory)
    params.platform = selectedPlatform || "all";

    console.log('Categories API payload:', params);
    return params;
  }, [queryParams, selectedChannel, selectedPlatform]);

  // Add effect to refetch category data when platform or channel selection changes
  useEffect(() => {
    if (selectedPlatform || selectedChannel) {
      // Refetch category data when platform or channel is selected
      console.log('Platform selected:', selectedPlatform, 'Channel selected:', selectedChannel);
    }
  }, [selectedPlatform, selectedChannel]);

  const { data: caseCategoryResponse, isLoading: isCaseCategoryLoading } = useQuery<CaseCategoryResponse>({
    queryKey: ['caseCategory', caseCategoryQueryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_CATEGORY,
        caseCategoryQueryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
  });

  // Transform case category data
  const transformedCaseCategoryData = useMemo(() => {
    if (!caseCategoryResponse?.data) return [] as CaseCategoryData[];

    return caseCategoryResponse.data.map(item => ({
      date: item.category,
      Today: item.count
    })) as CaseCategoryData[];
  }, [caseCategoryResponse]);

  const CustomXAxisTick = ({ x, y, payload }: any) => {
    const fullText = payload.value;
    const truncatedText = fullText.length > 10 ? fullText.substring(0, 10) + '...' : fullText;

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          dy={16}
          textAnchor="middle"
          fill="#666"
          fontSize="10px"
          fontFamily="Inter, system-ui, sans-serif"
        >
          <title>{fullText}</title>
          {truncatedText}
        </text>
      </g>
    );
  };

  const xAxisConfigCaseCategory = {
    dataKey: "date",
    tick: CustomXAxisTick,
    style: {
      fontSize: '10px',
      fontFamily: 'Inter, system-ui, sans-serif'
    },
    interval: 0,
    textAnchor: 'middle',
    height: 60
  };

  const yAxisConfigCaseCategory = {
    style: {
      fontSize: '8px',
      fontFamily: 'Inter, system-ui, sans-serif'
    }
  };

  const analyticsCardsData: AnalyticsCardData[] = [
    {
      id: 'platform-trend',
      title: 'Platform Distribution',
      type: 'progressbar',
      data: transformedPlatformDataForProgressBar,
    },
    {
      id: 'case-category',
      title: 'Categories',
      type: 'line',
      data: transformedCaseCategoryData,
      config: {
        ...caseCategoryConfig,
        xAxis: xAxisConfigCaseCategory,
        yAxis: yAxisConfigCaseCategory
      },
      isLoading: isCaseCategoryLoading
    }
  ];

  // Debug effect for ProgressBar data
  useEffect(() => {
    console.log('analyticsCardsData:', analyticsCardsData);
    analyticsCardsData.forEach((card, index) => {
      if (card.type === 'progressbar') {
        console.log(`ProgressBar card ${index} data:`, card.data);
      }
    });
  }, [analyticsCardsData]);

  const handleExpand = useCallback((index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  }, [expandedCard]);

  const handleIncidentsExport = useCallback(async () => {
    if (!incidentsChartRef.current) return
    const screenshot = await domToImage.toPng(incidentsChartRef.current)
    downloadURI(screenshot, "Incidents by Volume.png")
  }, [])

  const handleIncidentsExpand = useCallback(() => {
    if (!incidentsChartRef.current) return;
    onExpand(8, cardRefs, expandedCard, setExpandedCard); // Using index 8 for the incidents chart
  }, [expandedCard]);

  const handleChannelsExpand = useCallback(() => {
    onExpand(0, cardRefs, expandedCard, setExpandedCard); // Using index 0 for the channels donut chart
  }, [expandedCard]);

  const handleContactsNumExpand = useCallback(() => {
    onExpand(10, cardRefs, expandedCard, setExpandedCard); // Using index 10 for the contact numbers chart
  }, [expandedCard]);
  
  // Add the query hook for incident report data
  const validateDateRange = (fromDate: string, toDate: string) => {
    const start = new Date(fromDate);
    const end = new Date(toDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // If date range is more than 90 days, adjust it
    if (diffDays > 90) {
      const newFromDate = new Date(end);
      newFromDate.setDate(end.getDate() - 90);
      return {
        fromDate: format(newFromDate, 'yyyy-MM-dd'),
        toDate
      };
    }

    return { fromDate, toDate };
  };

  const { data: incidentReportData, isLoading: isIncidentReportLoading, refetch: refetchIncidentReport } = useQuery<IncidentReportResponse>({
    queryKey: ['incidentReport', fromDate, toDate, selectedFrequencyIncidents, selectedPackage, selectedBrand, selectedpriority, selectedcountry],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const validatedDates = validateDateRange(fromDate, toDate);

      try {
        console.log('Fetching data with frequency:', selectedFrequencyIncidents);
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.INCIDENT_REPORT,
          {
            package_name: selectedPackage,
            fromDate: validatedDates.fromDate,
            toDate: validatedDates.toDate,
            frequency: selectedFrequencyIncidents.toLowerCase(),
            brand: queryParams.brand,
            priority: queryParams.priority,
            country: queryParams.country
          },
          {
            headers: {
              Authorization: idToken,
              'Content-Type': 'application/json'
            } as AxiosRequestConfig['headers'],
            timeout: 30000
          }
        );
        console.log('Incident Report Response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Incident Report API Error:', error);
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
  });

  // Effect to handle loading state
  useEffect(() => {
    if (isIncidentReportLoading) {
      setIsLoading(true);
    } else {
      setIsLoading(false);
    }
  }, [isIncidentReportLoading]);

  // Transform incident report data with frequency consideration
  const transformedIncidentData = useMemo<TrafficTrendData[]>(() => {
    if (!incidentReportData?.data) return [];

    // For Monthly and Weekly, use the API response data directly without client-side calculations
    if (selectedFrequencyIncidents.toLowerCase() === 'monthly' || selectedFrequencyIncidents.toLowerCase() === 'weekly') {
      return incidentReportData.data.map((item) => ({
        date: item.inserted_date, // Use the exact date from API response
        Active: Number(item.active),
        "In Progress": Number(item.in_progress),
        Closed: Number(item.closed),
        trend: Number(item.total)
      }));
    }

    // For other frequencies (Daily, Yearly), keep the existing grouping logic
    const groupedData = incidentReportData.data.reduce((acc: { [key: string]: any }, item) => {
      const date = new Date(item.inserted_date);
      let groupKey = '';

      switch (selectedFrequencyIncidents.toLowerCase()) {
        case 'weekly':
          // Calculate which week of the month this date falls into
          const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
          const firstWeekday = firstDayOfMonth.getDay(); // 0 = Sunday, 1 = Monday, etc.
          const dayOfMonth = date.getDate();
          const weekInMonth = Math.ceil((dayOfMonth + firstWeekday) / 7);
          groupKey = `Week ${Math.min(weekInMonth, 4)}`; // Ensure we don't go beyond Week 4
          break;
        case 'monthly':
          groupKey = format(date, 'yyyy-MM');
          break;
        default:
          groupKey = item.inserted_date;
      }

      if (!acc[groupKey]) {
        acc[groupKey] = {
          date: groupKey,
          Active: 0,
          "In Progress": 0,
          Closed: 0,
          trend: 0,
          firstDate: date
        };
      }

      // Map the data according to the new API response format
      acc[groupKey].Active = (acc[groupKey].Active || 0) + Number(item.active);
      acc[groupKey]["In Progress"] = (acc[groupKey]["In Progress"] || 0) + Number(item.in_progress);
      acc[groupKey].Closed = (acc[groupKey].Closed || 0) + Number(item.closed);

      // Use the total field from the API response for the trend line
      acc[groupKey].trend = (acc[groupKey].trend || 0) + Number(item.total);

      return acc;
    }, {});

    // Convert grouped data to array and sort by date
    const sortedData = Object.values(groupedData)
      .sort((a: any, b: any) => {
        if (selectedFrequencyIncidents.toLowerCase() === 'weekly') {
          // Sort by week number
          const weekA = parseInt(a.date.split(' ')[1]);
          const weekB = parseInt(b.date.split(' ')[1]);
          return weekA - weekB;
        }
        return a.firstDate.getTime() - b.firstDate.getTime();
      });

    return sortedData.map((item: any) => {
      const date = new Date(item.firstDate);
      let displayDate = '';

      switch (selectedFrequencyIncidents.toLowerCase()) {
        case 'weekly':
          displayDate = item.date; // Already formatted as "Week X"
          break;
        case 'monthly':
          displayDate = format(date, 'MMM');
          break;
        default:
          displayDate = format(date, 'yyyy-MM-dd');
      }

      return {
        date: displayDate,
        Active: item.Active,
        "In Progress": item["In Progress"],
        Closed: item.Closed,
        trend: item.trend // Use the actual total value, not normalized percentage
      };
    });
  }, [incidentReportData, selectedFrequencyIncidents]);

  const handleIncidentsDownload = useCallback(() => {
    if (!transformedIncidentData) return;

    const csvContent = "data:text/csv;charset=utf-8,"
      + "Date,Active,In Progress,Closed\n"
      + transformedIncidentData.map(row =>
        `${row.date},${row.Active},${row["In Progress"]},${row.Closed}`
      ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "incidents_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [transformedIncidentData]);

  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return
      const ref = cardRefs.current[index]

      if (!ref) return
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref)
          downloadURI(screenshot, title + ".png")
          break
        case "csv":
          // Handle CSV exports based on chart title
          switch (title) {
            case "Channels":
              await handleChannelsCSVExport()
              break
            case "Platform":
              await handlePlatformCSVExport()
              break
            case "Categories":
              await handleCategoriesCSVExport()
              break
            case "Contact Numbers":
              await handleContactNumbersCSVExport()
              break
            case "Top Handles":
              await handleTopHandlesCSVExport()
              break
            default:
              console.warn(`CSV export not implemented for chart: ${title}`)
          }
          break
        default:
      }
    },
    [cardRefs],
  )


  const [showContactDonut, setShowContactDonut] = useState<boolean>(false);
  const [showHandlesDonut, setShowHandlesDonut] = useState<boolean>(false);
  const [filteredContactNumbers, setFilteredContactNumbers] = useState(contactNumbersDonutData);
  const [filteredTopHandles, setFilteredTopHandles] = useState(topHandlesDonutData);
  const [selectedContactBar, setSelectedContactBar] = useState<string | null>(null);
  const [selectedHandleBar, setSelectedHandleBar] = useState<string | null>(null);

  const handleContactClick = (data: any) => {
    setShowContactDonut(true);
    if (selectedContactBar === data.label) {
      // If clicking the same bar, reset to show all data
      setFilteredContactNumbers(contactNumbersDonutData);
      setSelectedContactBar(null);
    } else {
      // Filter data based on the clicked bar
      const filtered = contactNumbersDonutData.filter(item =>
        item.value <= data.value
      );
      setFilteredContactNumbers(filtered);
      setSelectedContactBar(data.label);
    }
  };

  const handleTopHandlesClick = (data: any) => {
    setShowHandlesDonut(true);
    if (selectedHandleBar === data.label) {
      // If clicking the same bar, reset to show all data
      setFilteredTopHandles(topHandlesDonutData);
      setSelectedHandleBar(null);
    } else {
      // Filter data based on the clicked bar
      const filtered = topHandlesDonutData.filter(item =>
        item.value <= data.value
      );
      setFilteredTopHandles(filtered);
      setSelectedHandleBar(data.label);
    }
  };

  const handleChannelSelect = (channel: ChannelName) => {
    console.log('Channel selected:', channel);
    setSelectedChannel(prev => prev === channel ? null : channel);
    
    // Reset platform selection when channel changes to ensure proper data flow
    // Platform Distribution depends on channel, so we need to reset platform
    setSelectedPlatform(null);
  };

  const handleFrequencyChangeV = (value: string) => {
    setSelectedFrequencyV(value);
    // Here you would typically fetch new data based on the selected frequency
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  };

  // Transform the data after it's fetched
  const transformedStats = incidentStatsData ? transformStatsData(incidentStatsData) : undefined;

  // Add React Query hook for Category data
  const { data: categoryDataResponse, isLoading: isCategoryLoading } = useQuery<CategoryDataResponse>({
    queryKey: ['categoryData', categoryQueryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.INCIDENCE_PERCENTAGE,
        categoryQueryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
  });

  // Transform the category data
  const transformedCategoryData = categoryDataResponse ? transformCategoryData(categoryDataResponse) : undefined;

  // Add React Query hook for Channel data
  const { data: channelDataResponse, isLoading: isChannelLoading } = useQuery<ChannelDataResponse>({
    queryKey: ['channelData', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      try {
        console.log('Channel API Request Payload:', queryParams);

        const response = await axios.post<ChannelDataResponse>(
          process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_CHANNEL,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              'Content-Type': 'application/json'
            } as AxiosRequestConfig['headers'],
            timeout: 30000
          }
        );

        console.log('Raw Channel API Response:', response.data);
        return response.data;
      } catch (error) {
        if (axios.isAxiosError(error)) {
          console.error('Channel API Error:', error);
        }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate // Only fetch when package and dates are available
  });

  // Transform the channel data with proper type checking
  const transformedChannelData = useMemo<TransformedChannelData[]>(() => {
    console.log('Channel Data Response in transform:', channelDataResponse);
    if (!channelDataResponse?.data || channelDataResponse.data.length === 0) {
      console.log('No channel data available');
      // Return empty array to show "No Data Found !" message
      return [];
    }

    // Color mapping for each channel
    const channelColors: { [key: string]: string } = {
      "Messaging Channel Platforms": "#008000",
      "Social Media Platforms": "#8B5CF6",
      "Organic Search": "#499167",
      "YouTube": "#ccccb3",
      "Bloggingsites": "#CC00FF",
      "Rogue Apps": "#82ca9d"
    };

    const transformed: TransformedChannelData[] = channelDataResponse.data.map((item) => ({
      label: item.channel,
      visit: item.percentage, // Use percentage instead of count
      value: item.percentage, // Add value field for legend percentage display
      fill: channelColors[item.channel] || "#808080" // Default gray color if channel not found in mapping
    }));

    console.log('Transformed Channel Data:', transformed);
    return transformed;
  }, [channelDataResponse]);

  // Calculate total count for Channel donut chart
  const channelTotalCount = useMemo(() => {
    if (!channelDataResponse?.total_count) {
      // If total_count is not available from API, calculate from individual counts
      return channelDataResponse?.data?.reduce((sum: number, item: any) => sum + item.count, 0) || 0;
    }
    return channelDataResponse.total_count;
  }, [channelDataResponse]);

  // Format channel total count to show in K format
  const formattedChannelTotalCount = useMemo(() => {
    const count = channelTotalCount;
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  }, [channelTotalCount]);

  // Helper function to handle multiselect
  const handleMultiSelect = (
    value: string,
    currentSelection: string[],
    setSelection: React.Dispatch<React.SetStateAction<string[]>>,
    options: string[]
  ) => {
    if (value === "all") {
      setSelection(["all"]);
      return;
    }

    const newSelection = currentSelection.filter(item => item !== "all");

    if (currentSelection.includes(value)) {
      const updatedSelection = newSelection.filter(item => item !== value);
      setSelection(updatedSelection.length === 0 ? ["all"] : updatedSelection);
    } else {
      setSelection([...newSelection, value]);
    }
  };

  // Add React Query hook for contact numbers data
  const { data: contactNumbersResponse, isLoading: isContactNumbersLoading } = useQuery<ContactNumberResponse>({
    queryKey: ['contactNumbers', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post<ContactNumberResponse>(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_PUBLISHER,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );

      console.log('Contact Numbers API Response:', response.data);
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
  });

  // Transform contact numbers data for the chart
  const transformedContactNumbersData = useMemo<ChartData[]>(() => {
    if (!contactNumbersResponse?.data) return [];

    return contactNumbersResponse.data
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(item => ({
        label: item.publisher,
        value: item.count
      }));
  }, [contactNumbersResponse]);

  // Add query for top handles data
  const { data: topHandlesResponse, isLoading: isTopHandlesLoading } = useQuery<TopHandlesResponse>({
    queryKey: ['topHandles', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No token found');

      const response = await axios.post<TopHandlesResponse>(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.TOP_HANDLES,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!queryParams.fromDate && !!queryParams.toDate
  });

  // Transform top handles data
  const transformedTopHandlesData = useMemo<ChartData[]>(() => {
    if (!topHandlesResponse?.data) return [];

    return topHandlesResponse.data
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(item => ({
        label: item.handles,
        value: item.count
      }));
  }, [topHandlesResponse]);


  // Add handler for date range changes
  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      const { minDate, maxDate } = getMinMaxDates();
      // Only update if the selected range is within allowed dates
      if (range.from >= minDate && range.to <= maxDate) {
        setDueDateRange(range);
      }
    }
  };

  // Reset filter selections when package or date changes
  useEffect(() => {
    // Reset all filter selections to "all" when package or date changes
    setSelectedBrand(["all"]);
    setSelectedpriority(["all"]);
    setSelectedcountry(["all"]);
  }, [selectedPackage, fromDate, toDate]);

  // Add queries for filter options
  const { data: brandFilterData } = useQuery<FilterResponse>({
    queryKey: ['brandFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'brand'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu: "dashboard_summary"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false
  });

  // Update priority filter query
  const { data: priorityFilterData } = useQuery<FilterResponse>({
    queryKey: ['priorityFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'priority'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu: "dashboard_summary"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false // No need to retry for static data
  });

  // Update country filter query
  const { data: countryFilterData } = useQuery<FilterResponse>({
    queryKey: ['countryFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'country'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu: "dashboard_summary"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false // No need to retry for static data
  });

  // Update filter state initialization with proper types
  const [filterState, setFilterState] = useState<FilterStateMap>({
    brand: {
      filters: brandFilterData?.data.map(label => ({
        label,
        checked: true
      })) || [],
      is_select_all: true,
      selected_count: brandFilterData?.data?.length || 0
    },
    priority: {
      filters: priorityFilterData?.data.map(label => ({
        label,
        checked: true
      })) || [],
      is_select_all: true,
      selected_count: priorityFilterData?.data?.length || 0
    },
    country: {
      filters: countryFilterData?.data.map(label => ({
        label,
        checked: true
      })) || [],
      is_select_all: true,
      selected_count: countryFilterData?.data?.length || 0
    }
  });

  // Handle filter changes
  const handleFilterSubmit = (id: string, data: any) => {
    setFilterState(prev => ({
      ...prev,
      [id]: data
    }));

    // Update the corresponding state based on the filter ID
    switch (id) {
      case 'brand':
        setSelectedBrand(data.is_select_all ? ["all"] : data.filters.filter((f: any) => f.checked).map((f: any) => f.label));
        break;
      case 'priority':
        setSelectedpriority(data.is_select_all ? ["all"] : data.filters.filter((f: any) => f.checked).map((f: any) => f.label));
        break;
      case 'country':
        setSelectedcountry(data.is_select_all ? ["all"] : data.filters.filter((f: any) => f.checked).map((f: any) => f.label));
        break;
    }
  };

  // Add search handlers for filters
  const handleBrandSearch = async (query: string) => {
    // If you have an API endpoint for searching brand, you can use it here
    // For now, just filter the existing data
    if (!query) return;
    setFilterState(prev => ({
      ...prev,
      brand: {
        ...prev.brand,
        filters: prev.brand.filters.filter(f =>
          f.label.toLowerCase().includes(query.toLowerCase())
        )
      }
    }));
  };

  const handleCountrySearch = async (query: string) => {
    // If you have an API endpoint for searching country, you can use it here
    // For now, just filter the existing data
    if (!query) return;
    setFilterState(prev => ({
      ...prev,
      country: {
        ...prev.country,
        filters: prev.country.filters.filter(f =>
          f.label.toLowerCase().includes(query.toLowerCase())
        )
      }
    }));
  };

  // Add proper type for the filter data
  interface FilterData {
    data: string[];
    status: boolean;
  }

  // Add memoized filter data with proper typing and null checks
  const brandFilters = useMemo(() =>
    (brandFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedBrand.includes(label) || selectedBrand.includes("all")
    })),
    [brandFilterData?.data, selectedBrand]
  );

  const priorityFilters = useMemo(() =>
    (priorityFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedpriority.includes(label) || selectedpriority.includes("all")
    })),
    [priorityFilterData?.data, selectedpriority]
  );

  const countryFilters = useMemo(() =>
    (countryFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedcountry.includes(label) || selectedcountry.includes("all")
    })),
    [countryFilterData?.data, selectedcountry]
  );

  const xAxisConfigstack = {
    dataKey: "date",
    tickFormatter: (value: string) => {
      if (!value) return '';
      try {
        const date = new Date(value);
        return format(date, 'dd/MMM');
      } catch {
        return value;
      }
    }
  };

  const router = useRouter();

  // Handle Website/App card click
  const handleWebsiteCardClick = () => {
    router.push("website/summary");
  };

  const handleSocialMediaCardClick = () => {
    router.push("socialmedia/summary");
  };


  const frequencyOptions = ["Daily", "Weekly", "Monthly"];

  const handleIncidentsFrequencyChange = async (value: string) => {
    console.log('Changing frequency to:', value);
    setIsLoading(true); // Set loading state immediately
    setSelectedFrequencyIncidents(value);
    try {
      await refetchIncidentReport(); // Wait for data to refetch
    } finally {
      setTimeout(() => setIsLoading(false), 500); // Ensure minimum loader visibility
    }
  };

  const handleChannelRefresh = () => {
    setSelectedChannel(null);
  };

  // Add state for selected publisher for donut
  const [selectedPublisherForDonut, setSelectedPublisherForDonut] = useState<string>("");

  // Add query for publisher insight donut data
  const { data: publisherInsightData, isLoading: isPublisherInsightLoading } = useQuery(
    ['publisherInsight', selectedPublisherForDonut, queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.PUBLISHER_INSIGHT,
        {
          ...queryParams,
          publisher: selectedPublisherForDonut
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage && !!showContactDonut && !!selectedPublisherForDonut && !!queryParams.fromDate && !!queryParams.toDate,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  // Transform publisher insight data for donut chart
  function transformPublisherInsightData(data: any): any[] {
    if (!data || !data.data) return [];
    // Define a color palette for different segments
    const colors = ['#540094', '#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE', '#EDE9FE', '#F3E8FF', '#FAF5FF'];
    
    return data.data.map((item: any, index: number) => ({
      label: item.sub_channel_name || '',
      number: item.sub_channel_name || '',
      value: item.percentage || 0, // Use percentage instead of count
      percentage: item.percentage || 0,
      fill: colors[index % colors.length], // Use different colors for each segment
      color: colors[index % colors.length]
    }));
  }

  // Add state for selected handle for donut
  const [selectedHandleForDonut, setSelectedHandleForDonut] = useState<string>("");

  // Add query for handle insight donut data
  const { data: handleInsightData, isLoading: isHandleInsightLoading } = useQuery(
    ['handleInsight', selectedHandleForDonut, queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.HANDLE_INSIGHT,
        {
          ...queryParams,
          handle: selectedHandleForDonut
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage && !!showHandlesDonut && !!selectedHandleForDonut && !!queryParams.fromDate && !!queryParams.toDate,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  // Transform handle insight data for donut chart
  function transformHandleInsightData(data: any): any[] {
    if (!data || !data.data) return [];
    // Define a color palette for different segments
    const colors = ['#540094', '#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE', '#EDE9FE', '#F3E8FF', '#FAF5FF'];
    
    return data.data.map((item: any, index: number) => ({
      label: item.sub_channel_name || '',
      number: item.sub_channel_name || '',
      value: item.percentage || 0, // Use percentage instead of count
      percentage: item.percentage || 0,
      fill: colors[index % colors.length], 
      color: colors[index % colors.length]
    }));
  }

  // Calculate total count for Publisher Insights donut chart
  const publisherInsightTotalCount = useMemo(() => {
    return publisherInsightData?.total_count || 0;
  }, [publisherInsightData]);

  // Calculate total count for Handle Insights donut chart
  const handleInsightTotalCount = useMemo(() => {
    return handleInsightData?.total_count || 0;
  }, [handleInsightData]);

  // Add effect to handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && !(document as any).webkitFullscreenElement && expandedCard !== null) {
        // User exited fullscreen, reset the expanded card state
        setExpandedCard(null);
      }
    };

    // Add listeners for different browser implementations
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [expandedCard]);

  // Add CSV export functions for each chart
  const handleIncidentsCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.INCIDENT_REPORT,
        {
          ...incidentReportQueryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );

      await handleCSVDownloadFromResponse(response, 'incidents_by_volume.csv');

    } catch (error) {
      console.error('Error exporting incidents CSV:', error);
    }
  }, [incidentReportQueryParams]);

  const handleChannelsCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_CHANNEL,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );

      await handleCSVDownloadFromResponse(response, 'channels_distribution.csv');
    } catch (error) {
      console.error('Error exporting channels CSV:', error);
    }
  }, [queryParams]);

  const handlePlatformCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_SUB_CHANNEL,
        {
          ...queryParams,
          channel: selectedChannel || "all",
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      await handleCSVDownloadFromResponse(response, 'platform_distribution.csv');

    } catch (error) {
      console.error('Error exporting platform CSV:', error);
    }
  }, [queryParams, selectedChannel]);

  const handleCategoriesCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_CATEGORY,
        {
          ...caseCategoryQueryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          
        }
      );

      await handleCSVDownloadFromResponse(response, 'categories_trend.csv');

    } catch (error) {
      console.error('Error exporting categories CSV:', error);
    }
  }, [caseCategoryQueryParams]);

  const handleContactNumbersCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.CASE_PUBLISHER,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
         
        }
      );

      await handleCSVDownloadFromResponse(response, 'contact_numbers.csv');

    } catch (error) {
      console.error('Error exporting contact numbers CSV:', error);
    }
  }, [queryParams]);

  const handleTopHandlesCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.TOP_HANDLES,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          
        }
      );

      await handleCSVDownloadFromResponse(response, 'top_handles.csv');
      
    } catch (error) {
      console.error('Error exporting top handles CSV:', error);
    }
  }, [queryParams]);

  return (
    <div className="min-h-screen w-full dark:bg-gray-800 bg-[#F3F4F6]">
      {/* Filter Bar - Made Sticky */}
      <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)] border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap gap-3">
          <FilterPill
            id="brand"
            title="Brand"
            filters={brandFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleBrandSearch}
            loading={!brandFilterData}
            isSearchable={true}
          />
          <FilterPill
            id="priority"
            title="Priority"
            filters={priorityFilters}
            onSubmit={handleFilterSubmit}
            onSearch={async () => { }}
            loading={!priorityFilterData}
          />
          <FilterPill
            id="country"
            title="Country"
            filters={countryFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleCountrySearch}
            loading={!countryFilterData}
            isSearchable={true}
          />
        </div>
      </div>

      {/* Top Row: Stats Cards and Incidents Volume Chart */}
      <div className="m-2">
        <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-2">
          {/* Stats Cards */}
          <div className="w-full shadow-md">
            <StatsCards cardData={transformStatsData(incidentStatsData)} isLoading={incidentStatsLoading} isFetching={incidentStatsFetching} showWhileFetching={true} />
          </div>

          {/* Incidents by Volume Chart */}
          <div className="w-full">
            <Card
              ref={(el) => {
                if (el) cardRefs.current[8] = el;
              }}
              className="w-full shadow-md rounded-lg bg-white dark:bg-card h-[300px]"
            >
              <CardTitle className="p-2 text-base font-semibold dark:text-white">
                <HeaderRow
                  selectoptions={frequencyOptions}
                  handleFrequencyChange={handleIncidentsFrequencyChange}
                  selectedFrequency={selectedFrequencyIncidents}
                  title="Incidents by Volume"
                  onExport={() => onExport("png", "Incidents by Volume", 8)}
                  handleExport={handleIncidentsCSVExport}
                  onExpand={() => handleExpand(8)}
                  isRadioButton={false}
                  isSelect={true}
                  placeholder="Daily" 
                  titleFontSize="text-base"
                  isExpanded={expandedCard === 8}
                />
              </CardTitle>
              <CardContent className="p-2">
                <div className="w-full h-[200px]">
                  {isLoading || isIncidentReportLoading ? (
                    <div className="flex items-center justify-center w-full h-full min-h-[220px] dark:bg-gray-800">
                      <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                    </div>
                  ) : !transformedIncidentData || transformedIncidentData.length === 0 ? (
                    <div className="flex items-center justify-center h-full w-full">
                      <span className="text-sm">No Data Found !</span>
                    </div>
                  ) : (
                    <StackedBarWithLine
                      chartData={transformedIncidentData as any}
                      chartConfig={incidentsChartConfig}
                      showTrendline={true}
                      trendlineKey="trend"
                      isHorizontal={false}
                      isLegend={true}
                      barRadius={5}
                      chartHeight={200}
                      isLoading={isLoading || isIncidentReportLoading}
                      stickyLegend={true}
                      enableHorizontalScroll={true}
                      isExpanded={expandedCard === 8}
                      xAxisConfig={{
                        dataKey: "date",
                        angle: 0,
                        textAnchor: "middle",
                        dy: 5,
                        dx: 0,
                        tickMargin: 5,
                        height: selectedFrequencyIncidents.toLowerCase() === 'weekly' ? 45 : 35,
                        tickLine: false,
                        axisLine: true
                      }}
                      YAxis1={{
                        yAxisId: "left",
                        orientation: "left",
                        tickFormatter: (value: string) => formatNumber(Number(value))
                      }}
                      YAxis2={{
                        yAxisId: "right",
                        orientation: "right",
                        tickFormatter: (value: string) => formatNumber(Number(value)),
                        hide: true
                      }}
                      onExpand={() => handleExpand(8)}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Second Row: Category Cards */}
      <div className="m-2">
        <div className="w-full shadow-md">
          <div className="bg-white rounded-lg p-2 dark:bg-gray-900">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {incidentCategoryLoading ? (
                <div className="col-span-full flex items-center justify-center w-full h-full min-h-[150px] shadow-md">
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : transformedCategoryData && transformedCategoryData.map((cat, idx) => (
                <CustomCategoryCard
                  key={idx}
                  title={cat.title}
                  count={cat.count}
                  percentage={cat.percentage}
                  bgColor={cat.bgColor}
                  link={cat.link}
                  isLoading={false}
                  onClick={
                    cat.title === "Website/App"
                      ? handleWebsiteCardClick
                      : cat.title === "Social Media"
                        ? handleSocialMediaCardClick
                        : undefined
                  }
                  className={
                    cat.title === "Website/App" || cat.title === "Social Media"
                      ? "cursor-pointer hover:shadow-lg transition-shadow"
                      : ""
                  }
                />
              ))}
              {!incidentCategoryLoading && (!transformedCategoryData || transformedCategoryData.length === 0) && (
                <div className="col-span-full flex items-center justify-center h-[150px]">
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="m-2">
        {/* Top Row: Donut, Platform Trend, Line Chart */}
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-4 gap-2 dark:bg-gray-800">
          <Card
            ref={(el) => {
              cardRefs.current[9] = el!;
            }}
            className="w-full shadow-md rounded-none bg-white dark:bg-gray-900 gap-2 dark:text-white text-header rounded-lg "
          >
            <CardContent className="w-full p-2">
              <div className="w-full h-[280px] sm:h-[300px] md:h-[310px] lg:h-[310px] overflow-hidden [&_.text-base.items-start.font-semibold]:pl-4 [&_.text-sm.items-start.font-semibold]:px-4 [&_.text-sm.items-start.font-semibold]:py-2 [&_.legend]:max-h-full [&_.legend]:overflow-y-auto [&_.legend]:text-xs [&_.legend]:pl-2 [&_.recharts-responsive-container]:h-[150px] [&_.recharts-wrapper]:w-[140px] [&_.recharts-wrapper]:h-[140px] [&_.recharts-wrapper]:mx-auto [&_.recharts-wrapper]:my-2">
                                  <DonutChart
                    chartData={transformedChannelData as any[]}
                    dataKey="visit"
                    nameKey="label"
                    chartConfig={transformedChannelData.reduce(
                      (acc, cur) => ({ ...acc, [cur.label]: { label: cur.label, color: cur.fill } }),
                      {}
                    )}
                    isView={true}
                    isLoading={isChannelLoading}
                    isPercentage={true}
                    isLabelist={false}
                    direction="flex-col"
                    isdonut={expandedCard === 9}
                    marginTop="mt-0"
                    position="items-start"
                    onSegmentClick={(segment) => {
                      // Channel selection affects Platform Distribution and Categories data
                      // Platform Distribution requires channel as mandatory field
                      // Categories requires both channel and platform as mandatory fields
                      handleChannelSelect(segment.label as ChannelName);
                      setSelectedPlatform(null); // Reset platform when a channel is selected
                    }}
                    selectedSegment={selectedChannel}
                    onExpand={() => handleExpand(9)}
                    showRefresh={true}
                    title="Channels"
                    titleFontSize="text-base"
                    onRefresh={() => {
                      setSelectedChannel(null);
                      setSelectedPlatform(null);
                    }}
                    onExport={() => onExport("png", "Channels", 9)}
                    handleExport={handleChannelsCSVExport}
                    legendPosition="bottom"
                    legendFontSize={10}
                    centerValue={formattedChannelTotalCount}
                    centerLabel="Total Count"
                    innerRadius={55}
                    outerRadius={80}
                    formatterType="percentage"
                    enableHorizontalScroll={true}
                    scrollbarGap={2}
                  />
              </div>
            </CardContent>
          </Card>

          {/* Platform Distribution */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[0] = el;
            }}
            className={`w-full shadow-md rounded-none bg-white dark:bg-gray-900 gap-2 dark:text-white text-header rounded-lg ${expandedCard === 0 ? 'h-[100vh]' : ''}`}
          >
            <CardContent className="w-full p-2">
              <div className={`w-full ${expandedCard === 0 ? 'h-[calc(100vh-48px)]' : 'h-[280px] sm:h-[300px] md:h-[310px] lg:h-[310px]'} overflow-hidden`}>
                {isPlatformWiseLoading ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[280px] sm:min-h-[300px] md:min-h-[310px] lg:min-h-[310px] dark:bg-gray-900">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !transformedPlatformDataForProgressBar || transformedPlatformDataForProgressBar.length === 0 ? (
                  <div className="flex items-center justify-center h-full w-full ">
                    <span className="text-sm dark:text-white" >No Data Found !</span>
                  </div>
                ) : (
                  <div className="w-full h-full">
                    <HeaderRow
                      title="Platform Distribution"
                      onExport={() => onExport("png", "Platform Distribution", 0)}
                      handleExport={handlePlatformCSVExport}
                      onExpand={() => handleExpand(0)}
                      isRadioButton={false}
                      isSelect={false}
                      titleFontSize="text-base"
                      isExpanded={expandedCard === 0}
                    />

                    <div className={`${expandedCard === 0 ? 'h-[calc(100vh-96px)]' : 'h-[calc(100%-48px)]'} overflow-y-auto scrollbar px-4`}>
                      <div className="flex flex-col">
                        <div className={`flex flex-col ${expandedCard === 0 ? 'gap-4 sm:gap-6' : 'gap-1 sm:gap-2'} pt-2`}>
                          {transformedPlatformDataForProgressBar.map((item: any) => {
                            const currentValue = parseFloat(item.percentage.replace("%", ""));
                            const isHovered = false; // We can add hover state later if needed
                            const maxVisit = Math.max(...transformedPlatformDataForProgressBar.map((d: any) => d.visit));
                            const incidentsWidth = (item.visit / maxVisit) * 100;

                            return (
                              <TooltipProvider key={item.label}>
                                <UITooltip>
                                  <TooltipTrigger asChild>
                                    <div
                                      className={`flex flex-col gap-1 group relative cursor-pointer transition-colors duration-200 ${selectedPlatform === item.label ? "bg-violet-100 dark:bg-violet-900/20 rounded-md p-1" : "hover:bg-gray-50 dark:hover:bg-gray-700/20 rounded-md p-1"
                                        } ${expandedCard === 0 ? 'p-3' : 'p-1'}`}
                                      onClick={() => {
                                        // Platform selection affects Categories data
                                        // Categories requires both channel and platform as mandatory fields
                                        setSelectedPlatform(selectedPlatform === item.label ? null : item.label);
                                      }}
                                    >
                                {/* Icon positioned to cover title and bar */}
                                <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10">
                                  {(() => {
                                    // Get the icon from the original platform data
                                    const originalPlatformData = transformedPlatformWiseData.find(p => p.label === item.label);
                                    const icon = originalPlatformData?.handles[0]?.icon;

                                    if (icon) {
                                      if (typeof icon === "string") {
                                        return <img src={icon} alt={item.label} className={`${expandedCard === 0 ? 'w-10 h-10 sm:w-12 sm:h-12' : 'w-6 h-6 sm:w-8 sm:h-8'} object-contain`} />;
                                      } else {
                                        return React.createElement(icon, { size: expandedCard === 0 ? 32 : 24, className: `text-[#540094] ${expandedCard === 0 ? 'sm:w-12 sm:h-12' : 'sm:w-8 sm:h-8'}` });
                                      }
                                    }
                                    return null;
                                  })()}
                                </div>
                                
                                {/* Title above the bars */}
                                <div className={`${expandedCard === 0 ? 'text-sm sm:text-base' : 'text-xs sm:text-sm'} font-normal text-gray-600 dark:text-white text-left mb-0.5 pl-10 flex justify-between items-center min-w-0`}>
                                  <span className="truncate flex-1 mr-2">{item.label}</span>
                                  <span className={`${expandedCard === 0 ? 'text-sm sm:text-base' : 'text-xs'} font-medium text-gray-600 dark:text-white flex-shrink-0`}>
                                    {currentValue}%
                                  </span>
                                </div>
                                
                                {/* Progress bars container */}
                                <div className="flex items-center gap-1 pl-10">
                                  <div className="flex-1 relative min-w-0">
                                    {/* Gray background bar */}
                                    <div
                                      className={`${expandedCard === 0 ? 'h-3' : 'h-2'} bg-gray-200 rounded-sm`}
                                      style={{
                                        width: "100%",
                                      }}
                                    />
                                    {/* Incidents bar */}
                                    <div
                                      className={`${expandedCard === 0 ? 'h-3' : 'h-2'} transition-all duration-1000 ease-out shadow-md absolute top-0 left-0`}
                                      style={{
                                        width: `${incidentsWidth}%`,
                                        backgroundColor: item.fill,
                                      }}
                                    />
                                    {/* Takedown% bar */}
                                    <div
                                      className={`absolute top-0 left-0 overflow-hidden shadow-lg ${expandedCard === 0 ? 'h-3' : 'h-2'}`}
                                      style={{
                                        width: `${currentValue * (incidentsWidth / 100)}%`,
                                        background: "linear-gradient(135deg, #540094 0%, #540094 100%)",
                                        boxShadow: "0 0 15px #54009440, 0 4px 12px #54009430",
                                      }}
                                    >
                                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent" />
                                    </div>
                                  </div>
                                </div>
                              </div>
                                    </TooltipTrigger>
                                                              <TooltipContent>
                              <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                                <div className="font-medium bg-gray-100 text-black dark:text-white">
                                  {item.label}
                                </div>
                                <div className="grid gap-1.5">
                                  <div className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground">
                                    <div
                                      className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                      style={{
                                        "--color-bg": item.fill,
                                        "--color-border": item.fill,
                                      } as React.CSSProperties}
                                    />
                                    <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                      <span className="text-muted-foreground">
                                        Count
                                      </span>
                                      <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                        {item.visit.toLocaleString()}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground">
                                    <div
                                      className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                      style={{
                                        "--color-bg": "#540094",
                                        "--color-border": "#540094",
                                      } as React.CSSProperties}
                                    />
                                    <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                      <span className="text-muted-foreground">
                                        Percentage
                                      </span>
                                      <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                        {item.percentage}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TooltipContent>
                                </UITooltip>
                              </TooltipProvider>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Categories Trend Line */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[1] = el;
            }}
            className={`w-full shadow-md rounded-none bg-white dark:bg-gray-900 dark:text-white text-header rounded-lg sm:col-span-1 lg:col-span-2 ${expandedCard === 1 ? 'h-[100vh]' : ''}`}
          >
            <CardContent className="w-full p-0 col m-0">
              <div className={`w-full ${expandedCard === 1 ? 'h-[calc(100vh-48px)]' : 'h-[275px]'}`}>
                {isCaseCategoryLoading ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[280px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !transformedCaseCategoryData || transformedCaseCategoryData.length === 0 ? (
                  <div className="flex items-center justify-center h-full w-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <div className="w-full h-full">
                    <HeaderRow
                      title={selectedPlatform ? `Categories - ${selectedPlatform}` : "Categories"}
                      onExport={() => onExport("png", selectedPlatform ? `Categories - ${selectedPlatform}` : "Categories", 1)}
                      handleExport={handleCategoriesCSVExport}
                      onExpand={() => handleExpand(1)}
                      isRadioButton={false}
                      isSelect={false}
                      titleFontSize="text-base"
                      isExpanded={expandedCard === 1}
                    />
                    <div className="w-full h-full overflow-x-auto">
                      <div style={{ 
                        width: `${Math.max(transformedCaseCategoryData.length * 120, 800)}px`, 
                        height: expandedCard === 1 ? 'calc(100vh - 100px)' : '100%' 
                      }}>
                        <LineChart 
                          data={transformedCaseCategoryData} 
                          margin={{ left: 20, right: 30, top: 5, bottom: 1 }} 
                          width={Math.max(transformedCaseCategoryData.length * 120, 800)} 
                          height={expandedCard === 1 ? (window.innerHeight - 120) : 260}
                        >
                          <CartesianGrid
                            strokeDasharray="5 5"
                            stroke="#e5e7eb"
                            strokeWidth={0.5}
                            vertical={false}
                          />
                          <XAxis
                            {...xAxisConfigCaseCategory}
                            style={{ fontSize: expandedCard === 1 ? 16 : 14 }}
                          />
                          <YAxis
                            style={{ ...yAxisConfigCaseCategory?.style, fontSize: expandedCard === 1 ? 14 : 10 }}
                            domain={[0, 'dataMax + 50']}
                            tickFormatter={(value) => {
                              if (value >= 1000) {
                                return `${(value / 1000).toFixed(1)}k`;
                              }
                              return value.toString();
                            }}
                          />
                          <Tooltip
                            content={({ active, payload, label }) => {
                              if (!active || !payload?.length) return null;
                              return (
                                <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                                  <div className="font-medium bg-gray-100 text-black dark:text-white" title={label}>
                                    {label}
                                  </div>
                                  <div className="grid gap-1.5">
                                    {payload.map((item, index) => {
                                      const indicatorColor = item.payload.fill || item.color || "#540094";
                                      return (
                                        <div
                                          key={item.dataKey}
                                          className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                                        >
                                          <div
                                            className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                            style={{
                                              "--color-bg": indicatorColor,
                                              "--color-border": indicatorColor,
                                            } as React.CSSProperties}
                                          />
                                          <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                            <span className="text-muted-foreground">
                                              {item.name || "Count"}
                                            </span>
                                            <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                              {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                            </span>
                                          </div>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>
                              );
                            }}
                          />
                          <Line
                            type="monotone"
                            dataKey="Today"
                            stroke="#540094"
                            strokeWidth={expandedCard === 1 ? 3 : 2}
                            dot={{ fill: "#540094", r: expandedCard === 1 ? 6 : 4 }}
                            activeDot={{ r: expandedCard === 1 ? 8 : 6, fill: "#540094" }}
                            name=" "
                          />
                        </LineChart>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>


        {/* Contact Numbers and Top Handles Section */}
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-2 mt-2">
          {/* Contact Numbers Card */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[10] = el;
            }}
            className="w-full shadow-md rounded-none bg-white dark:bg-gray-900 gap-2 dark:text-white text-header rounded-lg"
          >
            <CardContent className="w-full min-h-[250px] sm:min-h-[280px] md:min-h-[280px] lg:min-h-[300px] xl:min-h-[320px] p-1">
              <div className="w-full h-full ">
                {isContactNumbersLoading ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[250px] sm:min-h-[280px] md:min-h-[280px] lg:min-h-[300px] xl:min-h-[320px] dark:bg-gray-800">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : (
                  <HorizontalVerticalBarChart
                    chartData={transformedContactNumbersData}
                    chartConfig={{
                      value: {
                        color: "#540094",
                        label: "Count"
                      }
                    }}
                    isHorizontal={true}
                    onExpand={() => handleExpand(10)}
                    position="right"
                    isLoading={isContactNumbersLoading}
                    formatterType="number"
                    isRadioButton={false}
                    isSelect={false}
                    dataKey="value"
                    barsize={30}
                    setheight="100%"
                    title="Top Contacts"
                    titleFontSize="text-sm sm:text-base"
                    showLegend={false}
                    margin={{ left: 38, right: 20, top: 0, bottom: 5 }}
                    onExport={() => onExport("png", "Contact Numbers", 10)}
                    handleExport={handleContactNumbersCSVExport}
                    onBarClick={(data) => {
                      setSelectedContactBar(data.label);
                      setSelectedPublisherForDonut(data.label);
                      setShowContactDonut(true);
                    }}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Top Handles Card */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[7] = el;
            }}
            className="w-full shadow-md rounded-none bg-white dark:bg-gray-900 gap-2 dark:text-white text-header rounded-lg"
          >
            <CardContent className="w-full min-h-[250px] sm:min-h-[280px] md:min-h-[280px] lg:min-h-[300px] xl:min-h-[320px] p-1">
              <div className="w-full h-full">
                {isTopHandlesLoading ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[250px] sm:min-h-[280px] md:min-h-[280px] lg:min-h-[300px] xl:min-h-[320px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : (
                  <HorizontalVerticalBarChart
                    chartData={transformedTopHandlesData}
                    chartConfig={{
                      value: {
                        color: "#540094",
                        label: "Count"
                      }
                    }}
                    isHorizontal={true}
                    onExpand={() => handleExpand(7)}
                    position="right"
                    isLoading={isTopHandlesLoading}
                    formatterType="number"
                    isRadioButton={false}
                    isSelect={false}
                    dataKey="value"
                    barsize={30}
                    setheight="100%"
                    title="Top Handles"
                    titleFontSize="text-sm sm:text-base"
                    showLegend={false}
                    margin={{ left: 38, right: 20, top: 0, bottom: 5 }}
                    onExport={() => onExport("png", "Top Handles", 7)}
                    handleExport={handleTopHandlesCSVExport}
                    onBarClick={(data) => {
                      setSelectedHandleBar(data.label);
                      setSelectedHandleForDonut(data.label);
                      setShowHandlesDonut(true);
                    }}
                  />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Numbers and Top Handles Donut Charts */}
        <div className="grid grid-cols-2 gap-1 m-1 shadow-md">
          {/* Contact Numbers Donut Chart */}
          <Dialog open={showContactDonut} onOpenChange={(open) => {
            setShowContactDonut(open);
            if (!open) setSelectedPublisherForDonut("");
          }}>
            <DialogContent className="w-full h-full min-w-[280px] sm:min-w-[320px] md:min-w-[400px] min-h-[280px] sm:min-h-[320px] md:min-h-[350px] max-w-[90vw] sm:max-w-[500px] max-h-[80vh] sm:max-h-[400px] pt-4 dark:bg-gray-900">
              <div className="w-full h-full">
                {isPublisherInsightLoading ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[280px] sm:min-h-[320px] md:min-h-[350px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !publisherInsightData || !publisherInsightData.data || publisherInsightData.data.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <DonutChart
                    chartData={publisherInsightData ? transformPublisherInsightData(publisherInsightData) : []}
                    dataKey="value"
                    nameKey="number"
                    chartConfig={(publisherInsightData && publisherInsightData.data) ? publisherInsightData.data.reduce(
                      (acc: any, cur: any, index: number) => {
                        const colors = ['#540094', '#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE', '#EDE9FE', '#F3E8FF', '#FAF5FF'];
                        return {
                          ...acc,
                          [cur.sub_channel_name || '']: {
                            label: cur.sub_channel_name || '',
                            color: colors[index % colors.length]
                          }
                        };
                      },
                      {}
                    ) : {}}
                    isView={true}
                    isLoading={isPublisherInsightLoading}
                    isPercentage={true}
                    direction="flex-col"
                    isdonut={true}
                    marginTop="mt-0"
                    position="items-start"
                    onSegmentClick={handleContactClick}
                    onExpand={() => handleExpand(10)}
                    onInvestigateClick={() => { }}
                    title={`Contact Numbers Distribution${selectedContactBar ? ` - ${selectedContactBar}` : ''}`}
                    legendPosition="right"
                    legendFontSize={8}
                    centerValue={publisherInsightTotalCount.toString()}
                    centerLabel="Total Count"
                  />
                )}
              </div>
            </DialogContent>
          </Dialog>

          {/* Top Handles Donut Chart */}
          <Dialog open={showHandlesDonut} onOpenChange={(open) => {
            setShowHandlesDonut(open);
            if (!open) setSelectedHandleForDonut("");
          }}>
            <DialogContent className="w-full h-full min-w-[280px] sm:min-w-[320px] md:min-w-[400px] min-h-[280px] sm:min-h-[320px] md:min-h-[350px] max-w-[90vw] sm:max-w-[500px] max-h-[80vh] sm:max-h-[400px] pt-2 dark:bg-gray-900">
              <div className="w-full h-full pt-0 m-2">
                {isHandleInsightLoading ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[280px] sm:min-h-[320px] md:min-h-[350px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !handleInsightData || !handleInsightData.data || handleInsightData.data.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <DonutChart
                    chartData={handleInsightData ? transformHandleInsightData(handleInsightData) : []}
                    dataKey="value"
                    nameKey="number"
                    chartConfig={(handleInsightData && handleInsightData.data) ? handleInsightData.data.reduce(
                      (acc: any, cur: any, index: number) => {
                        const colors = ['#540094', '#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE', '#EDE9FE', '#F3E8FF', '#FAF5FF'];
                        return {
                          ...acc,
                          [cur.sub_channel_name || '']: {
                            label: cur.sub_channel_name || '',
                            color: colors[index % colors.length]
                          }
                        };
                      },
                      {}
                    ) : {}}
                    isView={true}
                    isLoading={isHandleInsightLoading}
                    isPercentage={true}
                    direction="flex-col"
                    isdonut={true}
                    marginTop="mt-0"
                    position="items-start"
                    onSegmentClick={handleTopHandlesClick}
                    onExpand={() => handleExpand(7)}
                    onInvestigateClick={() => { }}
                    title={`Top Handles Distribution${selectedHandleBar ? ` - ${selectedHandleBar}` : ''}`}
                    legendPosition="right"
                    legendFontSize={8}
                    centerValue={handleInsightTotalCount.toString()}
                    centerLabel="Total Count"
                  />
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default BrandInfringementDashboard;