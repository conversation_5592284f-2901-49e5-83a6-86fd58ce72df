'use client'
import React, { useState } from "react";
import ResizableTable from "@/components/mf/TableComponent";
import { <PERSON><PERSON>, DialogTrigger, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

const WhiteListing = ({ 
  isDialogOpen: propIsDialogOpen, 
  setIsDialogOpen: propSetIsDialogOpen 
}: { 
  isDialogOpen?: boolean, 
  setIsDialogOpen?: (open: boolean) => void 
} = {}) => {
  const [searchTerm, setSearchTerm] = useState("");
  // Local state as fallback when props are not provided
  const [localDialogOpen, setLocalDialogOpen] = useState(false);
  // Use props if provided, otherwise use local state
  const isDialogOpen = propIsDialogOpen ?? localDialogOpen;
  const setIsDialogOpen = propSetIsDialogOpen ?? setLocalDialogOpen;

  // Dynamic field groups for dialog
  const [entries, setEntries] = useState([
    { inputValue: "", select1: "", select2: "", select3: "" }
  ]);

  // Table data
  const [data, setData] = useState([
    {
      date: "2021-01-01",
      whitelistData: "example.com",
      category: "Category 1",
      ownership: "Own",
    },
    {
      date: "2021-01-01",
      whitelistData: "example.com",
      category: "Category 1",
      ownership: "Own",
    },
    {
      date: "2021-01-01",
      whitelistData: "example.com",
      category: "Category 1",
      ownership: "Own",
    },
    {
      date: "2021-01-01",
      whitelistData: "example.com",
      category: "Category 1",
      ownership: "Own",
    },
  ]);

  // Handler for adding a new entry group
  const handleAddEntry = () => {
    setEntries([...entries, { inputValue: "", select1: "", select2: "", select3: "" }]);
  };

  // Handler for removing an entry group
  const handleRemoveEntry = (idx: number) => {
    if (entries.length === 1) return; // Always keep at least one
    setEntries(entries.filter((_, i) => i !== idx));
  };

  // Handler for changing a field in an entry group
  const handleEntryChange = (idx: number, field: string, value: string) => {
    setEntries(entries.map((entry, i) => i === idx ? { ...entry, [field]: value } : entry));
  };

  // Handler for saving all entries to the table
  const handleSave = () => {
    // Map dialog fields to table columns (customize as needed)
    const newRows = entries.map(e => ({
      date: e.inputValue,
      whitelistData: e.select1,
      category: e.select2,
      ownership: e.select3,
    }));
    setData([...data, ...newRows]);
    setEntries([{ inputValue: "", select1: "", select2: "", select3: "" }]);
    setIsDialogOpen(false);
  };

  // Check if all fields in all entries are filled
  const allFieldsFilled = entries.every(e =>
    e.inputValue && e.select1 && e.select2 && e.select3
  );

  // Check if the last entry is fully filled (for enabling + button)
  const lastEntryFilled = entries.length > 0 && entries[entries.length - 1].inputValue && entries[entries.length - 1].select1 && entries[entries.length - 1].select2 && entries[entries.length - 1].select3;

  // Define columns for the table
  const columns = [
    { title: "Date", key: "date" },
    { title: "Whitelist Data", key: "whitelistData" },
    { title: "Category", key: "category" },
    { title: "Ownership", key: "ownership" },
    // { title: "Action", key: "action" },
  ];

 

  // Handler functions for table actions
  const handleEdit = (item: any) => {
    console.log("Edit:", item);
  };

  const handleDelete = (item: any) => {
    console.log("Delete:", item);
  };

  const handleView = (item: any) => {
    console.log("View:", item);
  };

  const handleDownload = (item: any) => {
    console.log("Download:", item);
  };

  const handleSelect = (selectedItems: any[]) => {
    console.log("Selected items:", selectedItems);
  };

  return (
    <div className="h-full w-full bg-[#F3F4F6] shadow-md overflow-y-hidden">
      <div className="flex flex-col m-2 mt-2 gap-2">
        {/* Dialog for New Whitelisting */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-6xl w-full">
            <DialogHeader>
              <DialogTitle>New Whitelisting</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4 py-2">
              {entries.map((entry, idx) => (
                <div className="flex flex-row gap-4 items-end" key={idx}>
                  
                  <div className="flex-1 min-w-[220px]">
                    <label className="block text-sm font-medium font-semibold mb-1">Select Category</label>
                    <Select value={entry.select1} onValueChange={v => handleEntryChange(idx, 'select1', v)}>
                      <SelectTrigger><SelectValue placeholder="Select Category" /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Field List">Field List</SelectItem>
                        <SelectItem value="Website / Webpage">Website / Webpage</SelectItem>
                        <SelectItem value="Handle">Handle</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-1 min-w-[220px]">
                    <label className="block text-sm font-medium font-semibold mb-1">Select Ownership</label>
                    <Select value={entry.select3} onValueChange={v => handleEntryChange(idx, 'select3', v)}>
                      <SelectTrigger><SelectValue placeholder="Select Ownership" /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Own">Own</SelectItem>
                        <SelectItem value="Managed by Own">Managed by Own</SelectItem>
                        <SelectItem value="Partner">Partner</SelectItem>
                        <SelectItem value="Authorized Agent">Authorized Agent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-1 min-w-[220px]">
                    <label className="block text-sm font-medium font-semibold mb-1">Select Platform</label>
                    <Select value={entry.select2} onValueChange={v => handleEntryChange(idx, 'select2', v)}>
                      <SelectTrigger><SelectValue placeholder="Select Platform" /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Facebook">Facebook</SelectItem>
                        <SelectItem value="Instagram">Instagram</SelectItem>
                        <SelectItem value="Twitter">Twitter</SelectItem>
                        <SelectItem value="LinkedIn">LinkedIn</SelectItem>
                        <SelectItem value="YouTube">YouTube</SelectItem>
                        <SelectItem value="Reddit">Reddit</SelectItem>
                        <SelectItem value="Pinterest">Pinterest</SelectItem>
                        <SelectItem value="TikTok">TikTok</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex-1 min-w-[220px]">
                    <label className="block text-sm font-medium font-semibold mb-1">Input Data</label>
                    <Input value={entry.inputValue} onChange={e => handleEntryChange(idx, 'inputValue', e.target.value)} placeholder="Enter value" />
                  </div>
                  <div className="flex flex-row gap-2 items-end mb-1">
                    <Button type="button" className="w-10 h-10 rounded-md font-large font-semibold" onClick={handleAddEntry} disabled={!(entry.inputValue && entry.select1 && entry.select2 && entry.select3) || idx !== entries.length - 1}>+</Button>
                    <Button type="button" className="w-10 h-10 rounded-md font-large font-semibold" onClick={() => handleRemoveEntry(idx)} disabled={entries.length === 1}>-</Button>
                  </div>
                </div>
              ))}
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button className="text:color-white font-medium font-semibold w-30">Cancel</Button>
              </DialogClose>
              <Button onClick={handleSave} className="text-white w-20 font-medium font-semibold" disabled={!allFieldsFilled}>Save</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        {/* Resizable Table */}
        <div className="bg-white rounded-md shadow-sm">
          <ResizableTable
            columns={columns}
            data={data}
            headerColor="#DCDCDC"
            isEdit={true}
            isDelete={true}
            isView={true}
            isDownload={false}
            isSearchable={true}
            isSelectable={true}
            isPaginated={true}
            SearchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            buttonTextName="Add Whitelisting"
            onGenerateReport={() => setIsDialogOpen(true)}
          />
        </div>
      </div>
    </div>
  );
};

export default WhiteListing;
