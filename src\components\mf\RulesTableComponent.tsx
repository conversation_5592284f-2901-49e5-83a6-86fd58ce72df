"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  MdEdit,
  MdDelete,
  MdVisibility,
  MdFileDownload,
  MdArrowDropDown,
  MdSearch,
  MdRefresh,
  MdArrowDownward,
  MdArrowUpward,
  MdPause,
  MdPlayArrow,
} from "react-icons/md";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import Pagination from "../ui/pagination";
import {
  Select,
  SelectItem,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Import ShadCN Select components
import { Loader2 } from "lucide-react"; // Add this import
import { Button } from "../ui/button";

export type Column<T = void> =
  | { title: string; key: string }
  | { title: string; key: string; render: (data: T) => React.ReactNode };

interface ResizableTableProps<T> {
  columns: Column<T>[];
  data: T[];
  headerColor?: string;
  isEdit?: boolean;
  isDelete?: boolean;
  isView?: boolean;
  isDownload?: boolean;
  isPaginated?: boolean;
  isSearchable?: boolean;
  isSelectable?: boolean;
  isCount?: boolean;
  isLoading?: boolean;
  actionButton?: React.ReactNode | React.ReactNode[];
  onEdit?: (item: T) => void;
  onDownloadAll?: (item: T[]) => void;
  onDelete?: (item: T) => void;
  onView?: (item: T) => void;
  onDownload?: (item: T) => void;
  onRefresh?: () => void;
  onSelect?: (selectedItems: T[]) => void;
  itemCount?: (count: number) => void;
  isPause?: boolean;
  isPlay?: boolean;
  onPause?: (item: T) => void;
  onPlay?: (item: T) => void;
  isItemSelected?: (item: T) => boolean;
  initialItemsPerPage?: number;
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  showColumnSelector?: boolean; // New prop to control column selector visibility
  isDownloading?: boolean; // New prop for download loading state
  downloadingItemId?: string | null; // New prop for specific item download loading
  totalPages?: number; // New prop for server-provided total pages
  currentPage?: number; // New prop for current page from parent
  totalRecords?: number; // New prop for total records count
}

const DropdownMenu: React.FC<{
  columns: Column<Record<string, string | number>>[];
  onToggle: (key: string) => void;
  visibleColumns: Column<Record<string, string | number>>[];
}> = ({ columns, onToggle, visibleColumns }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="flex w-56 items-center justify-between rounded border bg-card p-2 text-card-foreground">
          <span>Columns</span>
          <div className="ml-auto flex items-center space-x-2">
            {/* <div className="h-6 border-l-2 border-black" />  */}|
            <span className="ml-2 mt-1 text-sm text-primary">
              {columns.length === visibleColumns.length
                ? "All"
                : visibleColumns.length}
            </span>
            <MdArrowDropDown className="ml-2" />
          </div>
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-56">
        {columns.map((column) => (
          <div key={column.key} className="flex items-center px-4 py-2">
            <Checkbox
              checked={visibleColumns.some((col) => col.key === column.key)}
              onCheckedChange={() => onToggle(column.key)}
            />
            <span className="ml-2">{column.title}</span>
          </div>
        ))}
      </PopoverContent>
    </Popover>
  );
};

const ResizableTable: React.FC<ResizableTableProps<Record<string, string | number>>> = ({
  columns,
  data,
  headerColor = "#ccc",
  isEdit = false,
  isDelete = false,
  isView = false,
  isPaginated = true,
  isDownload = false,
  isSearchable = false,
  isSelectable = false,
  isCount = false,
  isLoading = false,
  actionButton,
  onEdit,
  onDelete,
  onView,
  onDownload,
  onSelect,
  onDownloadAll,
  onRefresh,
  itemCount,
  isPause = false,
  isPlay = false,
  onPause,
  onPlay,
  isItemSelected,
  initialItemsPerPage = 10,
  onPageChange,
  onLimitChange,
  showColumnSelector = true, // Default to true to maintain existing behavior
  isDownloading = false,
  downloadingItemId = null,
  totalPages,
  currentPage: parentCurrentPage = 1,
  totalRecords,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedItems, setSelectedItems] = useState<Record<string, string | number>[]>([]);
  const [isMounted, setIsMounted] = useState(false);
  const [visibleColumns, setVisibleColumns] =
    useState<Column<Record<string, string | number>>[]>(columns);
  const [itemsPerPage, setItemsPerPage] = useState(initialItemsPerPage);
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: number }>(
    {},
  );
  const tableRef = useRef<HTMLTableElement>(null);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [currentPage, setCurrentPage] = useState(parentCurrentPage);
  const [containerHeight, setContainerHeight] = useState<number>(0);

  // Sync currentPage with parent's currentPage prop
  useEffect(() => {
    setCurrentPage(parentCurrentPage);
  }, [parentCurrentPage]);

  // Clear search term when data changes (for pagination)
  useEffect(() => {
    setSearchTerm("");
  }, [data]);

  useEffect(() => {
    setIsMounted(true);
    const initialWidths: { [key: string]: number } = {};
    columns.forEach((col) => {
      initialWidths[col.key] = 150;
    });
    setColumnWidths(initialWidths);
  }, [columns]);

  useEffect(() => {
    const calculateHeight = () => {
      // Use a more conservative height calculation for flex layouts
      const vh = window.innerHeight;
      const otherElementsHeight = 300; // Increased to account for header, search, pagination
      const calculatedHeight = Math.max(vh - otherElementsHeight, 400); // Minimum height of 400px
      setContainerHeight(calculatedHeight);
    };

    calculateHeight();
    window.addEventListener('resize', calculateHeight);

    return () => window.removeEventListener('resize', calculateHeight);
  }, []);

  const handleColumnToggle = (key: string) => {
    const newVisibleColumns = visibleColumns.some((col) => col.key === key)
      ? visibleColumns.filter((col) => col.key !== key)
      : (() => {
          const columnToAdd = columns.find((col) => col.key === key)!;
          const originalIndex = columns.findIndex((col) => col.key === key);
          
          // Find the position to insert based on original order
          let insertIndex = 0;
          for (let i = 0; i < visibleColumns.length; i++) {
            const currentColIndex = columns.findIndex((col) => col.key === visibleColumns[i].key);
            if (currentColIndex > originalIndex) {
              insertIndex = i;
              break;
            }
            insertIndex = i + 1;
          }
          
          const newColumns = [...visibleColumns];
          newColumns.splice(insertIndex, 0, columnToAdd);
          return newColumns;
        })();
    setVisibleColumns(newVisibleColumns);
  };

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "asc"
    ) {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const sortedData = React.useMemo(() => {
    const sortableData = [...data];
    if (sortConfig !== null) {
      sortableData.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableData;
  }, [data, sortConfig]);

  const filteredData = sortedData.filter((item) => {
    return visibleColumns.some((column) =>
      String(item[column.key]).toLowerCase().includes(searchTerm.toLowerCase()),
    );
  });

  // For server-side pagination, use the data directly since it's already paginated
  // For client-side pagination, apply the slice logic
  const paginatedData = totalPages && totalPages > 0 
    ? filteredData // Server-side pagination - data is already the correct page
    : filteredData.slice( // Client-side pagination - apply internal pagination
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage,
      );

  const handleCheckboxChange = (item: Record<string, string | number>) => {
    let newSelectedItems: Record<string, string | number>[];
    
    // Check if the item is currently selected
    const isCurrentlySelected = isItemSelected ? isItemSelected(item) : selectedItems.includes(item);
    
    if (isCurrentlySelected) {
      // Remove the item from selection
      if (isItemSelected) {
        // When using isItemSelected function, we need to filter by comparing IDs
        newSelectedItems = selectedItems.filter(i => i.id !== item.id);
      } else {
        // When using direct array includes, filter by reference
        newSelectedItems = selectedItems.filter(i => i !== item);
      }
    } else {
      // Add the item to selection
      newSelectedItems = [...selectedItems, item];
    }
    
    setSelectedItems(newSelectedItems);
    if (onSelect) onSelect(newSelectedItems);
  };

  // Column Resize Handlers
  const handleMouseDown = (e: React.MouseEvent, key: string) => {
    const startX = e.clientX;
    const startWidth = columnWidths[key];

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const newWidth = startWidth + moveEvent.clientX - startX;
      setColumnWidths((prevWidths) => ({
        ...prevWidths,
        [key]: newWidth,
      }));
    };

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  useEffect(() => {
    if (typeof itemCount === "function") itemCount(selectedItems.length);
  }, [selectedItems.length]);

  if (!isMounted) return null;

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex w-full items-center space-x-2 rounded-lg border bg-card p-1.5">
        {isSearchable && (
          <div className="flex flex-grow items-center space-x-2">
            <MdSearch className="text-xl text-card-foreground transition-colors duration-200 hover:text-primary" />
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-card text-card-foreground outline-none"
            />
          </div>
        )}
        {/* Separator */}
        {isSearchable && <div className="mx-2 h-6 border-l" />}
        {showColumnSelector && (
          <DropdownMenu
            columns={columns}
            onToggle={handleColumnToggle}
            visibleColumns={visibleColumns}
          />
        )}
        {actionButton && <div className="mx-2 h-6 border-l" />}
        {actionButton}
        {(actionButton || isCount || isDownload) && <div className="mx-2 h-6 border-l" />}
        {isCount && (
          <span
            title="Total Selected Rows"
            onClick={() =>
              typeof onDownloadAll === "function" ? onDownloadAll(data) : null
            }
            className="rounded-lg bg-purple-100 p-2 text-primary"
          >
            <span>{selectedItems.length}</span>
          </span>
        )}
        {/* Separator */}
        {isDownload && isCount && <div className="mx-2 h-6 border-l" />}
        {isDownload && (
          <div className="flex items-center">
            {isDownloading ? (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            ) : (
              <MdFileDownload
                title="Download Report"
                className="cursor-pointer text-xl text-primary transition-colors duration-200 hover:text-gray-400"
                onClick={() => onDownloadAll && onDownloadAll(data)}
              />
            )}
          </div>
        )}
        {/* Only show separator if there are more elements after download */}

        {/* <div className="mx-2 h-6 border-l" />
        <Button variant="ghost" size="icon-xs" onClick={() => onRefresh()}>
          <MdRefresh
            title="Table Data Refresh"
            className="cursor-pointer text-xl text-purple-600 transition-colors duration-200 hover:text-purple-700"
          />
        </Button> */}
        {/* Separator */}
        <div className="relative"></div>
      </div>

      <div 
        className="flex-1 relative overflow-auto"
        style={{ 
          overflowY: 'auto',
          overflowX: 'auto',
          willChange: 'scroll-position'
        }}
      >
        {isLoading && (
          <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/5">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}

        <Table ref={tableRef} className="min-w-full border">
          <TableHeader className="sticky top-0 z-10 bg-white shadow-sm">
            <TableRow>
              {isSelectable && (
                <TableHead
                  className="border-r"
                  style={{
                    width: "50px",
                    minWidth: "50px",
                    maxWidth: "50px",
                    backgroundColor: headerColor,
                    position: "sticky",
                    top: 0,
                    zIndex: 10,
                  }}
                >
                  <Checkbox
                    onCheckedChange={(checked) => {
                      const allItems = checked ? filteredData : [];
                      setSelectedItems(allItems);
                      if (onSelect) {
                        onSelect(allItems);
                      }
                    }}
                    checked={filteredData.length > 0 && filteredData.every(item => 
                      isItemSelected ? isItemSelected(item) : selectedItems.includes(item)
                    )}
                  />
                </TableHead>
              )}
              {visibleColumns.map((column) => (
                <TableHead
                  key={column.key}
                  className="relative border-r"
                  style={{
                    backgroundColor: headerColor,
                    color: "black",
                    width: `${columnWidths[column.key]}px`,
                    whiteSpace: "nowrap",
                    position: "sticky",
                    top: 0,
                    zIndex: 10,
                  }}
                >
                  <div className="flex items-center justify-center text-center">
                    <span
                      onClick={() => handleSort(column.key)}
                      className="cursor-pointer text-center font-semibold"
                      style={{ 
                        width: "100%",
                        fontSize: "calc(100% + 1%)" // Increase font size by 1%
                      }}
                    >
                      {column.title}
                      {sortConfig?.key === column.key &&
                        (sortConfig?.direction === "asc" ? (
                          <MdArrowUpward className="inline-block" />
                        ) : sortConfig?.direction === "desc" ? (
                          <MdArrowDownward className="inline-block" />
                        ) : null)}
                    </span>
                    <div
                      onMouseDown={(e) => handleMouseDown(e, column.key)}
                      className="absolute right-0 top-0 h-full w-2 cursor-col-resize hover:bg-gray-400"
                      style={{ backgroundColor: "transparent" }}
                    />
                  </div>
                </TableHead>
              ))}

              {(isEdit || isDelete || isView  || isPause || isPlay) && (
                <TableHead
                  className="border-r text-center font-semibold"
                  style={{
                    backgroundColor: headerColor,
                    color: "black",
                    width: `${columnWidths}px`,
                    whiteSpace: "nowrap",
                    position: "sticky",
                    top: 0,
                    zIndex: 10,
                  }}
                >
                  Action
                </TableHead>
              )}
            </TableRow>
          </TableHeader>

          <TableBody>
            {/* Show "No Data Found !" when there's no data, otherwise show data rows */}
            {paginatedData.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={visibleColumns.length + (isSelectable ? 1 : 0) + ((isEdit || isDelete || isView || isPause || isPlay) ? 1 : 0)}
                  className="h-32 text-center"
                >
                  <div className="flex justify-center items-center h-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((item, index) => (
                <TableRow key={index}>
                  {isSelectable && (
                    <TableCell
                      className="border-r"
                      style={{
                        width: "20px",
                        minWidth: "20px",
                        maxWidth: "20px",
                      }}
                    >
                      <Checkbox
                        checked={isItemSelected ? isItemSelected(item) : selectedItems.includes(item)}
                        onCheckedChange={() => handleCheckboxChange(item)}
                      />
                    </TableCell>
                  )}
                  {visibleColumns.map((column) => (
                    <TableCell key={column.key} className="border-r text-center dark:text-white">
                      {"render" in column ? column.render(item) : item[column.key]}
                    </TableCell>
                  ))}
                  {(isEdit || isDelete || isView || isPause || isPlay) && (
                    <TableCell className="border-r dark:text-white">
                      <div className="flex space-x-2 justify-center">
                        {isEdit && (
                          <button
                            onClick={() => onEdit?.(item)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdEdit size={18} />
                          </button>
                        )}
                        {isDelete && (
                          <button
                            onClick={() => onDelete?.(item)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdDelete size={18} />
                          </button>
                        )}
                        {isView && (
                          <button
                            onClick={() => onView?.(item)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdVisibility size={18} />
                          </button>
                        )}
                        {isDownload && (
                          <button
                            onClick={() => onDownload?.(item)}
                            className="text-primary hover:text-gray-500"
                            disabled={isDownloading && downloadingItemId === String(item.id)}
                          >
                            {isDownloading && downloadingItemId === String(item.id) ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <MdFileDownload size={18} />
                            )}
                          </button>
                        )}
                        {isPause && (
                          <button
                            onClick={() => onPause?.(item)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdPause size={18} />
                          </button>
                        )}
                        {isPlay && (
                          <button
                            onClick={() => onPlay?.(item)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdPlayArrow size={18} />
                          </button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

             {isPaginated && (
         <div className="flex-shrink-0 mt-1 flex items-center justify-between bg-white p-2 border-t">
           <div className="flex items-center space-x-4">
             <Select
               value={String(itemsPerPage)}
               onValueChange={(value) => {
                 const newLimit = Number(value);
                 setItemsPerPage(newLimit);
                 setCurrentPage(1); // Reset to first page when changing limit
                 onLimitChange?.(newLimit);
               }}
             >
               <SelectTrigger className="w-[90px] outline-none focus:ring-0 dark:text-white">
                 <SelectValue placeholder="Rows" />
               </SelectTrigger>
               <SelectContent className="border-none outline-none focus:ring-0">
                 <SelectItem value="50">50</SelectItem>
                 <SelectItem value="100">100</SelectItem>
                 <SelectItem value="500">500</SelectItem>
                 <SelectItem value="1000">1000</SelectItem>
               </SelectContent>
             </Select>
             
             {/* Records count display */}
             {totalRecords && (
               <div className="text-sm text-gray-600 dark:text-gray-400">
                 {totalRecords === 0 ? (
                   "No records found"
                 ) : (
                   `${(currentPage - 1) * itemsPerPage + 1}-${Math.min(currentPage * itemsPerPage, totalRecords)} of ${totalRecords} records`
                 )}
               </div>
             )}
           </div>
                      <Pagination
             currentPage={currentPage}
             totalPages={totalPages || Math.ceil(filteredData.length / itemsPerPage)}
             onPageChange={(page) => {
               setCurrentPage(page);
               onPageChange?.(page);
             }}
             showFirstLast={true}
           />
         </div>
       )}
    </div>
  );
};

export default ResizableTable;
