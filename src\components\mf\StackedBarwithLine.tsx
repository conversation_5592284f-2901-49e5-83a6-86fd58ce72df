import * as React from "react";
import { useRef, useEffect, useState } from "react";
import { Bar, CartesianGrid, XAxis, <PERSON>A<PERSON>s, Line, Composed<PERSON><PERSON>, LabelList,ResponsiveContainer } from "recharts";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartLegend,ChartTooltip,ChartTooltipContent } from "@/components/ui/chart";
import { getXAxisAngle, formatNumber } from '@/lib/utils';
import HeaderRow from "./HeaderRow";
import { Loader2 } from "lucide-react";
import { format } from 'date-fns';

interface XAxisConfig {
  dataKey?: string;
  tickLine?: boolean;
  tickMargin?: number;
  axisLine?: boolean;
  tickFormatter?: (value: string) => string;
  angle?: number;
  textAnchor?: string;
  dy: number;
}
interface YAxis1 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
}
interface YAxis2 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
}
interface TrafficTrendData {
  month?: string;
  Invalid?: number;
  Valid?: number;
  "Invalid %"?: number;
  
}


interface StackBarLineProps {
  chartData?: TrafficTrendData[];
  chartConfig?: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  handleFrequencyChange?: (value: string) => void; 
  selectedType?: string;
  selectoptions?:string[];
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  placeholder?:string;
  isHorizontal?:boolean;
  xAxisConfig?: XAxisConfig;
  YAxis1?: YAxis1;
  YAxis2?: YAxis2;
  isLoading?:boolean;
  selectedFrequency?:string;
  showTrendline?: boolean;
  trendlineKey?: string;
  formatterType?: "number" | "percentage";
}

const 
StackedBarWithLine: React.FC<StackBarLineProps> = ({
  chartData = [],
    chartConfig = {},
    handleTypeChange,
    visitEventOptions,
    selectedType,
    selectedFrequency,
    handleFrequencyChange,
    selectoptions=[],
    handleExport,
    onExport,
    onExpand,
    isLoading,
    title ,
    isSelect= false,
    isRadioButton =false,
    placeholder="",
    isHorizontal=false,
    showTrendline = false,
    trendlineKey,
    formatterType = "number",
    xAxisConfig = {
    dataKey: "month", 
    tickLine: true,
    tickMargin: 10,
    axisLine: true,
    angle: 0,
    textAnchor: "middle",
    dy: 10,
  },
  YAxis1 = {
    yAxisId: "left",
    orientation: "left",
    stroke: "hsl(var(--chart-1))",
    tickFormatter: (value: number) => formatNumber(value)
  },
  YAxis2 = {
    yAxisId: "right",
    orientation: "right",
    stroke: "hsl(var(--chart-3))",
    tickFormatter: (value: number) => `${value}%`,
  }, }) => {
  const labels = Object.values(chartConfig || {}).map(item => item.label);
  const colors = Object.values(chartConfig || {}).map(item => item.color);
  const months = chartData?.map(item => item.month).filter((month): month is string => month !== undefined) || [];
  const xAxisAngle = getXAxisAngle(months);
  const mergedXAxisConfig = { ...xAxisConfig };
  const mergedYAxis1Props = { ...YAxis1 };
  const mergedYAxis2Props = { ...YAxis2 };

  const CustomLegendContent = ({ labels,colors }: { labels: string[] , colors: string[];}) => {
    return (
      <div className="flex space-x-4 pt-10 justify-center">
      {labels?.map((labelText:string, index) => {
        return (
          <div className="flex items-center space-x-2" key={index}>
                            <span  style={{ backgroundColor: colors[index] }} className={`w-3 h-3 rounded-full `}></span>
            <span>{labelText}</span>
          </div>
        );
      })}
    </div>
    );
  };
  return (
    <Card className="border-none w-full">
      {/* <CardTitle className="p-2 ">
     <HeaderRow
      visitEventOptions={visitEventOptions}
     handleTypeChange={handleTypeChange}
     selectedType={selectedType}
     selectedFrequency={selectedFrequency}
     handleFrequencyChange={handleFrequencyChange}
     title={title}
     onExpand={onExpand}
      handleExport={handleExport}
      selectoptions={selectoptions}
     isRadioButton={isRadioButton}
     isSelect={isSelect}
     onExport={onExport}
     placeholder={placeholder}/>
     </CardTitle> */}
     {isLoading ? (
      <div className="flex items-center justify-center h-[380px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
       </div>
     ) : (
      <CardContent className="h-[380px] sm:w-full overflow-y-auto scrollbar">
        <ChartContainer config={chartConfig} className="relative md:h-[300px] min-h-[300px] sm:w-full lg:w-full p-0">
        <ResponsiveContainer width="100%" height="100%">
          {chartData.length > 0 ? (
            <ComposedChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 30, right: 2, left: 2 }}
              barGap={20}
            >
             <ChartLegend content={<CustomLegendContent labels={labels} colors={colors} />} />

              <CartesianGrid vertical={false} />
              <XAxis className="text-small-font" 
                {...mergedXAxisConfig}
                angle={xAxisAngle} 
                interval={0}
                tickFormatter={(value: string) => {
                  if (!value) return '';
                  try {
                    const date = new Date(value);
                    return format(date, 'dd/MMM');
                  } catch {
                    return value;
                  }
                }}
              />
              <YAxis className="text-small-font" {...mergedYAxis1Props}
                tickFormatter={(value: number) => formatNumber(value)}
              />
              <YAxis className="text-small-font"
                {...mergedYAxis2Props}
                tickFormatter={(value) => `${value}%`}
              />
              <ChartTooltip cursor={false} content={<ChartTooltipContent  indicator="dot" formatterType={formatterType} />} />

              {Object.keys(chartConfig).map((key, index) => {
                if (showTrendline && key === trendlineKey) {
                  return (
                    <Line
                      key={index}
                      type="monotone"
                      dataKey={key}
                      stroke={chartConfig[key].color}
                      strokeWidth={3}
                      dot={{ fill: "#A93226", r: 5 }}
                      yAxisId="right"
                    />
                  );
                }
                if (key !== "Invalid %") {
                  return (
                    <Bar
                      key={index}
                      dataKey={key}
                      stackId="a"
                      fill={key === "Total" ? "#540094" : chartConfig[key].color}
                      yAxisId="left"
                      barSize={60}
                    >
                      <LabelList
                        dataKey={key}
                        position="center"
                        formatter={(value: number) => formatNumber(value)}
                        style={{ fontSize: '8px', fill: '#000' }}
                      />
                    </Bar>
                  );
                }
                return null;
              })}
            </ComposedChart>
            ) : (
              <div className="flex items-center justify-center h-full w-full">
                <span className="text-sm dark:text-white">No Data Found !</span>
              </div>
            )}
        </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
      )}
    </Card>
  );
};

export default StackedBarWithLine;
