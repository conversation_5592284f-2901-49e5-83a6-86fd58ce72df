"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, Settings, X, Plus, Minus, Loader2, ChevronDown } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { MultiSelect } from "@/components/ui/multi-select";

import { useRouter, useSearchParams } from "next/navigation";
import { useApiCall } from "@/queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import FilterModal from "@/components/report/filterModal";
import DeliveryOptionsModal from "@/components/report/deliveryoptionsModal";
// import ThresholdModal from "@/components/report/thresholdModal";
import ConfirmationDialog from "@/components/report/confirmationDialog/index";
import ToastContent, {
  ToastType,
} from "@/components/mf/ToastContent";
import { useToast } from "@/hooks/use-toast";
import { useDateRange } from "@/components/mf/DateRangeContext";

interface EmailListModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface GroupedOption {
  label: string;
  items: { id: string; label: string }[];
}

const GenerateReportPage = () => {

  // Helper function to ensure category is always an array
  const ensureArray = (value: any): string[] => {
    if (Array.isArray(value)) return value;
    if (value && typeof value === 'string') return [value];
    if (value) return [value];
    return [];
  };

  const [toastData, setToastData] = useState<{
    type: ToastType;
    title: string;
    description?: string;
    variant?: "default" | "destructive" | null;
  } | null>(null);
  const { selectedPackage } = usePackage();
  const [frequency, setFrequency] = useState<any>(null);
  const [fileType, setFileType] = useState<string>("csv");
  const [thresholdModalOpen, setThresholdModalOpen] = useState(false);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [customEmails, setCustomEmails] = useState<string[]>([""]);
  const [columnOrder, setColumnOrder] = useState<
    Array<{ id: string; content: string; type: "dimension" | "metric" }>
  >([]);
  const [selectedColumns, setSelectedColumns] = useState<typeof columnOrder>(
    []
  );
  const [selectedDimensions, setSelectedDimensions] = useState<string[]>([]);
  const [selectedSocialMediaDimensions, setSelectedSocialMediaDimensions] = useState<string[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [selectedItemForThreshold, setSelectedItemForThreshold] = useState<{
    id: string;
    label: string;
  } | null>(null);
  const [templateValue, setTemplateValue] = useState<string[]>([]);
  const [templateValueSocialMedia, setTemplateValueSocialMedia] = useState<string[]>([]);
  const [selectedItemForFilter, setSelectedItemForFilter] = useState<{
    id: string;
    label: string;
  } | null>(null);
  const [filterCategory, setFilterCategory] = useState<string>("");
  const [dimensionSearch, setDimensionSearch] = useState("");
  const [metricSearch, setMetricSearch] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  const [selectedTemplateSocialMedia, setSelectedTemplateSocialMedia] = useState<string>("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams.get("id");
  const mode = searchParams.get("mode");
  const frequencyValue = searchParams.get("frequency");
  const [reportName, setReportName] = useState("");
  const [reportCategory, setReportCategory] = useState<string>("summary");
  const [category, setCategory] = useState<string[]>([]);

  const [deliveryModalOpen, setDeliveryModalOpen] = useState(false);
  const [deliveryModalType, setDeliveryModalType] = useState<
    "schedule" | "download"
  >("schedule");
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [dimensions, setDimensions] = useState<GroupedOption[]>([]);
  const [socialMediaDimensions, setSocialMediaDimensions] = useState<GroupedOption[]>([]);
  const [openDimensionPopover, setOpenDimensionPopover] = useState(false);
  
  const [openSocialMediaDimensionPopover, setOpenSocialMediaDimensionPopover] = useState(false);
  const [filterData, setFilterData] = useState<any>();
  const [deliveryData, setDeliveryData] = useState<any | null>(undefined);
  const [statusCheck, setStatusCheck] = useState("no");
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const [isDownloadReport, setIsDownloadReport] = useState<boolean | null>(null);

  const [dimensionsFilters, setDimensionsFilters] = useState<
    Array<{
      field: string;
      value: string[];
    }>
  >([]);

  const [reportNameError, setReportNameError] = useState<string | null>(null);
  const [dimensionsError, setDimensionsError] = useState<string | null>(null);
  const [metricsError, setMetricsError] = useState<string | null>(null);
  const [categoryError, setCategoryError] = useState<string | null>(null);
  const [metricsThresholds, setMetricsThresholds] = useState<
    Array<{ field: string; operator: string; value: string }>
  >([]);
  const [categoryData, setCategoryData] = useState<any[]>([]);
  const { toast } = useToast();
  const { startDate, endDate } = useDateRange();

  // api call  category 
  const categoryApiResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/get_category`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/get_category`,
    method: "POST",
    params: {
      package_name: selectedPackage,
      // package_name: "com.myairtelapp"
    },
    onSuccess: (data: any) => {
      console.log("Category API Response:", data);
      setCategoryData(data);
    },
    onError: (error) => {
      console.error("Error fetching category:", error);
    },
  });
  
  const categoryApi = categoryApiResult.type === "mutation" ? categoryApiResult.result : null;
  const categoryLoading = categoryApiResult.loading;

  useEffect(() => {
    if (selectedPackage) {
      (categoryApi as any).mutate();
    }
  }, [selectedPackage,startDate,endDate]);

  // api  call template

  const templateApiResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/get_template`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/get_template`,
    method: "POST",
    params: {
      package_name: selectedPackage,
      // package_name: "com.myairtelapp",
      category: 'website_app'
    },
    onSuccess: (data: any) => {
      setTemplateValue(data);
    },
    onError: (error) => {
      console.error("Error fetching template:", error);
    },
  });
  
  const templateApi = templateApiResult.type === "mutation" ? templateApiResult.result : null;
  const templateLoading = templateApiResult.loading;
const templateApiSocialMediaResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/get_template`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/get_template`,
    method: "POST",
    params: {
      package_name: selectedPackage,
      // package_name: "com.myairtelapp",
      category: 'social_media'
    },
    onSuccess: (data: any) => {
      console.log("Template data:", data);
      setTemplateValueSocialMedia(data);

    },
    onError: (error) => {
      console.error("Error fetching template:", error);
    },
  });
  
  const templateApiSocialMedia = templateApiSocialMediaResult.type === "mutation" ? templateApiSocialMediaResult.result : null;
  const templateLoadingSocialMedia = templateApiSocialMediaResult.loading;

  useEffect(() => {
    console.log(category,"cat")
    if (category.length > 0 && category.includes('website_app')) {
      (templateApi as any).mutate();
    }
  }, [category,selectedPackage]);
  
useEffect(() => {
    if (category.length > 0 && category.includes('social_media')) {
      (templateApiSocialMedia as any).mutate();
    }
  }, [category,selectedPackage]);

  // Set default website_app template when templateValue changes
  useEffect(() => {
    if (templateValue && templateValue.length > 0 && !editId && !selectedTemplate && category.includes('website_app')) {
      setSelectedTemplate(templateValue[0]);
    }
  }, [templateValue, editId, selectedTemplate, category,selectedPackage]);

  // Set default social_media template when templateValueSocialMedia changes
  useEffect(() => {
    if (templateValueSocialMedia && templateValueSocialMedia.length > 0 && !editId && !selectedTemplateSocialMedia && category.includes('social_media')) {
      setSelectedTemplateSocialMedia(templateValueSocialMedia[0]);
    }
  }, [templateValueSocialMedia, editId, selectedTemplateSocialMedia, category,selectedPackage,socialMediaDimensions]);
  

  // api call for website_app template fields
  const websiteAppTemplateFieldsResult = useApiCall({
      url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/get_template_fields`,
      // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/get_template_fields`,
      method: "POST",
      params: {
        template: selectedTemplate,
        category: "website_app",
        package_name: selectedPackage,
        // package_name: "com.myairtelapp",
        report_type: reportCategory
      },
      onSuccess: (data: any) => {
        setDimensions(data?.dimensions || []);
      },
      onError: (error) => {
        
      console.error("Error fetching website_app template fields:", error);
      },
    });
    
  const websiteAppTemplateFieldsMutation = websiteAppTemplateFieldsResult.type === "mutation" ? websiteAppTemplateFieldsResult.result : null;
  const websiteAppTemplateFieldsLoading = websiteAppTemplateFieldsResult.loading;

  // api call for social_media template fields
  const socialMediaTemplateFieldsResult = useApiCall({
      url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/get_template_fields`,
      // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/get_template_fields`,
      method: "POST",
      params: {
        template: selectedTemplateSocialMedia,
          category: "social_media",
        package_name: selectedPackage,
        // package_name: "com.myairtelapp",
        report_type: reportCategory
      },
      onSuccess: (data: any) => {
        setSocialMediaDimensions(data?.dimensions || []);
      },
      onError: (error) => {
        console.error("Error fetching social_media template fields:", error);
      },
    });
    
  const socialMediaTemplateFieldsMutation = socialMediaTemplateFieldsResult.type === "mutation" ? socialMediaTemplateFieldsResult.result : null;
  const socialMediaTemplateFieldsLoading = socialMediaTemplateFieldsResult.loading;

  // Add useEffect to handle website_app template fields API call
  useEffect(() => {
    if (mode === "view") return;
    
    if (selectedTemplate && category.includes('website_app') && selectedPackage && reportCategory) {
      (websiteAppTemplateFieldsMutation as any).mutate();
    }
  }, [selectedTemplate, category, selectedPackage, reportCategory, mode]);

  // Add useEffect to handle social_media template fields API call
  useEffect(() => {
    if (mode === "view") return;
    
    if (selectedTemplateSocialMedia && category.includes('social_media') && selectedPackage && reportCategory && socialMediaTemplateFieldsMutation) {
      (socialMediaTemplateFieldsMutation as any).mutate({});
    }
  }, [selectedTemplateSocialMedia, category, selectedPackage, reportCategory, mode]);

  // api call for filter
  const filterApiResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/fields/filters/${selectedItemForFilter?.id}/`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/fields/filters/${selectedItemForFilter?.id}/`,
    method: "POST",
    params: {
      package_name: selectedPackage,
      // package_name: "com.myairtelapp",
      category: filterCategory || ensureArray(category).join(","),
      report_type:reportCategory
    },
    onSuccess: (data: any) => {
      setFilterData(data);
    },
    onError: (error) => {
      console.error("Error fetching report:", error);
    },
  });
  
  const filterApi = filterApiResult.type === "mutation" ? filterApiResult.result : null;
  const filterLoading = filterApiResult.loading;

  // api call for creating report
  const createReportApiResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/create_report`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/create_report`,
    method: "POST",
    manual: true,
    onSuccess: (data: any) => {
      setToastData({
        type: "success",
        title: "Success",
        description: "Report created successfully!",
        variant: "default",
      });
      if (data?.status === "success") {
        router.push("/reportingtool/report");
      }
    },
    onError: (error) => {
      console.error("Error creating report:", error);
    },
  });
  
  const createReportApi = createReportApiResult.type === "mutation" ? createReportApiResult.result : null;
  const createReportLoading = createReportApiResult.loading;

  // Update columnOrder when dimensions change
  useEffect(() => {
    const dimensionColumns = selectedDimensions.map((dim) => ({
      id: `dim-${dim}`,
      content: dim,
      type: "dimension" as const,
    }));

    setColumnOrder([...dimensionColumns]);
  }, [selectedDimensions]);

  // api call for view report
  const viewReportApiResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/view_report`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/view_report`,
    method: "POST",
    manual: true,
    onSuccess: (data: any) => { },
    onError: (error) => {
      console.error("Error fetching report:", error);
    },
  });
  
  const viewReportApi = viewReportApiResult.type === "mutation" ? viewReportApiResult.result : null;
  const viewReportLoading = viewReportApiResult.loading;

  useEffect(() => {
    if (editId) {
      if (
        viewReportApi &&
        typeof (viewReportApi as any).mutate === "function"
      ) {
        (viewReportApi as any).mutate({
          doc_id: editId,
          package_name: selectedPackage,
          // package_name: "com.myairtelapp"
        });
      }
    }
  }, [editId]);

  // Replace the useEffect that sets the form data with this:
  useEffect(() => {
    if ((viewReportApi as any)?.data?.data) {
      const responseData = (viewReportApi as any).data.data;
      
      // Extract categories and data from the response
      const categories: string[] = [];
      let websiteAppData = null;
      let socialMediaData = null;
      
      if (responseData.website_app) {
        // Use the actual category value from the response
        const websiteAppCategory = ensureArray(responseData.website_app.category);
        categories.push(...websiteAppCategory);
        websiteAppData = responseData.website_app;
      }
      
      if (responseData.social_media) {
        // Use the actual category value from the response
        const socialMediaCategory = ensureArray(responseData.social_media.category);
        categories.push(...socialMediaCategory);
        socialMediaData = responseData.social_media;
      }
      
      // Remove duplicates from categories
      const uniqueCategories = categories.filter((item, index) => categories.indexOf(item) === index);
      
      // Use website_app data as primary, fall back to social_media data for common fields
      const primaryData = websiteAppData || socialMediaData;
      
      if (primaryData) {
        setReportName(primaryData.report_name);
        setReportCategory(primaryData.report_type);
        setFrequency(primaryData.occurence);
        setFileType(primaryData.reportFormats);
        
        // Set download status based on the response
        const downloadStatus = primaryData.download === "yes";
        setIsDownloadReport(downloadStatus);
        
        // Only set category if categoryData is loaded
        if (categoryData && categoryData.length > 0) {
          console.log("Setting category with available categoryData:", uniqueCategories);
          setCategory(uniqueCategories);
        } else {
          console.log("CategoryData not yet loaded, deferring category setting");
          // We'll set it in a separate effect when categoryData loads
        }
        
        // Set website_app-specific data
        if (websiteAppData) {
          setSelectedTemplate(websiteAppData.template);
          
          // Set website_app dimensions filters
          if (websiteAppData.dimensions && websiteAppData.dimensions.length > 0) {
            setDimensionsFilters(websiteAppData.dimensions);

            // Extract dimension fields for selection
            const dimensionFields = websiteAppData.dimensions.map((dim: { field: string }) => dim.field);
            setSelectedDimensions(dimensionFields);

            const transformed = websiteAppData.dimensions.map((dimension: { field: string }) => ({
              label: "",
              items: [
                {
                  id: dimension.field,
                  label: dimension.field,
                },
              ],
            }));

            setDimensions(transformed);
          }
        }
        
        // Set social_media-specific data
        if (socialMediaData) {
          setSelectedTemplateSocialMedia(socialMediaData.template);
          
          // Set social_media dimensions if website_app doesn't exist or if we need separate social_media dimensions
            if (socialMediaData.dimensions && socialMediaData.dimensions.length > 0) {
            // If we only have social_media data, use it for main dimensions
            if (!websiteAppData) {
              setDimensionsFilters(socialMediaData.dimensions);
              const dimensionFields = socialMediaData.dimensions.map((dim: { field: string }) => dim.field);
              setSelectedDimensions(dimensionFields);
            } else {
              // If we have both, set social_media dimensions separately
              const socialMediaDimensionFields = socialMediaData.dimensions.map((dim: { field: string }) => dim.field);
              setSelectedSocialMediaDimensions(socialMediaDimensionFields);
            }

            const socialMediaTransformed = socialMediaData.dimensions.map((dimension: { field: string }) => ({
              label: "",
              items: [
                {
                  id: dimension.field,
                  label: dimension.field,
                },
              ],
            }));

            setSocialMediaDimensions(socialMediaTransformed);
          }
        }
        
        // Transform delivery options to match DeliveryOptionsModal expectations
        let transformedDeliveryData = null;
        
        if (websiteAppData?.deliveryOptions) {
          transformedDeliveryData = { ...websiteAppData.deliveryOptions };
          
          // Transform email data structure for website_app
          if (websiteAppData.deliveryOptions.email) {
            const emailData = websiteAppData.deliveryOptions.email;
            transformedDeliveryData.email = {
              ...emailData,
              status: emailData.status || true, // Ensure status is set for the modal
              website_app_to: emailData.to || [],
              website_app_mail_id_list: emailData.mail_id_list || [],
              // Also keep original structure for backward compatibility
              to: emailData.to || [],
              mail_id_list: emailData.mail_id_list || []
            };
            
            // Set customEmails for backward compatibility
            if (emailData.to && emailData.to.length > 0) {
              setCustomEmails(emailData.to);
            }
          }
        }
        
        if (socialMediaData?.deliveryOptions) {
          if (!transformedDeliveryData) {
            transformedDeliveryData = { ...socialMediaData.deliveryOptions };
          }
          
          // Transform email data structure for social_media
          if (socialMediaData.deliveryOptions.email) {
            const emailData = socialMediaData.deliveryOptions.email;
            if (!transformedDeliveryData.email) {
              transformedDeliveryData.email = {};
            }
            
            transformedDeliveryData.email = {
              ...transformedDeliveryData.email,
              ...emailData,
              status: emailData.status || true, // Ensure status is set for the modal
              social_media_to: emailData.to || [],
              social_media_mail_id_list: emailData.mail_id_list || [],
              // Also keep original structure
              to: emailData.to || [],
              mail_id_list: emailData.mail_id_list || []
            };
            
            // Set customEmails if no website_app emails
            if (!websiteAppData?.deliveryOptions?.email?.to && emailData.to && emailData.to.length > 0) {
              setCustomEmails(emailData.to);
            }
          }
        }
        
        
        // If we have both website_app and social_media, create a nested structure
        if (websiteAppData && socialMediaData) {
          const nestedDeliveryOptions = {
            website_app: transformedDeliveryData,
            social_media: socialMediaData.deliveryOptions
          };
          setDeliveryData(nestedDeliveryOptions);
        } else {
          // Single category - use transformed data directly
          setDeliveryData(transformedDeliveryData);
        }
        
       
      }
    }
  }, [viewReportApi?.data]);



  useEffect(() => {
    console.log("Debug - Current state:", {
      selectedTemplate,
      dimensionsLength: dimensions,
      selectedDimensionsLength: selectedDimensions.length,
      dimensionsFiltersLength: dimensionsFilters.length,
      editId,
      hasViewData: !!viewReportApi?.data,
      currentCategory: category,
      deliveryData: deliveryData
    });
  }, [
    selectedTemplate,
    dimensions,
    selectedDimensions,
    dimensionsFilters,
    editId,
    viewReportApi?.data,
    category,
    deliveryData
  ]);

  // Debug category changes
  useEffect(() => {
    console.log("Category state changed:", category);
  }, [category]);

  // Handle click outside to close category dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isCategoryDropdownOpen && !target.closest(".relative")) {
        setIsCategoryDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isCategoryDropdownOpen]);

  // Handle category setting when both viewReportApi data and categoryData are available
  useEffect(() => {
    if ((viewReportApi as any)?.data?.data && categoryData && categoryData.length > 0) {
      const responseData = (viewReportApi as any).data.data;
      const categories: string[] = [];
      
      if (responseData.website_app) {
        const websiteAppCategory = ensureArray(responseData.website_app.category);
        categories.push(...websiteAppCategory);
      }
      
      if (responseData.social_media) {
        const socialMediaCategory = ensureArray(responseData.social_media.category);
        categories.push(...socialMediaCategory);
      }
      
      const uniqueCategories = categories.filter((item, index) => categories.indexOf(item) === index);
      
      console.log("=== CATEGORY SETTING DEBUG ===");
      console.log("Extracted categories from response:", uniqueCategories);
      console.log("Available categoryData options:", categoryData);
      console.log("CategoryData values:", categoryData.map(item => item.value));
      
      // Check if the extracted categories match any available options
      const validCategories = uniqueCategories.filter(cat => 
        categoryData.some(option => option.value === cat)
      );
      
      console.log("Valid matching categories:", validCategories);
      console.log("Setting category to:", validCategories.length > 0 ? validCategories : uniqueCategories);
      
      setCategory(validCategories.length > 0 ? validCategories : uniqueCategories);
    }
  }, [viewReportApi?.data, categoryData]);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(columnOrder || []);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setColumnOrder(items);
  };

  const handleColumnSelect = (value: string) => {
    const selectedColumn = columnOrder?.find((col) => col.id === value);
    if (selectedColumn && !selectedColumns?.find((col) => col.id === value)) {
      setSelectedColumns([...selectedColumns, selectedColumn]);
    }
  };

  const handleRemoveColumn = (id: string) => {
    setSelectedColumns(selectedColumns?.filter((col) => col.id !== id) || []);
  };

  const handleDimensionSelect = (value: string) => {
    setSelectedDimensions((prev) => {
      const currentSelected = prev || [];
      if (currentSelected.includes(value)) {
        return currentSelected.filter((dim) => dim !== value);
      }
      const newSelected = [...currentSelected, value];
      // Clear error if at least one dimension is selected
      if (newSelected.length > 0) {
        setDimensionsError(null);
      }
      return newSelected;
    });
  };

  const handleSocialMediaDimensionSelect = (value: string) => {
    setSelectedSocialMediaDimensions((prev) => {
      const currentSelected = prev || [];
      if (currentSelected.includes(value)) {
        return currentSelected.filter((dim) => dim !== value);
      }
      const newSelected = [...currentSelected, value];
      // Clear error if at least one dimension is selected
      if (newSelected.length > 0) {
        setDimensionsError(null);
      }
      return newSelected;
    });
  };

  const handleMetricSelect = (value: string) => {
    setSelectedMetrics((prev) => {
      const currentSelected = prev || [];
      if (currentSelected.includes(value)) {
        return currentSelected.filter((metric) => metric !== value);
      }
      const newSelected = [...currentSelected, value];
      // Clear error if at least one metric is selected
      if (newSelected.length > 0) {
        setMetricsError(null);
      }
      return newSelected;
    });
  };

  const handleFilterClick = (item: { id: string; label: string }, categoryType: string) => {
    setSelectedItemForFilter(item);
    setFilterCategory(categoryType);
    if (typeof (filterApi as any).mutate === "function") {
      (filterApi as any).mutate();
    }
    setFilterModalOpen(true);
  };

  const handleSaveThreshold = (thresholdData: {
    field: string;
    operator: string;
    value: string;
  }) => {
    // Check if this metric already has a threshold
    const existingIndex = metricsThresholds.findIndex(
      (metric: { field: string; operator: string; value: string }) => metric.field === thresholdData.field
    );

    if (existingIndex !== -1) {
      // Update existing threshold
      const updatedThresholds = [...metricsThresholds];
      updatedThresholds[existingIndex] = thresholdData;
      setMetricsThresholds(updatedThresholds);
    } else {
      // Add new threshold
      setMetricsThresholds([...metricsThresholds, thresholdData]);
    }
  };

  const handleSettingsClick = (item: { id: string; label: string }) => {
    setSelectedItemForThreshold(item);
    setThresholdModalOpen(true);
  };

  const handleCustomEmailChange = (index: number, value: string) => {
    const newEmails = [...(customEmails || [""])];
    newEmails[index] = value;
    setCustomEmails(newEmails);
  };

  const handleAddCustomEmail = () => {
    setCustomEmails([...(customEmails || [""]), ""]);
  };

  const handleRemoveCustomEmail = (index: number) => {
    if ((customEmails || []).length > 1) {
      const newEmails = (customEmails || []).filter((_, i) => i !== index);
      setCustomEmails(newEmails);
    }
  };

  // Handle template selection with validation
  const handleTemplateChange = (value: string) => {
    setSelectedTemplate(value);
    setSelectedDimensions([]);
  };
const handleTemplateChangeSocialMedia = (value: string) => {
    setSelectedTemplateSocialMedia(value);
    setSelectedSocialMediaDimensions([]);
  };
  const handleDownloadClick = () => {
    if (!reportName.trim()) {
      setReportNameError("Report name is mandatory.");
      return;
    }

    if (category.length === 0) {
      setCategoryError("Please select at least one category");
      return;
    }

    if (selectedTemplate === "Custom") {
      let hasError = false;
      if (selectedDimensions.length === 0) {
        setDimensionsError("Please select at least one dimension");
        hasError = true;
      }
      if (hasError) return;
    }

    console.log("=== DOWNLOAD CLICK DEBUG ===");
    console.log("Current deliveryData:", deliveryData);
    console.log("Current customEmails:", customEmails);
    console.log("Category:", category);
    console.log("=== END DOWNLOAD DEBUG ===");

    setDeliveryModalType("download");
    setDeliveryModalOpen(true);
    setStatusCheck("yes");
  };

  const handleFilterSave = (dimensionData: {
    field: string;
    value: string[];
  }) => {
    setDimensionsFilters((prev) => {
      const existingIndex = prev.findIndex(
        (dim) => dim.field === dimensionData.field
      );

      if (existingIndex !== -1) {
        const updatedFilters = [...prev];
        updatedFilters[existingIndex] = dimensionData;
        return updatedFilters;
      } else {
        return [...prev, dimensionData];
      }
    });
  };

  const editReportApiResult = useApiCall({
    url: `https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/reporting_tool/reporting_tool_screen/edit_report`,
    // url: process.env.NEXT_PUBLIC_APP_PERF + `reporting_tool/edit_report`,
    method: "POST",
    manual: true,
    onSuccess: (data: any) => {
      // toast({
      //   title: "Success",
      //   description: "Report updated successfully!",
      //   duration: 3000,
      // });
      setToastData({
        type: "success",
        title: "Success",
        description: "Report updated successfully!",
        variant: "default",
      });

      router.push("/reportingtool/report");
    },
    onError: (error) => {
      console.error("Error editing report:", error);
    },
  });
  
  const editReportApi = editReportApiResult.type === "mutation" ? editReportApiResult.result : null;
  const editReportLoading = editReportApiResult.loading;

  // Fixed handleModalSubmit function
  const handleModalSubmit = (data: any) => {
    setDeliveryData(data);
    if (data.dateRange) {
      console.log("Start Date from child:", data.dateRange.startDate);
      console.log("End Date from child:", data.dateRange.endDate);
    }

    // Helper function to create dimensions payload for a specific category
    const createDimensionsPayload = (templateName: string, categoryType: string) => {
      let dimensionsForPayload: Array<{ field: string; value: string[] }> = [];

      if (templateName === "Custom") {
        const relevantDimensions = categoryType === "website_app" ? selectedDimensions : selectedSocialMediaDimensions;
        dimensionsForPayload = relevantDimensions.map((dimensionId) => {
          const existingFilter = dimensionsFilters.find(
            (filter) => filter.field === dimensionId
          );

          if (existingFilter) {
            return existingFilter;
          } else {
            return {
              field: dimensionId,
              value: [],
            };
          }
        });
      } else {
        // For non-custom templates, include all dimensions from the template
        const relevantDimensions = categoryType === "website_app" ? dimensions : socialMediaDimensions;
        const allDimensions: Array<{ id: string; label: string }> = [];

        relevantDimensions.forEach((group) => {
          group.items.forEach((item) => {
            allDimensions.push(item);
          });
        });

        // Use existing filter value if available, otherwise default to empty array
        dimensionsForPayload = allDimensions.map((dimension) => {
          const existingFilter = dimensionsFilters.find(
            (filter) => filter.field === dimension.id
          );

          return {
            field: dimension.id,
            value: existingFilter ? existingFilter.value : [],
          };
        });
      }

      return dimensionsForPayload;
    };

    // FIXED: Conditional API calling with proper mutate functions
    if (editId) {
      // For edit mode, send only the specific category data wrapped in category key
      let updateData: any = {};

      if (category.includes('website_app')) {
        const websiteAppPayload: any = {
          report_name: reportName,
          occurence: frequency,
          package_name: selectedPackage,
          dimensions: createDimensionsPayload(selectedTemplate, "website_app"),
          reportFormats: fileType,
          report_type: reportCategory,
          deliveryOptions: data?.website_app || data,
          download: deliveryModalType === "download" ? "yes" : "no",
          template: selectedTemplate,
          category: "website_app",
        };

        // Add start_date and end_date only if frequency is "Custom Range"
        if (frequency === "Custom Range") {
          websiteAppPayload.start_date = data?.dateRange?.startDate;
          websiteAppPayload.end_date = data?.dateRange?.endDate;
        }

        updateData = {
          "website_app": websiteAppPayload
        };
      } else if (category.includes('social_media')) {
        const socialMediaPayload: any = {
          report_name: reportName,
          occurence: frequency,
          package_name: selectedPackage,
          dimensions: createDimensionsPayload(selectedTemplateSocialMedia, "social_media"),
          reportFormats: fileType,
          report_type: reportCategory,
          deliveryOptions: data?.social_media || data,
          download: deliveryModalType === "download" ? "yes" : "no",
          template: selectedTemplateSocialMedia,
          category: "social_media",
        };

        // Add start_date and end_date only if frequency is "Custom Range"
        if (frequency === "Custom Range") {
          socialMediaPayload.start_date = data?.dateRange?.startDate;
          socialMediaPayload.end_date = data?.dateRange?.endDate;
        }

        updateData = {
          "social_media": socialMediaPayload
        };
      }

      const updatePayload = {
        doc_id: editId,
        package_name: selectedPackage,
        update_data: updateData
      };

      console.log("Edit payload:", updatePayload);

      if (
        editReportApi &&
        typeof (editReportApi as any).mutate === "function"
      ) {
        (editReportApi as any).mutate(updatePayload);
      } else {
        console.error("Edit API mutate function not available");
      }
    } else {
      // For create mode, create separate payloads for website_app and social_media categories
      const finalPayload: any = {};

      if (category.includes('website_app')) {
        const websiteAppPayload: any = {
          report_name: reportName,
          occurance: frequency,
          package_name:selectedPackage,
          dimensions: createDimensionsPayload(selectedTemplate, "website_app"),
          reportFormats: fileType,
          report_type: reportCategory,
          deliveryOptions: data?.website_app || data,
          download: deliveryModalType === "download" ? "yes" : "no",
          template: selectedTemplate,
          category: ["website_app"],
        };

        // Add start_date and end_date only if frequency is "Custom Range"
        if (frequency === "Custom Range") {
          websiteAppPayload.start_date = data?.dateRange?.startDate;
          websiteAppPayload.end_date = data?.dateRange?.endDate;
        }

        finalPayload.website_app = websiteAppPayload;
      }

      if (category.includes('social_media')) {
        const socialMediaPayload: any = {
          report_name: reportName,
          occurance: frequency,
          package_name: selectedPackage,
          dimensions: createDimensionsPayload(selectedTemplateSocialMedia, "social_media"),
          reportFormats: fileType,
          report_type: reportCategory,
          deliveryOptions: data?.social_media || data,
          download: deliveryModalType === "download" ? "yes" : "no",
          template: selectedTemplateSocialMedia,
          category: ["social_media"],
        };

        // Add start_date and end_date only if frequency is "Custom Range"
        if (frequency === "Custom Range") {
          socialMediaPayload.start_date = data?.dateRange?.startDate;
          socialMediaPayload.end_date = data?.dateRange?.endDate;
        }

        finalPayload.social_media = socialMediaPayload;
      }

      // If both categories are not selected, fall back to original structure
      if (!category.includes('website_app') && !category.includes('social_media')) {
        const dimensionsForPayload = selectedDimensions.map((dimensionId) => {
          const existingFilter = dimensionsFilters.find(
            (filter) => filter.field === dimensionId
          );

          if (existingFilter) {
            return existingFilter;
          } else {
            return {
              field: dimensionId,
              value: [],
            };
          }
        });

        const basePayload: any = {
          report_name: reportName,
          occurance: frequency,
          package_name: selectedPackage,
          dimensions: dimensionsForPayload,
          reportFormats: fileType,
          report_type: reportCategory,
          deliveryOptions: data,
          download: deliveryModalType === "download" ? "yes" : "no",
          template: selectedTemplate,
          category: category,
        };

        // Add start_date and end_date only if frequency is "Custom Range"
        if (frequency === "Custom Range") {
          basePayload.start_date = data?.dateRange?.startDate;
          basePayload.end_date = data?.dateRange?.endDate;
        }
        
        console.log("check the payload (fallback)", basePayload);
      } else {
        console.log("check the final payload", finalPayload);
      }

      // If creating a new report, call create API only
      if (
        createReportApi &&
        typeof (createReportApi as any).mutate === "function"
      ) {
        (createReportApi as any).mutate(finalPayload);
      } else {
        console.error("Create API mutate function not available");
      }
    }

    setDeliveryModalOpen(false);
  };


  // Fixed handleConfirmation function
  const handleConfirmation = (action: "cloud" | "email" | "download") => {
    setConfirmationDialogOpen(false);
    if (action === "cloud" || action === "email") {
      setDeliveryModalType("download");
      setDeliveryModalOpen(true);
    } else {
      if (deliveryData) {
        // FIXED: Conditional API calling for download
        if (editId) {
          // For edit mode, send only the specific category data wrapped in category key
          let updateData: any = {};

          if (category.includes('website_app')) {
            const websiteAppPayload: any = {
              report_name: reportName,
              occurence: frequency,
              package_name: selectedPackage,
              dimensions: dimensionsFilters,
              reportFormats: fileType,
              report_type: reportCategory,
              deliveryOptions: deliveryData?.website_app || deliveryData,
              download: "yes",
              template: selectedTemplate,
              category: "website_app",
            };

            // Add start_date and end_date only if frequency is "Custom Range"
            if (frequency === "Custom Range") {
              websiteAppPayload.start_date = deliveryData?.dateRange?.startDate;
              websiteAppPayload.end_date = deliveryData?.dateRange?.endDate;
            }

            updateData = {
              "website_app": websiteAppPayload
            };
          } else if (category.includes('social_media')) {
            const socialMediaPayload: any = {
              report_name: reportName,
              occurence: frequency,
              package_name: selectedPackage,
              dimensions: dimensionsFilters,
              reportFormats: fileType,
              report_type: reportCategory,
              deliveryOptions: deliveryData?.social_media || deliveryData,
              download: "yes",
              template: selectedTemplateSocialMedia,
              category: "social_media",
            };

            // Add start_date and end_date only if frequency is "Custom Range"
            if (frequency === "Custom Range") {
              socialMediaPayload.start_date = deliveryData?.dateRange?.startDate;
              socialMediaPayload.end_date = deliveryData?.dateRange?.endDate;
            }

            updateData = {
              "social_media": socialMediaPayload
            };
          }

          const updatePayload = {
            doc_id: editId,
            package_name: selectedPackage,
            update_data: updateData
          };

          console.log("Edit confirmation payload:", updatePayload);

          if (
            editReportApi &&
            typeof (editReportApi as any).mutate === "function"
          ) {
            (editReportApi as any).mutate(updatePayload);
          } else {
            console.error("Edit API mutate function not available");
          }
        } else {
          // For create mode, create separate payloads for website_app and social_media categories
          const finalPayload: any = {};

          if (category.includes('website_app')) {
            const websiteAppPayload: any = {
              report_name: reportName,
              occurance: frequency,
              package_name: selectedPackage,
              dimensions: dimensionsFilters,
              reportFormats: fileType,
              report_type: reportCategory,
              deliveryOptions: deliveryData?.website_app || deliveryData,
              download: "yes",
              template: selectedTemplate,
              category: ["website_app"],
            };

            // Add start_date and end_date only if frequency is "Custom Range"
            if (frequency === "Custom Range") {
              websiteAppPayload.start_date = deliveryData?.dateRange?.startDate;
              websiteAppPayload.end_date = deliveryData?.dateRange?.endDate;
            }

            finalPayload.website_app = websiteAppPayload;
          }

          if (category.includes('social_media')) {
            const socialMediaPayload: any = {
              report_name: reportName,
              occurance: frequency,
              package_name: selectedPackage,
              dimensions: dimensionsFilters,
              reportFormats: fileType,
              report_type: reportCategory,
              deliveryOptions: deliveryData?.social_media || deliveryData,
              download: "yes",
              template: selectedTemplateSocialMedia,
              category: ["social_media"],
            };

            // Add start_date and end_date only if frequency is "Custom Range"
            if (frequency === "Custom Range") {
              socialMediaPayload.start_date = deliveryData?.dateRange?.startDate;
              socialMediaPayload.end_date = deliveryData?.dateRange?.endDate;
            }

            finalPayload.social_media = socialMediaPayload;
          }

          console.log("check the confirmation payload", finalPayload);

          // If creating new, call create API
          if (
            createReportApi &&
            typeof (createReportApi as any).mutate === "function"
          ) {
            (createReportApi as any).mutate(finalPayload);
          } else {
            console.error("Create API mutate function not available");
          }
        }
      }
    }
  };

  console.log("frequency value in generate page", frequency)

  const handleScheduleClick = () => {
    if (!reportName.trim()) {
      setReportNameError("Report name is mandatory.");
      return;
    }

    if (category.length === 0) {
      setCategoryError("Please select at least one category");
      return;
    }

    if (selectedTemplate === "Custom") {
      let hasError = false;
      if (selectedDimensions.length === 0) {
        setDimensionsError("Please select at least one dimension");
        hasError = true;
      }
      if (hasError) return;
    }

    setDeliveryModalType("schedule");
    setDeliveryModalOpen(true);
  };


  const handleCloseDeliveryModal = () => {
    setDeliveryModalOpen(false);
    setDeliveryData(null);
  };

  const handleCategoryToggle = (categoryValue: string) => {
    if (mode === "view") return;
    
    setCategory((prev) => {
      const currentSelected = prev || [];
      const isCurrentlySelected = currentSelected.includes(categoryValue);
      
      let newSelection;
      if (isCurrentlySelected) {
        newSelection = currentSelected.filter((cat) => cat !== categoryValue);
      } else {
        newSelection = [...currentSelected, categoryValue];
      }
      
      // Clear error if at least one category is selected
      if (newSelection.length > 0) {
        setCategoryError(null);
      }
      
      return newSelection;
    });
  };

  return (
    <>
      {(createReportLoading || viewReportLoading || editReportLoading) ? (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      ) : (
        <div className="bg-white p-6">
          {toastData && (
            <ToastContent
              type={toastData.type}
              title={toastData.title}
              description={toastData.description}
              variant={toastData.variant}
            />
          )}
          <div className="mb-6 flex items-center justify-between">
            <h1 className="text-xl font-bold">
              {mode === "view"
                ? "View Report"
                : mode === "edit"
                  ? "Edit Report"
                  : "Generate New Report"}
            </h1>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-4 gap-4 items-center">
              {/* Report Name */}
              <div className="space-y-2">
                <Label>Report Name</Label>
                <Input
                  placeholder="Enter Report Name "
                  className="dark:text-white"
                  value={reportName}
                  onChange={(e) => {
                    setReportName(e.target.value);
                    setReportNameError(null); // Clear error on change
                  }}
                  disabled={mode === "view" || mode === "edit"}
                />
                {reportNameError && ( // Display error message
                  <p className="text-sm text-red-500">{reportNameError}</p>
                )}
              </div>

              {/* Category Dropdown */}
              <div className="space-y-2">
                <Label>Category</Label>
                
                {/* Custom dropdown for selecting categories */}
                <div className="relative">
                  <div 
                    className={`flex dark:text-white h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer ${mode === "view" ? "opacity-50 cursor-not-allowed" : ""}`}
                    onClick={() => mode !== "view" && setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
                  >
                    <span className="flex-1 truncate">
                      {category.length === 0 
                        ? "Select Category" 
                        : category.map(cat => {
                            const categoryItem = categoryData.find(item => item.value === cat);
                            return categoryItem ? categoryItem.label : cat;
                          }).join(", ")
                      }
                    </span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${isCategoryDropdownOpen ? 'rotate-180' : ''}`} />
                  </div>
                  
                  {isCategoryDropdownOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-md max-h-60 overflow-auto">
                      {categoryLoading ? (
                        <div className="flex justify-center items-center p-4">
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                        </div>
                      ) : categoryData.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground">
                          No categories available
                        </div>
                      ) : (
                        categoryData.map((item) => {
                          const isSelected = category.includes(item.value);
                          
                          return (
                            <div 
                              key={item.value}
                              className="flex items-center gap-2 p-2 hover:bg-accent cursor-pointer"
                              onClick={() => handleCategoryToggle(item.value)}
                            >
                              <Checkbox 
                                id={`category-checkbox-${item.value}`}
                                checked={isSelected}
                                disabled={mode === "view" || mode === "edit"}
                              />
                              <Label 
                                htmlFor={`category-checkbox-${item.value}`}
                                className="cursor-pointer flex-1 dark:text-white"
                              >
                                {item.label}
                              </Label>
                            </div>
                          );
                        })
                      )}
                    </div>
                  )}
                </div>
                
                {categoryError && (
                  <p className="text-sm text-red-500">{categoryError}</p>
                )}
              </div>
              

              {/* File Type */}
              <div className="space-y-2">
                <Label>File Type</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="csv"
                      name="fileType"
                      value="csv"
                      checked={fileType === "csv"}
                      onChange={(e) => setFileType(e.target.value)}
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view" || mode === "edit"}
                    />
                    <Label htmlFor="csv">Csv</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="xlsx"
                      name="fileType"
                      value="xlsx"
                      checked={fileType === "xlsx"}
                      onChange={(e) => setFileType(e.target.value)}
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view" || mode === "edit"}
                    />
                    <Label htmlFor="xlsx">Xlsx</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="parquet"
                      name="fileType"
                      value="parquet"
                      checked={fileType === "parquet"}
                      onChange={(e) => setFileType(e.target.value)}
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view" || mode === "edit"}
                    />
                    <Label htmlFor="parquet">Parquet</Label>
                  </div>
                </div>
              </div>

              {/* Report Category */}
              <div className="space-y-2">
                <Label>Report Category</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="summary"
                      name="reportCategory"
                      value="summary"
                      checked={reportCategory === "summary"}
                      onChange={(e) =>
                        setReportCategory(e.target.value as "summary")
                      }
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view" || mode === "edit"}
                    />
                    <Label htmlFor="summary">Summary</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="transactional"
                      name="reportCategory"
                      checked={reportCategory === "transactional"}
                      value="transactional"
                      onChange={(e) =>
                        setReportCategory(e.target.value as "transactional")
                      }
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view" || mode === "edit"}
                    />
                    <Label htmlFor="transactional">Transactional</Label>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-x-4 w-full">
              
              {/* Website App Section - Show only when website_app category is selected */}
              {category.includes('website_app') && (
                <div className="w-1/2 border border-gray-300 rounded-md p-4">
                  <div className="space-y-4 col-span-3">
                    {/* Website App Template */}
                    <div className="space-y-2">
                      <Label>Website App Template</Label>
                      <Select
                        value={selectedTemplate}
                        onValueChange={handleTemplateChange}
                        disabled={mode === "view"}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose Website App Template" />
                        </SelectTrigger>

                        <SelectContent>
                          {templateLoading ? (
                            <div className="flex justify-center items-center p-2">
                              <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            </div>
                          ) : (
                            <>
                              {templateValue?.map((template) => (
                                <SelectItem key={template} value={template} className="text-sm leading-5">
                                  {template}
                                </SelectItem>
                              ))}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Website App Dimensions */}
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold">Website App Dimensions</h3>
                    </div>
                    {selectedTemplate === "Custom" && (
                      <Popover
                        open={openDimensionPopover}
                        onOpenChange={setOpenDimensionPopover}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="w-full justify-between"
                            disabled={mode === "view"}
                          >
                            Select Website App Dimensions
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="ml-2 h-4 w-4 shrink-0 opacity-50"
                            >
                              <path d="m6 9 6 6 6-6" />
                            </svg>
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <div className="p-2">
                            <Input
                              type="text"
                              placeholder="Search dimensions..."
                              className="mb-2"
                              value={dimensionSearch}
                              onChange={(e) => setDimensionSearch(e.target.value)}
                            />
                            <div className="max-h-[300px] overflow-y-auto">
                              {dimensions
                                .map((group) => ({
                                  ...group,
                                  items: group.items.filter((item) =>
                                    item.label
                                      .toLowerCase()
                                      .includes(dimensionSearch.toLowerCase())
                                  ),
                                }))
                                .filter((group) => group.items.length > 0)
                                .map((group) => (
                                  <div key={group.label} className="mb-4">
                                    <div className="mb-2 px-2 text-sm font-medium text-gray-700">
                                      {group.label}
                                    </div>
                                    {group.items.map((item) => (
                                      <div
                                        key={item.id}
                                        className="flex cursor-pointer items-center justify-between rounded-md px-2 py-1.5 "
                                      >
                                        <div className="flex items-center gap-2">
                                          {selectedTemplate === "Custom" ? (
                                            <>
                                              <Checkbox
                                                id={`dimension-${item.id}`}
                                                checked={(
                                                  selectedDimensions || []
                                                ).includes(item.id)}
                                                onCheckedChange={() =>
                                                  handleDimensionSelect(item.id)
                                                }
                                              />
                                              <Label
                                                htmlFor={`dimension-${item.id}`}
                                                className="cursor-pointer"
                                                onClick={() =>
                                                  handleDimensionSelect(item.id)
                                                }
                                              >
                                                {item.label}
                                              </Label>
                                            </>
                                          ) : (
                                            <Label>{item.label}</Label>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ))}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    )}

                    {dimensionsError && (
                      <p className="text-sm text-red-500">{dimensionsError}</p>
                    )}

                    {/* Display selected dimensions */}
                    {selectedTemplate === "Custom" &&
                      (selectedDimensions || []).length === 0 ? (
                      <div className="mt-2 flex h-20 items-center justify-center rounded-md border border-dashed border-gray-300">
                        <p className="text-sm text-gray-500">
                          Select dimensions to view them here
                        </p>
                      </div>
                    ) : (
                      <>
                        {websiteAppTemplateFieldsLoading ? (
                          <p className="text-xs text-gray-500">
                            Loading Website App Dimensions...
                          </p>
                        ) : (
                          <div className="mt-2 max-h-[200px] overflow-y-auto rounded-md border border-gray-300 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                            {dimensions.map((group) => {
                              const groupItems =
                                selectedTemplate === "Custom"
                                  ? group.items.filter((item) =>
                                    (selectedDimensions || []).includes(item.id)
                                  )
                                  : group.items;

                              if (groupItems.length === 0) return null;

                              return (
                                <div key={group.label} className="space-y-2 p-2">
                                  <div className="sticky top-0 bg-white text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">
                                    {group.label}
                                  </div>

                                  {groupItems.map((item) => (
                                    <div
                                      key={item.id}
                                      className="flex items-center justify-between"
                                    >
                                      <div
                                        className={`flex items-center gap-2 ${selectedTemplate !== "Custom"
                                            ? "justify-between w-full"
                                            : ""
                                          }`}
                                      >
                                        {selectedTemplate === "Custom" ? (
                                          <>
                                            <Checkbox
                                              id={item.id}
                                              checked={true}
                                              onClick={() =>
                                                handleDimensionSelect(item.id)
                                              }
                                            />
                                            <Label htmlFor={item.id}>
                                              {item.label}
                                            </Label>
                                          </>
                                        ) : (
                                          <>
                                            <Label htmlFor={item.id}>
                                              {item.label}
                                            </Label>
                                            <Filter
                                              className="h-4 w-4 cursor-pointer text-primary"
                                              onClick={() =>
                                                handleFilterClick(item, "website_app")
                                              }
                                            />
                                          </>
                                        )}
                                      </div>

                                      {selectedTemplate === "Custom" && (
                                        <Filter
                                          className="h-4 w-4 cursor-pointer text-primary"
                                          onClick={() => handleFilterClick(item, "website_app")}
                                        />
                                      )}
                                    </div>
                                  ))}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* Social Media Section - Show only when social_media category is selected */}
              {category.includes('social_media') && (
                <div className="w-1/2 border border-gray-300 rounded-md p-4">
                  <div className="space-y-4 col-span-3">
                    {/* Social Media Template */}
                    <div className="space-y-2">
                      <Label>Social Media Template</Label>
                      <Select
                        value={selectedTemplateSocialMedia}
                        onValueChange={handleTemplateChangeSocialMedia}
                        disabled={mode === "view"}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose Social Media Template" />
                        </SelectTrigger>

                        <SelectContent>
                          {templateLoadingSocialMedia ? (
                            <div className="flex justify-center items-center p-2">
                              <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            </div>
                          ) : (
                            <>
                              {templateValueSocialMedia?.map((template) => (
                                <SelectItem key={template} value={template} className="text-sm leading-5">
                                  {template}
                                </SelectItem>
                              ))}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Social Media Dimensions */}
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold">Social Media Dimensions</h3>
                    </div>
                    {selectedTemplateSocialMedia === "Custom" && (
                      <Popover
                        open={openSocialMediaDimensionPopover}
                        onOpenChange={setOpenSocialMediaDimensionPopover}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="w-full justify-between"
                            disabled={mode === "view"}
                          >
                            Select Social Media Dimensions
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="ml-2 h-4 w-4 shrink-0 opacity-50"
                            >
                              <path d="m6 9 6 6 6-6" />
                            </svg>
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <div className="p-2">
                            <Input
                              type="text"
                              placeholder="Search dimensions..."
                              className="mb-2"
                              value={dimensionSearch}
                              onChange={(e) => setDimensionSearch(e.target.value)}
                            />
                            <div className="max-h-[300px] overflow-y-auto">
                              {socialMediaDimensions
                                .map((group) => ({
                                  ...group,
                                  items: group.items.filter((item) =>
                                    item.label
                                      .toLowerCase()
                                      .includes(dimensionSearch.toLowerCase())
                                  ),
                                }))
                                .filter((group) => group.items.length > 0)
                                .map((group) => (
                                  <div key={group.label} className="mb-4">
                                    <div className="mb-2 px-2 text-sm font-medium text-gray-700">
                                      {group.label}
                                    </div>
                                    {group.items.map((item) => (
                                      <div
                                        key={item.id}
                                        className="flex cursor-pointer items-center justify-between rounded-md px-2 py-1.5"
                                      >
                                        <div className="flex items-center gap-2">
                                          {selectedTemplateSocialMedia === "Custom" ? (
                                            <>
                                              <Checkbox
                                                id={`social_media-dimension-${item.id}`}
                                                checked={(
                                                  selectedSocialMediaDimensions || []
                                                ).includes(item.id)}
                                                onCheckedChange={() =>
                                                  handleSocialMediaDimensionSelect(item.id)
                                                }
                                              />
                                              <Label
                                                
                                                htmlFor={`social_media-dimension-${item.id}`}
                                                className="cursor-pointer"
                                                onClick={() =>
                                                  handleSocialMediaDimensionSelect(item.id)
                                                }
                                              >
                                                {item.label}
                                              </Label>
                                            </>
                                          ) : (
                                            <Label>{item.label}</Label>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ))}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    )}

                    {dimensionsError && (
                      <p className="text-sm text-red-500">{dimensionsError}</p>
                    )}

                    {/* Display selected dimensions */}
                    {selectedTemplateSocialMedia === "Custom" &&
                      (selectedSocialMediaDimensions || []).length === 0 ? (
                      <div className="mt-2 flex h-20 items-center justify-center rounded-md border border-dashed border-gray-300">
                        <p className="text-sm text-gray-500">
                          Select dimensions to view them here
                        </p>
                      </div>
                    ) : (
                      <>
                        {socialMediaTemplateFieldsLoading ? (
                          <p className="text-xs text-gray-500">
                            Loading Social Media Dimensions...
                          </p>
                        ) : (
                          <div className="mt-2 max-h-[200px] overflow-y-auto rounded-md border border-gray-300 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                            {socialMediaDimensions.map((group) => {
                              const groupItems =
                                selectedTemplateSocialMedia === "Custom"
                                  ? group.items.filter((item) =>
                                    (selectedSocialMediaDimensions || []).includes(item.id)
                                  )
                                  : group.items;

                              if (groupItems.length === 0) return null;

                              return (
                                <div key={group.label} className="space-y-2 p-2">
                                  <div className="sticky top-0 bg-white text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">
                                    {group.label}
                                  </div>

                                  {groupItems.map((item) => (
                                    <div
                                      key={item.id}
                                      className="flex items-center justify-between"
                                    >
                                      <div
                                        className={`flex items-center gap-2 ${selectedTemplateSocialMedia !== "Custom"
                                            ? "justify-between w-full"
                                            : ""
                                          }`}
                                      >
                                        {selectedTemplateSocialMedia === "Custom" ? (
                                          <>
                                            <Checkbox
                                              id={item.id}
                                              checked={true}
                                              onClick={() =>
                                                handleSocialMediaDimensionSelect(item.id)
                                              }
                                            />
                                            <Label htmlFor={item.id}>
                                              {item.label}
                                            </Label>
                                          </>
                                        ) : (
                                          <>
                                            <Label htmlFor={item.id}>
                                              {item.label}
                                            </Label>
                                            <Filter
                                              className="h-4 w-4 cursor-pointer text-primary"
                                              onClick={() =>
                                                handleFilterClick(item, "social_media")
                                              }
                                            />
                                          </>
                                        )}
                                      </div>

                                      {selectedTemplateSocialMedia === "Custom" && (
                                        <Filter
                                          className="h-4 w-4 cursor-pointer text-primary"
                                          
                                         onClick={() => handleFilterClick(item, "social_media")}
                                        />
                                      )}
                                    </div>
                                  ))}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              onClick={() => {
                router.push("/reportingtool/report");
              }}
              className="text-white bg-primary hover:bg-primary"
            >
              Cancel
            </Button>
            <Button
              onClick={handleScheduleClick}
              className="text-white bg-primary hover:bg-primary"
              disabled={(mode === "view" || mode === "edit") && isDownloadReport === true}
            >
              Schedule
            </Button>
            <Button
              onClick={handleDownloadClick}
              className="text-white bg-primary hover:bg-primary"
              disabled={(mode === "view" || mode === "edit") &&  isDownloadReport === false}
            >
              Download
            </Button>
          </div>

          <ConfirmationDialog
            isOpen={confirmationDialogOpen}
            onClose={() => setConfirmationDialogOpen(false)}
            onConfirm={handleConfirmation}
          />

          <FilterModal
            isOpen={filterModalOpen}
            onClose={() => {
              setFilterModalOpen(false);
              setSelectedItemForFilter(null);
            }}
            selectedItem={selectedItemForFilter}
            onSave={handleFilterSave}
            filterData={filterData}
            filterloading={filterLoading}
            savedFilters={dimensionsFilters}
            mode={mode}
          />

          <DeliveryOptionsModal
            category={Array.isArray(category) ? category.join(",") : category}
            isOpen={deliveryModalOpen}
            onClose={handleCloseDeliveryModal}
            type={deliveryModalType}
            onSubmit={handleModalSubmit}
            defaultData={deliveryData}
            mode={mode}
            frequency={frequency}
            onFrequencyChange={setFrequency}
          />
        </div>
      )}
    </>
  );
};

export default GenerateReportPage;