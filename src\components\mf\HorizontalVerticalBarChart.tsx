"use client"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,LabelList } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartLegend,
  ChartLegendContent,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import HeaderRow from "./HeaderRow";
import { Loader2 } from "lucide-react"
import { InformationCard } from "./InformationCard";
import { useFullscreen } from "@/hooks/fullscreen";

interface chartData {
  label: string;
  [key: string]: string | number;
}

interface chartconfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

interface CustomTickProps {
  x: number;
  y: number;
  payload: {
    value: string;
  };
  chartConfig: chartconfig;
}

type LabelPosition = "top" | "bottom" | "left" | "right" | "center" | "inside" | "outside" | "insideLeft" | "insideRight" | "insideTop" | "insideBottom" | "insideTopL<PERSON><PERSON>" | "insideBottomL<PERSON>t" | "insideTopRight" | "insideBottomRight" | "insideStart" | "insideEnd" | "end";

interface HorizontalBarChartProps {
  chartData: chartData[];
  chartConfig: chartconfig;
  xAxisTitle?: string;
  yAxisTitle?: string;
  isHorizontal: boolean;
  InformCard?: { title: string; desc: string }[];
  title?: string;
  position?: LabelPosition;
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  isRadioButton: boolean;
  isSelect: boolean;
  BarchartTitle?: string;
  formatterType?: "number" | "percentage";
  dataKey?: string;
  namekeys?: string;
  isLoading: boolean;
  barsize?: number;
  setheight?: string;
  setwidth?: string;
  showLegend?: boolean;
  onBarClick?: (data: { label: string; value: number }) => void;
  margin?: {
    left?: number;
    right?: number;
    top?: number;
    bottom?: number;
  };
  titleFontSize?: string;
}

const HorizontalVerticalBarChart: React.FC<HorizontalBarChartProps> = ({
  chartData,
  chartConfig,
  xAxisTitle,
  yAxisTitle,
  isHorizontal = false,
  handleExport,
  onExport,
  onExpand,
  isLoading,
  position,
  isSelect,
  isRadioButton,
  title,
  BarchartTitle,
  formatterType = "number",
  dataKey = "value",
  namekeys,
  InformCard=[],
  barsize,
  setheight,
  setwidth,
  showLegend = true,
  onBarClick,
  margin = { left: 10, right: 20, top: 5, bottom: 20 },
  titleFontSize,
}) => {
  const isFullscreen = useFullscreen();
  
  const formatLabel = (value: number) => {
    if (formatterType === "percentage") {
      return `${(value * 1).toFixed(0)}%`;   // Format as percentage
    }
    return `${value}`;  // Format as number
  };
  
  // Dynamic chart height for fullscreen
  const getFullscreenChartHeight = () => {
    if (window.innerWidth < 640) {
      return 400; // Mobile
    } else if (window.innerWidth >= 640 && window.innerWidth <= 1024) {
      return 500; // Tablet
    } else if (window.innerWidth > 1024 && window.innerWidth < 1280) {
      return 600; // Desktop
    } else {
      return 700; // Large screens
    }
  };
  
  // Calculate chart height based on setheight prop or data length
  // When legend is hidden, reduce the height to prevent scroll bars
  const baseHeight = setheight ? parseInt(setheight) : Math.min(chartData.length * 10, 400);
  const chartHeight = isFullscreen ? getFullscreenChartHeight() : (showLegend ? baseHeight : Math.max(baseHeight - 60, 200)); // Reduce by 60px when legend hidden, minimum 200px
  
  // Calculate chart width based on setwidth prop or default to 100%
  const chartWidth = setwidth ? parseInt(setwidth) : undefined;
  
  const CustomTick = ({ x, y, payload, chartConfig }: CustomTickProps) => {
    const label = chartConfig[payload.value]?.label || payload.value;
  
    return (
      <g transform={`translate(${x},${y})`}>
        <title>{label}</title> {/* Tooltip on hover */}
        <text
          x={0}
          y={0}
          dy={4} // Adjusts vertical alignment
          textAnchor="end"
          fontSize={10}
          style={{
            width: "120px", // Increased width to accommodate 15 chars
            wordWrap: "break-word",
            overflowWrap: "break-word",
            maxWidth: "120px",
          }}
        >
          {label.length > 15 ? `${label.slice(0, 15)}...` : label}
        </text>
      </g>
    );
  };

  const barColor = chartConfig[dataKey]?.color || Object.values(chartConfig)[0]?.color || "#000000";
  
   
  return (
    <Card className={`border-none h-full w-full p-0 ${isFullscreen ? 'h-[100vh]' : ''}`} style={{ width: setwidth }}>
      {title && (
        <HeaderRow
          title={title}
          onExpand={onExpand}
          handleExport={handleExport}
          isRadioButton={isRadioButton}
          isSelect={isSelect}
          onExport={onExport}
          titleFontSize={titleFontSize}
        />
      )}

      {BarchartTitle && (
        <CardHeader className="items-center pb-0">
          <CardTitle className="text-body font-semibold">{BarchartTitle}</CardTitle>
        </CardHeader>
      )}
      <CardContent className={`${showLegend ? 'overflow-y-auto scrollbar' : ''} ${setheight ? 'h-full' : 'min-h-fit'}`} style={setheight ? { height: setheight } : {}}>
      <div className={`flex flex-row space-y-8 space-x-8 ${showLegend ? 'h-full' : ''} w-full`}>
          {/* Information Cards */}
          <div className="flex-0 flex flex-col justify-center mt-8">
            {InformCard?.map((item, index) => (

              <InformationCard
                key={index}
                InformTitle={item.title}
                informDescription={item.desc}
              />
            ))}
          </div>
          <div className=" flex justify-center flex-1 w-full" style={{margin: "0px 0px 0px 0px", padding: "0px 0px 0px 0px"}}>
      {isLoading ?(
         <div className="flex items-center justify-center min-h-full">
                    <Loader2 className=" h-8 w-8 animate-spin text-primary dark:text-white" />
               </div>
      ):(
        <ChartContainer config={chartConfig} style={{ height: chartHeight, width: chartWidth || "100%" }}>
          {chartData.length>0 ?(
          <BarChart  
            height={chartHeight}
            width={chartWidth}
            accessibilityLayer
            data={chartData}
            layout={isHorizontal ? "vertical" : "horizontal"}
            margin={margin}
            barSize={barsize}
            barGap={1}
            barCategoryGap="2%"
          >
            {/* Conditionally set axis based on orientation */}
            {isHorizontal ? (
              <>
               <YAxis
                  dataKey="label"
                  type="category"
                  tickLine={false}
                  tickMargin={2}
                  axisLine={false}
                  interval={0}
                  className="text-body"
                  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} />}
                  style={{ fontSize: "10px" }}
                  width={60}
                />
                <XAxis 
                  dataKey={dataKey}
                  axisLine={true}
                  interval={0}
                  type="number"
                  fontSize={10}
                />
              </>
            ) : (
              <>
                <XAxis
                  dataKey="label"
                  type="category"
                  tickLine={false}
                  tickMargin={3}
                  axisLine={true}
                  interval={0}
                  fontSize={10}
                  tickFormatter={(value) =>
                    chartConfig[value as keyof typeof chartConfig]?.label
                  }
                />
                <YAxis
                  dataKey={dataKey}
                  axisLine={true}
                  interval={0}
                  type="number" 
                  className="text-body"
                  tickMargin={10}
                  fontSize={10}
                  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} />}
                />
              </>
            )}
            
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent  nameKey={namekeys} hideLabel />}
            />
            {showLegend && (
              <ChartLegend
                content={<ChartLegendContent verticalAlign="bottom"  nameKey={namekeys}/>}
                className="-translate-y-2 flex-wrap gap-2 [&>*]:basis-1/4 [&>*]:justify-center"
              />
            )}
            <Bar 
              dataKey={dataKey}
              layout={isHorizontal ? "vertical" : "horizontal"}
              radius={4}
              fill={barColor}
              onClick={(data) => onBarClick?.(data)}
            >
              <LabelList 
                dataKey={dataKey}
                position={position}
                style={{ fontSize: "8px", fill: "#000", backgroundColor:"#fff"}}
                formatter={formatLabel}
              />
            </Bar>
          </BarChart>
          ):(
            <div className="flex items-center justify-center h-full">
            <span className="text-sm dark:text-white">No Data Found !</span>
          </div>)}

        </ChartContainer>
      )}
      </div>
      </div>
      </CardContent>
    </Card>
  )
}
export default HorizontalVerticalBarChart;
