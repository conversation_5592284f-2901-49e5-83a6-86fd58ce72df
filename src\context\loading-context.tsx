import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';

interface LoadingContextType {
  isLoading: boolean;
  isLoadingMap: Record<string, boolean>;
  startLoading: (key?: string) => void;
  stopLoading: (key?: string) => void;
}

const LoadingContext = createContext<LoadingContextType | null>(null);

export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [globalLoading, setGlobalLoading] = useState(false);
  const [loadingMap, setLoadingMap] = useState<Record<string, boolean>>({});

  const startLoading = useCallback((key?: string) => {
    if (key) {
      setLoadingMap(prev => ({ ...prev, [key]: true }));
    } else {
      setGlobalLoading(true);
    }
  }, []);

  const stopLoading = useCallback((key?: string) => {
    if (key) {
      setLoadingMap(prev => ({ ...prev, [key]: false }));
    } else {
      setGlobalLoading(false);
    }
  }, []);

  const value = useMemo(() => ({
    isLoading: globalLoading,
    isLoadingMap: loadingMap,
    startLoading,
    stopLoading,
  }), [globalLoading, loadingMap, startLoading, stopLoading]);

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};