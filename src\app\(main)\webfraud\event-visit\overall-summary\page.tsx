"use client";
import React, { useEffect, useState, useMemo } from "react";
import KeyValueCard from "@/components/mf/keyvalueCard";
import ResizableTable from "@/components/mf/TableComponent";
import DonutChart from "@/components/mf/DonutChart";
import StackedBarWithLine from "@/components/mf/StackedBarwithLine";
import { useCallback, useRef } from "react";
import domToImage from "dom-to-image";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import HeaderRow from "@/components/mf/HeaderRow";
import ChartBarStacked from "@/components/mf/stackedBarChart";
import { onExpand, downloadURI,debounce } from "@/lib/utils";
import { generateChartConfig, ChartData } from "@/lib/chartutils";
import Endpoint from "../../common/endpoint";
import { Loader2 } from "lucide-react";
import { useApiCall } from "../../queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { Filter } from "@/components/mf/Filters";
import { useLoading } from '@/components/mf/LoadingContext';

interface FilterItem {   
  label: string;
  checked: boolean;
}

interface FilterState {
  filters: FilterItem[];
  is_select_all: boolean;
  selected_count: number;
  loading: boolean;
}

interface FilterPayload {
  [key: string]: FilterState;
}

//color
interface ChartConfig {
  [key: string]: { label: string; color: string };
}

interface FraudCategory {
  color: string;
  label: string;
}

interface ColorConfig {
  [key: string]: { label: string; color: string };
  Invalid: FraudCategory;
  Valid: FraudCategory;
  DeviceRepetition: FraudCategory;
  IPRepeat: FraudCategory;
  ImperceptibleWindow: FraudCategory;
  DeviceSpoofing: FraudCategory;
  DeprecatedOS: FraudCategory;
  BrandBidding: FraudCategory;
  DeprecatedBrowser: FraudCategory;
  IPRepeatHourly: FraudCategory;
  ServerFarm: FraudCategory;
  PopUnder: FraudCategory;
  FakeDevice: FraudCategory;
  VPNProxy: FraudCategory;
  GeoFraud: FraudCategory;
  TimezoneMismatch: FraudCategory;
  DuplicateUser: FraudCategory;
  DistributionFraud: FraudCategory;
  BlacklistedUA: FraudCategory;
  ClickSpamming: FraudCategory;
  MouseClickPattern: FraudCategory;
  MouseMovementPattern: FraudCategory;
  DomainSpoofing: FraudCategory;
  AdStacking: FraudCategory;
  PlacementFraud: FraudCategory;
  SuspiciousDeviceRepetition: FraudCategory;
  SuspiciousBrowser: FraudCategory;
  SuspiciousOS: FraudCategory;
  NotFraud: FraudCategory;
  Cookiestuffing: FraudCategory;
  Clickinjection: FraudCategory;
  StackedClick: FraudCategory;
  PunchLead: FraudCategory;
  FakeEvent: FraudCategory;
  BotEvent: FraudCategory;
  GeoHopFraud: FraudCategory;
}

interface ConfigData {
  _id: string;
  ColorConfig: ColorConfig;
}

//Top 5 Campaign
interface ColumnC {
  title: string;
  key: keyof Datac;
}
interface Datac {
  campaign_id: string;
  total_visits: number;
  visit_invalid_percent: number;
  total_events: number;
  event_invalid_percent: number;
}

interface campaignData {
  data: Datac[];
  total_records: number;
  page_number: number;
  limit: number;
  total_pages: number;
  search_term: string;
  package_name: string;
  publisher: string[];
  sub_publisher: string[];
  campaign: string[];
  channel: string[];
}

const CampaignColumns: ColumnC[] = [
  { title: "Campaign ID", key: "campaign_id" },
  { title: "Total Visits", key: "total_visits" },
  { title: "Invalid Visit %", key: "visit_invalid_percent" },
  { title: "Total Events", key: "total_events" },
  { title: "Invalid Event %", key: "event_invalid_percent" },
];

//Top 5 Sources
interface ColumnS {
  title: string;
  key: keyof DataP;
}
interface DataP {
  publisher_name: string;
  total_visits: number;
  visit_invalid_percent: string;
  total_events: number;
  event_invalid_percent: string;
  valid_conv_rate: string;
  invalid_conv_rate: string;
}

interface PublisherData {
  data: DataP[];
  total_records: number;
  page_number: number;
  limit: number;
  total_pages: number;
  search_term: string;
  package_name: string;
  publisher: string[];
  sub_publisher: string[];
  campaign: string[];
  channel: string[];
}

const PublisherColumns: ColumnS[] = [
  { title: "Source", key: "publisher_name" },
  { title: "Total Visits", key: "total_visits" },
  { title: "Invalid Visit %", key: "visit_invalid_percent" },
  { title: "Total Events", key: "total_events" },
  { title: "Invalid Event %", key: "event_invalid_percent" },
  { title: "Valid Traffic Conv. Rate", key: "valid_conv_rate" },
  { title: "Invalid Traffic Conv. Rate", key: "invalid_conv_rate" },
];
//traffic count
interface TrafficEvents {
  total_traffic: number;
  valid_traffic: number;
  invalid_traffic: number;
  valid_traffic_percent: string;
  invalid_traffic_percent: string;
}

interface TrafficVisits {
  total_traffic: number;
  valid_traffic: number;
  invalid_traffic: number;
  valid_traffic_percent: string;
  invalid_traffic_percent: string;
}

interface TrafficComparison {
  valid_conversion_rate: string;
  invalied_conversion_rate: string;
  conversion_rate: string;
}

interface TrafficData {
  events: TrafficEvents;
  visits: TrafficVisits;
  comparision: TrafficComparison;
}
//traffic trend
interface TrafficTrendResponse {
  data: {
    week: string;
    date:string;
    month:string;
    year:string;
    invalid_count: number;
    valid_count: number;
    invalid_percent: string;
  }[];
}
//visit traffic category
interface FraudDataVisit {
  fraud_sub_category: string;
  total_count: number;
  percentage: string;
}
//event traffic category
interface FraudDataEvent {
  fraud_sub_category: string;
  total_count: number;
  percentage: string;
}

interface vPublishers {
  publisher_name: string;
  fraud_sub_category: string;
  fraud_sub_category_count: string;
}

interface ePublishers {
  publisher_name: string;
  fraud_sub_category: string;
  fraud_sub_category_count: string;
}
// interface VPublisher {
//   [publisher_name: string]: {
//     fraud_sub_categories: {
//       fraud_sub_category: string;
//       fraud_sub_category_count: string;
//     }[];
//   };
// }

// Add type for API response
interface FilterApiResponse {
  data: string[];
  isLoading: boolean;
}

interface PublisherTrafficItem {
  publisher_name: string;
  fraud_sub_category: string;
  fraud_sub_category_count: string;
}

interface GroupedPublisherData {
  label: string;
  fraud_sub_categories: { [key: string]: string };
}

interface ChartDataEntry {
  label?: string;
  [key: string]: string | number | undefined;
}

const Dashboard = () => {
  const { selectedPackage } = usePackage();
  const { startDate, endDate } = useDateRange();
  const [selectedType, setSelectedType] = useState<string>("");
  const [selectedFrequencyV, setSelectedFrequencyV] = useState("date");
  const [selectedFrequencyE, setSelectedFrequencyE] = useState("date");
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
const cardRefs = useRef<HTMLElement[]>([]);
  const [cards, setCards] = useState<any>(null);
  const [existingvisit, setExistingvisit] = useState<any>([]);
  const [Existingevent, setExistingevent] = useState<any>([]);
  const [existingCampaign, setExistingCampaign] = useState<any>([]);
  const [existingPublisher, setExistingPublisher] = useState<any>([]);
  const [ExistingVpublisher, setExistingvPublisher] = useState<any>([]);
  const [ExistingEpublisher, setExistingEPublisher] = useState<any>([]);
  const [existingVtrend, setExistingVtrend] = useState<any>([]);
  const [existingEtrend, setExistingEtrend] = useState<any>([]);
  const [currentPagep, setCurrentPagep] = useState(1);
  const [currentPagec, setCurrentPagec] = useState(1);
  const [limitps, setLimitp] = useState(10);
  const [limitpc, setLimitc] = useState(10);
  const [colorConfig, setColorConfig] = useState<ChartConfig>({});
  const [chartConfigs, setChartConfig] = useState<ChartConfig>({});
  const [chartConfigvp, setChartConfigvp] = useState<any>({});
  const [chartConfigep, setChartConfigep] = useState<any>({});
  const [chartConfigVisit, setChartConfigVisit] = useState({});
  const [chartConfigEvent, setChartConfigEvent] = useState({});
  const [chartDatav, setChartDatav] = useState<ChartData[]>([]);
  const [chartDataE, setChartDataE] = useState<ChartData[]>([]);
 // const [isLoading, setIsLoading] = useState(false);
  const [searchTermP, setSearchTermP] = useState("");
  const [searchTermC, setSearchTermC] = useState("");
     const[TotalRecordTP,setTotalRecordTP]=useState();
        const[TotalRecordTC,setTotalRecordTC]=useState();
  const [existingPublisherdata, setExistingPublisherdata] = useState<string[]>(
    []
  );
  const [existingSubPublisherdata, setExistingSubPublisherdata] = useState<
    string[]
  >([]);
  const [existingCampaigndata, setExistingCampaigndata] = useState<string[]>(
    []
  );
  const [existingChanneldata, setExistingChanneldata] = useState<string[]>([]);
  const [existingEventTypedata, setExistingEventTypedata] = useState<string[]>(
    []
  );
  const [loadedFilter, setLoadedFilter] = useState<any>({});
  const loading = useLoading();
  if (!loading) {
    throw new Error('Loading context not found');
  }
  const { isLoading, startLoading, stopLoading } = loading;
  const [query, setQuery] = useState({
    publishers: ["all"],
    sub_publishers: ["all"],
    campaigns: ["all"],
    channels: ["all"],
    event_types: ["all"],
  });


  //  const isInitialLoad = useRef(true); // Track initial load

  // Publishers Filter API
  const publishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingPublisherdata(data.data);
      if (data.data.length > 0) {
       stopLoading();
      }
    },
    onError: (error) => {
      // console.error("Publishers Filter Error:", error);
    },
  });

  // Sub Publishers Filter API
  const subPublishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.SUB_PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      // console.log("sucess sub publisher",data);
      setExistingSubPublisherdata(data.data);
      if (data.data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
      // console.error("Sub Publishers Filter Error:", error);
    },
  });

  // Campaigns Filter API
  const campaignsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CAMPAIGNS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingCampaigndata(data.data);
      if (data.data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
    },
  });

  // Channels Filter API
  const channelsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CHANNELS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingChanneldata(data.data);
      if (data.data.length > 0) {
        stopLoading();
      }  
    },
    onError: (error) => {
      // console.error("Channels Filter Error:", error);
    },
  });

  // Event Type Filter API
  const eventTypeFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.EVENT_TYPE,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingEventTypedata(data.data);
      if (data.data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
      // console.error("Event Type Filter Error:", error);
    },
  });

  // step 2

  const filter = React.useMemo(
    () => ({
      Publishers: {
        filters:
          existingPublisherdata?.map((publisher: string) => ({
            label: publisher,
            // Change: Only check if it exists in the current selection
            checked: query.publishers?.includes("all") || 
                   query.publishers?.includes(publisher) || 
                   !query.publishers, // Default true if no selection exists
          })) || [],
        // Change: Determine if all are selected
        is_select_all: !query.publishers || 
                     query.publishers.includes("all") ||
                     query.publishers?.length === existingPublisherdata?.length,
        // Change: Actual selected count
        selected_count: query.publishers?.includes("all") 
                      ? existingPublisherdata?.length ?? 0
                      : query.publishers?.length ?? existingPublisherdata?.length ?? 0,
        loading: false,
      },
      "Sub Publishers": {
        filters:
          existingSubPublisherdata?.map((subPublisher: string) => ({
            label: subPublisher,
            checked: query.sub_publishers?.includes("all") || 
                   query.sub_publishers?.includes(subPublisher) || 
                   !query.sub_publishers,
          })) || [],
        is_select_all: !query.sub_publishers || 
                     query.sub_publishers.includes("all") ||
                     query.sub_publishers?.length === existingSubPublisherdata?.length,
        selected_count: query.sub_publishers?.includes("all")
                      ? existingSubPublisherdata?.length ?? 0
                      : query.sub_publishers?.length ?? existingSubPublisherdata?.length ?? 0,
        loading: false,
      },
      Campaigns: {
        filters:
          existingCampaigndata?.map((campaign: string) => ({
            label: campaign,
            checked: query.campaigns?.includes("all") || 
                   query.campaigns?.includes(campaign) || 
                   !query.campaigns,
          })) || [],
        is_select_all: !query.campaigns || 
                     query.campaigns.includes("all") ||
                     query.campaigns?.length === existingCampaigndata?.length,
        selected_count: query.campaigns?.includes("all")
                      ? existingCampaigndata?.length ?? 0
                      : query.campaigns?.length ?? existingCampaigndata?.length ?? 0,
        loading: false,
      },
      Channels: {
        filters:
          existingChanneldata?.map((channel: string) => ({
            label: channel,
            checked: query.channels?.includes("all") || 
                   query.channels?.includes(channel) || 
                   !query.channels,
          })) || [],
        is_select_all: !query.channels || 
                     query.channels.includes("all") ||
                     query.channels?.length === existingChanneldata?.length,
        selected_count: query.channels?.includes("all")
                      ? existingChanneldata?.length ?? 0
                      : query.channels?.length ?? existingChanneldata?.length ?? 0,
        loading: false,
      },
      "Event Type": {
        filters:
          existingEventTypedata?.map((eventType: string) => ({
            label: eventType,
            checked: query.event_types?.includes("all") || 
                   query.event_types?.includes(eventType) || 
                   !query.event_types,
          })) || [],
        is_select_all: !query.event_types || 
                     query.event_types.includes("all") ||
                     query.event_types?.length === existingEventTypedata?.length,
        selected_count: query.event_types?.includes("all")
                      ? existingEventTypedata?.length ?? 0
                      : query.event_types?.length ?? existingEventTypedata?.length ?? 0,
        loading: false,
      },
    }),
    [
      existingPublisherdata,
      existingSubPublisherdata,
      existingCampaigndata,
      existingChanneldata,
      query.publishers,
      query.sub_publishers,
      query.campaigns,
      query.channels,
      query.event_types,
    ]
  );

  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];

      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs]
  );

  //color Api
  const colorApi = useApiCall<ConfigData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.COLOR_API,
    method: "GET",
    params: {},
    onSuccess: (data) => {
      // console.log("COLORS Success:", data);
      setColorConfig(data.ColorConfig);
    },
    onError: (error) => {
      // console.error("Colors Error:", error);
    },
  });

  useEffect(() => {
    if (colorApi.result?.data) {
      const response = colorApi.result?.data.ColorConfig;
    }
  }, [colorApi.result?.data]);
  const chartConfig = useMemo(() => {
    if (!colorConfig) return {};

    // Map colorConfig to chartConfig format
    const config: ChartConfig = {};
    for (const key in colorConfig) {
      if (colorConfig.hasOwnProperty(key)) {
        config[key] = {
          label: colorConfig[key].label,
          color: colorConfig[key].color,
        };
      }
    }
    return config;
  }, [colorConfig]);

  // Trigger API call for card values

  const trafficountApi = useApiCall<TrafficData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TRAFFIC_COUNT,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      // console.log("Traffic Cards Success:", data);
      stopLoading();
    },
    onError: (error) => {
      // console.error("Traffic Cards Error:", error);
    },
  });

  useEffect(() => {
    if (trafficountApi.type === "mutation" && trafficountApi.result.data) {
      const { events, visits, comparision } = trafficountApi.result.data;
      const updatedData = {
        events: {
          total_traffic: events.total_traffic,
          valid_traffic: events.valid_traffic,
          invalid_traffic: events.invalid_traffic,
          valid_traffic_percent: events.valid_traffic_percent,
          invalid_traffic_percent: events.invalid_traffic_percent,
        },
        visits: {
          total_traffic: visits.total_traffic,
          valid_traffic: visits.valid_traffic,
          invalid_traffic: visits.invalid_traffic,
          valid_traffic_percent: visits.valid_traffic_percent,
          invalid_traffic_percent: visits.invalid_traffic_percent,
        },
        comparision: {
          valid_conversion_rate: comparision.valid_conversion_rate,
          invalied_conversion_rate: comparision.invalied_conversion_rate,
          conversion_rate: comparision.conversion_rate,
        },
      };
      if (JSON.stringify(updatedData) !== JSON.stringify(cards)) {
        setCards(updatedData);
      }
    }
  }, [trafficountApi]);

  const stats = [
    {
      title: "Total Traffic",
      leftKey: "Visit :",
      leftValue: cards?.visits?.total_traffic,
      colors: "#490080",
      rightKey: "Event :",
      rightValue: cards?.events?.total_traffic,
      backgroundColor: "dark:bg-card",
    },
    {
      title: "Valid Traffic",
      leftKey: "Visit :",
      leftValue: cards?.visits?.valid_traffic,
      leftpercentage: cards?.visits?.valid_traffic_percent,
      percentage: `(${cards?.visits?.valid_traffic_percent})`,
      colors: "#00A86B",
      rightKey: "Event :",
      rightValue: cards?.events?.valid_traffic,
      percentage1: `(${cards?.events?.valid_traffic_percent})`,
      backgroundColor: "dark:bg-card",
    },
    {
      title: "Invalid Traffic",
      leftKey: "Visit :",
      leftValue: cards?.visits?.invalid_traffic,
      percentage: `(${cards?.visits?.invalid_traffic_percent})`,
      colors: "#FF0000",
      rightKey: "Event :",
      rightValue: cards?.events?.invalid_traffic,
      percentage1: `(${cards?.events?.invalid_traffic_percent})`,
      backgroundColor: "dark:bg-card",
    },
    {
      title: "Conversion Rate",
      leftKey: "Valid Traffic :",
      percentage: cards?.comparision?.valid_conversion_rate,
      colors: "#490080",
      rightKey: "Invalid Traffic :",
      percentage1: cards?.comparision?.invalied_conversion_rate,
      backgroundColor: "dark:bg-card",
    },
    {
      title: "Conv. Rate (Valid to Invalid Delta)",
      leftSide: cards?.comparision?.conversion_rate,
      colors: "#00A86B",
      backgroundColor: "dark:bg-card",
    },
  ];
  const visitEventOptions = [
    { value: "visit", label: "Visit" },
    { value: "event", label: "Event" },
  ];

  const handleTypeChange = (value: string) => {
    setSelectedType(value);
    // console.log("Selected type:", value)
  };
  const selectOptionsV = ["Daily", "Weekly", "Monthly","Yearly"];

  const handleFrequencyChangeV = (value: string) => {
    // Convert frequency to the appropriate format
    const frequencyMap: { [key: string]: string } = {
        Daily: "date",
        Weekly: "week",
        Monthly: "month",
        Yearly: "year",
    };
    setSelectedFrequencyV(frequencyMap[value]);
};
const selectOptionsE = ["Daily", "Weekly", "Monthly","Yearly"];

  const handleFrequencyChangeE = (value: string) => {
    // Convert frequency to the appropriate format
    const frequencyMap: { [key: string]: string } = {
        Daily: "date",
        Weekly: "week",
        Monthly: "month",
        Yearly: "year",
    };
    setSelectedFrequencyE(frequencyMap[value]);
};

  //traffic  visit trend api
  const trafficTrendVisitApi = useApiCall<TrafficTrendResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TRAFFIC_TRENDS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      summary_type: "visit",
      frequency: selectedFrequencyV,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    }, 
    onSuccess: (data) => {
      // console.log("Traffic Trend Success:", data);
      if (
        Array.isArray(trafficTrendVisitApi.result?.data)
      ) {
        const updatedTrend = trafficTrendVisitApi.result?.data.map((trendItem) => {
          let timePeriod = "";
        if (selectedFrequencyV === "month") {
          timePeriod = trendItem.month || ""; 
        } else if (selectedFrequencyV === "week") {
          timePeriod = trendItem.week || ""; 
        } else if (selectedFrequencyV === "date") {
          timePeriod = trendItem.date || "";
        } else if (selectedFrequencyV === "year") {
          timePeriod = trendItem.year || ""; 
        }
    
          return {
            month: timePeriod, // Dynamically set timePeriod based on frequency
            Invalid: trendItem.invalid_count,
            Valid: trendItem.valid_count,
            "Invalid %": parseFloat(trendItem.invalid_percent.replace("%", "")),
          }
          });
        if (JSON.stringify(updatedTrend) !== JSON.stringify(existingVtrend)) {
          setExistingVtrend(updatedTrend);
  
          // Define required keys and their fallback colors
          const requiredKeys = [
            { key: "Invalid", fallbackColor: "#FF0000" },
            { key: "Valid", fallbackColor: "#00A86B" },
            { key: "Invalid %", fallbackColor: "#b91c1c"}, // Use Invalid's color
          ];
  
          // Create chartConfig dynamically
          const chartConfig: ChartConfig = requiredKeys.reduce(
            (acc, { key, fallbackColor }) => {
              acc[key] = {
                label: key,
                color:fallbackColor,
                  // colorConfig[key === "Invalid %" ? "Invalid" : key]?.fallbackColor, ||""
                  
              };
              return acc;
            },
            {} as ChartConfig
          );
          setChartConfig(chartConfig);
        }
      }
      stopLoading();
    },
    onError: (error) => {
      // console.error("Traffic Trend Error:", error);
    },
  });

   //traffic  event trend api
   const trafficTrendEventApi = useApiCall<TrafficTrendResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TRAFFIC_TRENDS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      summary_type: "event",
      frequency: selectedFrequencyE,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    }, 
    onSuccess: (data) => {
      // console.log("Traffic Trend Success:", data);
      if (
        Array.isArray(trafficTrendEventApi.result?.data)
      ) {
        const updatedTrend = trafficTrendEventApi.result?.data.map((trendItem) => {
          let timePeriod = "";
        if (selectedFrequencyE === "month") {
          timePeriod = trendItem.month || ""; 
        } else if (selectedFrequencyE === "week") {
          timePeriod = trendItem.week || ""; 
        } else if (selectedFrequencyE === "date") {
          timePeriod = trendItem.date || "";
        } else if (selectedFrequencyE === "year") {
          timePeriod = trendItem.year || ""; 
        }
    
          return {
            month: timePeriod, // Dynamically set timePeriod based on frequency
            Invalid: trendItem.invalid_count,
            Valid: trendItem.valid_count,
            "Invalid %": parseFloat(trendItem.invalid_percent.replace("%", "")),
          }
          });
        if (JSON.stringify(updatedTrend) !== JSON.stringify(existingEtrend)) {
          setExistingEtrend(updatedTrend);
  
          // Define required keys and their fallback colors
          const requiredKeys = [
            { key: "Invalid", fallbackColor: "#FF0000" },
            { key: "Valid", fallbackColor: "#00A86B" },
            { key: "Invalid %", fallbackColor: "#b91c1c"}, // Use Invalid's color
          ];
  
          // Create chartConfig dynamically
          const chartConfig: ChartConfig = requiredKeys.reduce(
            (acc, { key, fallbackColor }) => {
              acc[key] = {
                label: key,
                color:fallbackColor,
                  // colorConfig[key === "Invalid %" ? "Invalid" : key]?.fallbackColor, ||""
                  
              };
              return acc;
            },
            {} as ChartConfig
          );
          setChartConfig(chartConfig);
        }
      }
      stopLoading();
    },
    onError: (error) => {
      // console.error("Traffic Trend Error:", error);
    },
  });

 
  //trigger api for visit Traffic

  const visitTrafficApi = useApiCall<FraudDataVisit[]>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.VISIT_TRAFFIC,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      // console.log("Visit Traffic Success:", data);
      setExistingvisit(data);
   
    
    },
    onError: (error) => {
      // console.error("Visit Traffic Error:", error);
      stopLoading();
    },
  });

  useEffect(() => {
    if (existingvisit.length > 0 && colorConfig) {
      const { chartData, chartConfig } = generateChartConfig(
        existingvisit,
        colorConfig
      );
      setChartDatav(chartData); // Set the generated chart data
      setChartConfigVisit(chartConfig); // Set the generated chart config
    }
  }, [ existingvisit, colorConfig]);

  //trigger api for event Traffic
  const eventTrafficApi = useApiCall<FraudDataEvent[]>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.EVENT_TRAFFIC,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      // console.log("Event Traffic Success:", data);
      setExistingevent(data);
    },
    onError: (error) => {
      // console.error("Event Traffic Error:", error);
    },
  });
  useEffect(() => {
    if (Existingevent.length > 0 && colorConfig) {
      const { chartData, chartConfig } = generateChartConfig(
        Existingevent,
        colorConfig
      );
      setChartDataE(chartData);
      setChartConfigEvent(chartConfig);
    }
  }, [Existingevent, colorConfig]);

  //top 5 publisher

  const topPublisherApi = useApiCall<PublisherData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TOP_PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      page: currentPagep,
      limit: limitps,
      search_term: searchTermP,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      setTotalRecordTP(data.total_pages as any);
      stopLoading();
    },
    onError: (error) => {
    },
  });
  useEffect(() => {
    const response = topPublisherApi.result?.data?.data;
    if (
      topPublisherApi.type === "mutation" &&
      response &&
      Array.isArray(response)
    ) {
      const updatedtop = response.map((topItem: any) => ({
        publisher_name: topItem.publisher_name || "Uncategorized",
        total_visits: topItem.total_visits, // Keep as number
        visit_invalid_percent: topItem.visit_invalid_percent,
        total_events: topItem.total_events, // Keep as number
        event_invalid_percent: topItem.event_invalid_percent,
        valid_conv_rate: topItem.valid_conv_rate,
        invalid_conv_rate: topItem.invalid_conv_rate,
      }));
      if (JSON.stringify(updatedtop) !== JSON.stringify(existingPublisher)) {
        setExistingPublisher(updatedtop);
      }
    }
  }, [topPublisherApi,existingPublisher]);
  // const total_pagesp = topPublisherApi.result?.data?.total_pages;

  //top 5 campaign

  const topCampaignApi = useApiCall<campaignData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TOP_CAMPAIGNS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      page: currentPagec,
      limit: limitpc,
      search_term: searchTermC,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      // console.log("Top Campaigns Success:", data);
      setTotalRecordTC(data.total_pages as any);
      stopLoading();
    },
    onError: (error) => {
      // console.error("Top Campaigns Error:", error);
      // setIsLoading(false);
    },
  });

  useEffect(() => {
    const response = topCampaignApi.result?.data?.data;
    if (
      topCampaignApi.type === "mutation" &&
      response &&
      Array.isArray(response)
    ) {
      const updatedtop = response.map((topItem: any) => ({
        campaign_id: topItem.campaign_id,
        total_visits: topItem.total_visits,
        visit_invalid_percent: topItem.visit_invalid_percent,
        total_events: topItem.total_events,
        event_invalid_percent: topItem.event_invalid_percent,
      }));
      if (JSON.stringify(updatedtop) !== JSON.stringify(existingCampaign)) {
        setExistingCampaign(updatedtop);
      }
    }
  }, [topPublisherApi,existingCampaign]);


  //visit Publisher
  const vPublisherApi = useApiCall<vPublishers>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.VISIT_TRAFFIC_PUBLISHER,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      // console.log("Visit Publisher Success:", data);
    },
    onError: (error) => {
      // console.error("Visit Publisher Error:", error);
      stopLoading();
    },
  });

  useEffect(() => {
    const response = vPublisherApi.result?.data;
    if (
      vPublisherApi.type === "mutation" &&
      response &&
      Array.isArray(response)
    ) {
      let updatedData: GroupedPublisherData[] = [];
      const groupedByPublisher = (response as PublisherTrafficItem[]).reduce((acc: { [key: string]: GroupedPublisherData }, item) => {
        const { publisher_name, fraud_sub_category, fraud_sub_category_count } = item;
        if (!acc[publisher_name]) {
          acc[publisher_name] = {
            label: publisher_name,
            fraud_sub_categories: {},
          };
        }
        acc[publisher_name].fraud_sub_categories[fraud_sub_category] = fraud_sub_category_count;
        return acc;
      }, {});
  
      // Here, we remove the 20 entry limit and take all the data from groupedByPublisher
      let updatedPublishers = Object.values(groupedByPublisher);

      // Check if the label exists in the updatedPublishers (no limit applied)
      updatedData = updatedPublishers; // Include all publishers without limit

      // Map data into the desired chart format
      const chartData: ChartDataEntry[] = updatedData.map((publisher) => {
        const dataEntry: ChartDataEntry = { label: publisher.label };
        Object.entries(publisher.fraud_sub_categories).forEach(([category, count]) => {
          const normalizedCategory = category.replace(" ", "");
          const numericCount = parseFloat((count as string)?.replace("%", "").trim());
          dataEntry[normalizedCategory] = numericCount;
        });
        return dataEntry;
      });
  
      // Update state if the data has changed
      if (JSON.stringify(chartData) !== JSON.stringify(ExistingVpublisher)) {
        setExistingvPublisher(chartData);
      }
  
      // Update chart configuration for fraud categories
      const updatedChartConfig: ChartConfig = {};
      Object.entries(groupedByPublisher).forEach(([publisherName, publisher]) => {
        const { fraud_sub_categories } = publisher;
        Object.entries(fraud_sub_categories).forEach(([category, count]) => {
          const normalizedCategory = category.replace(/\s+/g, "");
          const color = colorConfig[normalizedCategory]?.color || "#000000";
          updatedChartConfig[normalizedCategory] = {
            label: category,
            color: color,
          };
        });
      });
  
      if (
        JSON.stringify(updatedChartConfig) !== JSON.stringify(chartConfigvp)
      ) {
        setChartConfigvp(updatedChartConfig);
      }
    }
  }, [vPublisherApi,ExistingVpublisher, colorConfig]);

  //EVENT Publisher
  const ePublisherApi = useApiCall<ePublishers>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.EVENT_TRAFFIC_PUBLISHER,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (data) => {
      // console.log("Event Publisher Success:", data);
    },
    onError: (error) => {
      // console.error("Event Publisher Error:", error);
      stopLoading();
    },
  });

  useEffect(() => {
    const response = ePublisherApi.result?.data;
    if (
      ePublisherApi.type === "mutation" &&
      response &&
      Array.isArray(response)
    ) {
      const groupedByPublisher = (response as PublisherTrafficItem[]).reduce((acc: { [key: string]: GroupedPublisherData }, item) => {
        const { publisher_name, fraud_sub_category, fraud_sub_category_count } =
          item;
        if (!acc[publisher_name]) {
          acc[publisher_name] = {
            label: publisher_name,
            fraud_sub_categories: {},
          };
        }
        acc[publisher_name].fraud_sub_categories[fraud_sub_category] =
          fraud_sub_category_count;

        return acc;
      }, {});

      const chartData: ChartDataEntry[] = Object.values(groupedByPublisher).map((publisher) => {
        const dataEntry: ChartDataEntry = { label: publisher.label };
        Object.entries(publisher.fraud_sub_categories).forEach(
          ([category, count]) => {
            const normalizedCategory = category.replace(" ", ""); // Remove all spaces
            const numericCount = parseFloat(count.replace("%", "").trim()); // Remove '%' and convert to float
            dataEntry[normalizedCategory] = numericCount;
          }
        );
        return dataEntry;
      });
      if (JSON.stringify(chartData) !== JSON.stringify(ExistingEpublisher)) {
        setExistingEPublisher(chartData);
      }

      const updatedChartConfig: ChartConfig = {};
      Object.entries(groupedByPublisher).forEach(([publisherName, publisher]) => {
        const { fraud_sub_categories } = publisher;
        Object.entries(fraud_sub_categories).forEach(([category, count]) => {
          const normalizedCategory = category.replace(" ", "");
          const color = colorConfig[normalizedCategory]?.color || "#000000";

          updatedChartConfig[normalizedCategory] = {
            label: category,
            color: color,
          };
        });
      });

      if (
        JSON.stringify(updatedChartConfig) !== JSON.stringify(chartConfigep)
      ) {
        setChartConfigep(updatedChartConfig);
      }
    }
  }, [ePublisherApi, ExistingEpublisher,colorConfig]);

 //fetch traffic trend api
 const fetchtrafficVisitTrend = useCallback(() => {
  if (trafficTrendVisitApi.type === "mutation") {
    startLoading();
    trafficTrendVisitApi.result.mutate({});} 
}, [trafficTrendVisitApi])

 //fetch traffic  event trend api
 const fetchtrafficEventTrend = useCallback(() => {
  if (trafficTrendEventApi.type === "mutation") {
    startLoading();
    trafficTrendEventApi.result.mutate({});} 
}, [trafficTrendEventApi])
  //fetch top publisher api
   const fetchTopPublisher = useCallback(()=>{
    if (topPublisherApi.type === "mutation") {       
      stopLoading();
      setExistingPublisher([]);
      topPublisherApi.result.mutate({});}
   },[topPublisherApi,])
   const debouncedFetchTopPublisher = useCallback(debounce(fetchTopPublisher, 500), []);

   //fetch top campaign api
   const fetchTopCampaign = useCallback(()=>{
       stopLoading();
       setExistingCampaign([]);
    if (topCampaignApi.type === "mutation"){ 
      topCampaignApi.result.mutate({});
    }
   },[topPublisherApi])
   const debouncedFetchTopCampaign = useCallback(debounce(fetchTopCampaign, 500), []);

    // Add this new function to fetch all APIs
  const fetchTCData = useCallback(() => {if (trafficountApi.type === "mutation"){       startLoading();
    trafficountApi.result.mutate({})}},[]);
  const fetchVTData = useCallback(() => { if (visitTrafficApi.type === "mutation"){       startLoading();
    visitTrafficApi.result.mutate({})}},[]);
    const fetchETData = useCallback(() => {if (eventTrafficApi.type === "mutation") {       startLoading();
      eventTrafficApi.result.mutate({})}},[]);
      const  fetchVPData= useCallback(() => {if (vPublisherApi.type === "mutation") {       startLoading();
        vPublisherApi.result.mutate({})}},[]);
        const  fetchEPData= useCallback(() => {if (ePublisherApi.type === "mutation") {       startLoading();
          ePublisherApi.result.mutate({})}},[]);
          const fetchCData = useCallback(() => { if (colorApi.type === "query") {colorApi.result.refetch()}},[]);

          //Filters
       const fetchPublisher = useCallback(() => {if (publishersFilterApi.type === "mutation"){publishersFilterApi.result.mutate({})}},[]);
       const fetchSubPublisher = useCallback(() => {if (subPublishersFilterApi.type === "mutation"){subPublishersFilterApi.result.mutate({})}},[]);
         const fetchCampaign = useCallback(() => {if (campaignsFilterApi.type === "mutation"){campaignsFilterApi.result.mutate({})}},[]);
         const fetchChannel = useCallback(() => { if (channelsFilterApi.type === "mutation"){channelsFilterApi.result.mutate({})}},[]);
         const fetchEventType = useCallback(() => { if (eventTypeFilterApi.type === "mutation"){eventTypeFilterApi.result.mutate({})}},[]);

  const yAxisConfigE = {
    dataKey: "label",
    title: "Event Publisher Name",
  };
  const yAxisConfigV = {
    dataKey: "label",
    title: "Visit Publisher Name",
  };

  const xAxisConfigstack = {
    dataKey: "value",
    title: "Fraud Category",
    tickFormatter: (value: number | string) => `${value}%`,
  };

  const handleExpand = (index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  };

  
 const deepEqual = (arr1: any[], arr2: any[]) => {
   if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
   if (arr1.length !== arr2.length) return false;
   return arr1.every((item, index) => item.checked === arr2[index].checked && item.label === arr2[index].label);
 };
   // Update the handleFilterChange function
   const handleFilterChange = useCallback(
     async (newState: Record<string, any>) => {
      startLoading();

       const payload = {
         publishers: newState.Publishers?.is_select_all
           ? ['all']
           : newState.Publishers?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         sub_publishers: newState['Sub Publishers']?.is_select_all
           ? ['all']
           : newState['Sub Publishers']?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         campaigns: newState.Campaigns?.is_select_all
           ? ['all']
           : newState.Campaigns?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         channels: newState.Channels?.is_select_all
           ? ['all']
           : newState.Channels?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         event_types: newState['Event Type']?.is_select_all
           ? ['all']
           : newState['Event Type']?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
       };
   
       setQuery(payload);
       const filtersChanged =
       !deepEqual(newState.Publishers?.filters || [], loadedFilter.Publishers?.filters || []) ||
       !deepEqual(newState['Sub Publishers']?.filters || [], loadedFilter['Sub Publishers']?.filters || []) ||
       !deepEqual(newState.Campaigns?.filters || [], loadedFilter.Campaigns?.filters || []) ||
       !deepEqual(newState.Channels?.filters || [], loadedFilter.Channels?.filters || []) ||
       !deepEqual(newState['Event Type']?.filters || [], loadedFilter['Event Type']?.filters || []);

     if (filtersChanged) {
       setLoadedFilter(newState);
     }
   },
   [loadedFilter]
   );   

   useEffect(() => {
    if (selectedFrequencyE) {
      startLoading();
      fetchtrafficEventTrend();
    }
    }, [selectedFrequencyE]);

  useEffect(() => {
    if (selectedFrequencyV) {
      startLoading();
      fetchtrafficVisitTrend();
    }
    }, [selectedFrequencyV]);
    
  const fetchAllData = useCallback(() => {
    startLoading();
    fetchPublisher(); 
    fetchSubPublisher();
    fetchCampaign();
    fetchChannel();
    fetchEventType();
    fetchtrafficVisitTrend();
    fetchtrafficEventTrend();
    fetchTopPublisher();
    fetchTopCampaign();
    fetchTCData();
    fetchCData();
    fetchEPData();
    fetchVPData();
    fetchETData();
    fetchVTData();
  }, [fetchPublisher, fetchSubPublisher, fetchCampaign, fetchChannel, fetchEventType, fetchtrafficVisitTrend, fetchtrafficEventTrend, fetchTopPublisher, fetchTopCampaign, fetchTCData, fetchCData, fetchEPData, fetchVPData, fetchETData, fetchVTData]);
  
  // Handle initial load + changes for selectedPackage, startDate, and endDate
  useEffect(() => {
    if (selectedPackage && startDate && endDate) {
      fetchAllData(); 
    }
  }, [selectedPackage, startDate, endDate]);
  useEffect(() => {
    if (selectedPackage ) {
      fetchAllData(); // Call non-debounced APIs once on initial load
    }
  }, [loadedFilter]);
 
  useEffect(() => {
    if (searchTermC|| limitpc|| currentPagec) {
      startLoading();
      debouncedFetchTopCampaign();
    }
  }, [searchTermC, limitpc, currentPagec]);
  useEffect(() => {
    if (searchTermP|| limitps||currentPagep) {
      startLoading();
      debouncedFetchTopPublisher();
    }
    }, [searchTermP, limitps,currentPagep]);

  return (
    <div className="grid gap-2 w-full">
      <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5 py-2">
        <Filter filter={filter} onChange={handleFilterChange} />
      </div>

      {/* Row 2 */}
      <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 w-full gap-2  min-h-[340px] md:min-h-[300px]">
      <Card className="w-full sm:col-span-1 md:col-span-1 lg:col-span-1 xl:col-span-1 shadow-md rounded-lg bg-white dark:bg-card p-2 h-[300px] overflow-y-auto scrollbar">          {cards ? (
            stats?.map((card, index) => {
              return (
                <div key={index} className="overflow-x-auto scrollbar">
                  <KeyValueCard
                    title={card.title}
                    leftKey={card.leftKey}
                    leftValue={card.leftValue}
                    percentage={card.percentage}
                    leftSide={card.leftSide}
                    colors={card.colors}
                    rightKey={card.rightKey}
                    rightValue={card.rightValue}
                    percentage1={card.percentage1}
                    backgroundColor={card.backgroundColor}
                  />
                </div>
              );
            })
          ) : (
            <div className="flex items-center justify-center min-h-full">
              <Loader2 className=" h-8 w-8 animate-spin text-primary" />
            </div>
          )}
        </Card>

        {/* Chart Section */}
        <Card
    ref={(el) => (cardRefs.current[0] = el!)}
    className="w-full sm:col-span-1 md:col-span-4 lg:col-span-4 xl:col-span-2 shadow-md rounded-lg bg-white dark:bg-card  h-[300px] overflow-hidden"
  >     
   <CardTitle className="p-2 ">
     <HeaderRow
      selectoptions={selectOptionsV}
      handleFrequencyChange={handleFrequencyChangeV} 
      selectedFrequency={selectedFrequencyV}
      title="Visit Traffic Trend"
      onExport={() => onExport("png", "Visit Traffic Trend", 0)}
      onExpand={() => handleExpand(0)}
      isRadioButton={false}
      isSelect={true}
      placeholder="Daily"
    />
     </CardTitle>   
     <CardContent className=" w-full  h-full overflow-y-auto scrollbar ">
      <div className="min-w-[900px] overflow-x-auto scrollbar  h-full">
   <StackedBarWithLine
            // visitEventOptions={visitEventOptions}
            // handleTypeChange={handleTypeChange}
            // selectedType="visit"
            chartData={existingVtrend}
            chartConfig={chartConfigs}
            isHorizontal={false}

            isLoading={isLoading}
            xAxisConfig={{
              dataKey: "month",
              tickLine: false,
              tickMargin: 10,
              axisLine: false,
              tickFormatter: (value: string) => value,
              textAnchor: "middle",
              dy: 10,
            }}
            YAxis1={{
              yAxisId: "left",
              orientation: "left",
              stroke: "hsl(var(--chart-1))",
            }}
            YAxis2={{
              yAxisId: "right",
              orientation: "right",
              stroke: "hsl(var(--chart-3))",
            }} onExpand={function (): void {
              throw new Error("Function not implemented.");
            } }   
                   />
                   </div>
                   </CardContent> 
        </Card>
           {/* Chart Section */}
           <Card
    ref={(el) => (cardRefs.current[1] = el!)}
    className="w-full sm:col-span-1 md:col-span-5 lg:col-span-5 xl:col-span-2 shadow-md rounded-lg bg-white dark:bg-card overflow-hidden h-[300px]"
  >
    <CardTitle className="p-2 ">
     <HeaderRow
      selectoptions={selectOptionsE}
      handleFrequencyChange={handleFrequencyChangeE} 
      selectedFrequency={selectedFrequencyE}
      title="Event Traffic Trend"
      onExport={() => onExport("png", "Eventt Traffic Trend", 1)}
      onExpand={() => handleExpand(1)}
      isRadioButton={false}
      isSelect={true}
      placeholder="Daily"
    />
     </CardTitle>   
     <CardContent className=" w-full  h-full overflow-y-auto scrollbar ">
     <div className="min-w-[900px] overflow-x-auto scrollbar  h-full">
          <StackedBarWithLine
            // visitEventOptions={visitEventOptions}
            // handleTypeChange={handleTypeChange}
            //  selectedType="event"
            chartData={existingEtrend}
            chartConfig={chartConfigs}
            isHorizontal={false}
            placeholder="Daily"
            isLoading={isLoading}
            xAxisConfig={{
              dataKey: "month",
              tickLine: false,
              tickMargin: 10,
              axisLine: false,
              tickFormatter: (value: string) => value,
              textAnchor: "middle",
              dy: 10,
            }}
            YAxis1={{
              yAxisId: "left",
              orientation: "left",
              stroke: "hsl(var(--chart-1))",
            }}
            YAxis2={{
              yAxisId: "right",
              orientation: "right",
              stroke: "hsl(var(--chart-3))",
            }} onExpand={function (): void {
              throw new Error("Function not implemented.");
            } }          />
            </div>
            </CardContent>
        </Card>
      </div>

      {/* Row 2  */}
      <div className="gap-1 w-full">
        <div className="w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2">
          Distribution By Category
        </div>
        <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2  sm:grid-cols-1  w-full gap-2 h-[300px] overflow-hidden">
          <Card
            ref={(el) => (cardRefs.current[2] = el!)}
            className=" w-full shadow-md rounded-none bg-white gap-2  dark:bg-card dark:text-white text-header"
          >
            <CardContent className=" w-full min-h-[300px]">
              <DonutChart
                chartData={chartDatav}
                chartConfig={chartConfigVisit}
                title="Visit Traffic"
                onExport={() => onExport("png", "Visit Traffic",2)}
                onExpand={() => handleExpand(2)}
                // isSelect={true}
                //selectoptions={selectOptions}
                dataKey="visit"
                nameKey="label"
                // placeholder="Weekly"
                isView={true}
                isLoading={isLoading}
                isPercentage={true}
                direction="flex-col"
                isdonut={false}
                marginTop="mt-0"
                position="items-start"
              />
            </CardContent>
          </Card>

          <Card
            ref={(el) => (cardRefs.current[3] = el!)}
            className="w-full shadow-md rounded-none bg-white gap-2 dark:bg-card dark:text-white text-header h-full "
          >
            <CardContent className=" w-full  min-h-[300px]">
              <DonutChart
                chartData={chartDataE}
                chartConfig={chartConfigEvent}
                title="Event Traffic"
                onExport={() => onExport("png", "Event Traffic", 3)}
                onExpand={() => handleExpand(3)}
                // isSelect={true}
                // selectoptions={selectOptions}
                dataKey="visit"
                nameKey="label"
                // placeholder="Weekly"
                isLoading={isLoading}
                isView={true}
                isPercentage={true}
                direction="flex-col"
                isdonut={false}
                marginTop="mt-0"
                position="items-start"
              />
            </CardContent>
          </Card>
        </div>
      </div>

      <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-1 w-full gap-2">
        <Card
          ref={(el) => (cardRefs.current[4] = el!)}
          className="shadow-md rounded-none bg-white gap-2 dark:bg-card dark:text-white p-2 text-header "
        >
          <HeaderRow
            title="Top Sources / Publishers"
            onExport={() => onExport("png", "Top Sources / Publishers", 4)}
            onExpand={() => handleExpand(4)}
            visitEventOptions={visitEventOptions}
            handleTypeChange={handleTypeChange}
            selectedType={selectedType}
            isRadioButton={false}
          />
          <ResizableTable
            isPaginated={true}
            isPause={false}
            isPlay={false}
            columns={PublisherColumns}
            data={isLoading ?[]:existingPublisher}
            isLoading={isLoading}
            setSearchTerm={setSearchTermP}
            SearchTerm={searchTermP}
            headerColor="#DCDCDC"
            height={280}
            isEdit={false}
            isSearchable={true}
            onLimitChange={(newLimit: number) => {
              startLoading();
              setLimitp(newLimit);
              setCurrentPagep(1);
            }}
            onPageChangeP={(newPage: number) => {
              startLoading();
              setCurrentPagep(newPage);
            }}
            pageNo={currentPagep}
            totalPages={TotalRecordTP}
          />
        </Card>
        <Card
          ref={(el) => (cardRefs.current[5] = el!)}
          className="shadow-md rounded-none bg-white gap-4 dark:bg-card dark:text-white p-2 text-header "
        >
          <HeaderRow
            title="Top Campaigns"
            onExport={() => onExport("png", "Top Campaigns", 5)}
            onExpand={() => handleExpand(5)}
            visitEventOptions={visitEventOptions}
            handleTypeChange={handleTypeChange}
            selectedType={selectedType}
            isRadioButton={false}
          />
          <ResizableTable
            isPaginated={true}
            isPause={false}
            isPlay={false}
            columns={CampaignColumns}
            data={isLoading ?[]:existingCampaign}
            isLoading={isLoading}
            headerColor="#DCDCDC"
            height={280}
            isEdit={false}
            isSearchable={true}
            setSearchTerm={setSearchTermC}
            SearchTerm={searchTermC}
            onLimitChange={(newLimit: number) => {
              startLoading();
              setLimitc(newLimit);
              setCurrentPagec(1);
            }}
            onPageChangeP={(newPage: number) => {
              startLoading()
              setCurrentPagec(newPage);
            }}
            pageNo={currentPagec}
            totalPages={TotalRecordTC}
          />
        </Card>
      </div>

      <div className="gap-1 w-full h-[400px]">
        <div className="w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2">
          Distribution By Publishber
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-1  lg:grid-cols-2 gap-2">
          <div>
            <Card
              ref={(el) => (cardRefs.current[6] = el!)}
              className=" shadow-md  rounded-none bg-white gap-2 dark:bg-card dark:text-white p-2 w-full h-[400px] "
            >
              <ChartBarStacked
                chartData={ExistingVpublisher}
                chartConfig={chartConfigvp}
                xAxis={xAxisConfigstack}
                isHorizontal={false}
                title="Visit Traffic"
                onExport={() => onExport("png", "Visit Traffic", 6)}
                onExpand={() => handleExpand(6)}
                visitEventOptions={visitEventOptions}
                handleTypeChange={handleTypeChange}
                selectedType={selectedType}
                isRadioButton={false}
                layoutDirection="flex-col"
                isLegend={false}
                yAxis={yAxisConfigV}
                ischangeLegend={true}
                isLoading={isLoading}
                isCartesian={true}
              />
            </Card>
          </div>
          <div>
            <Card
              ref={(el) => (cardRefs.current[7] = el!)}
              className=" shadow-md  rounded-none bg-white gap-2 dark:bg-card dark:text-white p-2  w-full h-[400px]"
            >
              <ChartBarStacked
                chartData={ExistingEpublisher}
                chartConfig={chartConfigep}
                xAxis={xAxisConfigstack}
                yAxis={yAxisConfigE}
                isHorizontal={false}
                title="Event Traffic"
                onExport={() => onExport("png", "Event Traffic", 7)}
                onExpand={() => handleExpand(7)}
                visitEventOptions={visitEventOptions}
                handleTypeChange={handleTypeChange}
                selectedType={selectedType}
                isRadioButton={false}
                AxisLabel="Value"
                layoutDirection="flex-col"
                isLegend={false}
                ischangeLegend={true}
                isLoading={isLoading}
                isCartesian={true} 
              />
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
