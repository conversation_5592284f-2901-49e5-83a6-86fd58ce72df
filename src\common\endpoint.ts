import { WEB_VITALS } from "next/dist/shared/lib/utils";

const Endpoint = {
  LOGIN: "/dev/api/v1/auth/login/post/",
  SIGN_UP: "/dev/api/v1/auth/sign_up/post/",
  SIGN_OUT: "/dev/api/v1/auth/signout/post/",
  CHANGE_PASSWORD: "/dev/api/v1/auth/change_password/post/",
  FORGOT_PASSWORD: "/dev/api/v1/auth/forgot_password/post/",
  CONFIRM_FORGOT_PASSWORD: "/dev/api/v1/auth/confirm_forgot_password/post/",
  VERIFY_OTP: "/dev/api/v1/auth/verify_otp/post/",
  RESEND_OTP: "/dev/api/v1/auth/resend_otp/post/",
  VERIFY_MFA: "/dev/api/v1/auth/mfa_verify/post/",
  IS_MFA: "/dev/api/v1/auth/is_mfa/post/",
  GET_SOFTWARE_TOKEN: "/dev/api/v1/auth/associate_software_token/post/post/",
  VERIFY_SOFTWARE_TOKEN: "/dev/api/v1/auth/verify_access_token/post/",
  SET_MFA_PREFERENCE: "/dev/api/v1/auth/verify_software_token/post/",
  UAM: {
    KEYWORD_OVERVIEW: "/api/v1/ecom/unified_ad_manager/keyword_overview/post/",
    KEYWORD_UPDATE: "/api/v1/ecom/unified_ad_manager/keyword_update/post/",
    AD_GROUP_OVERVIEW:
      "/api/v1/ecom/unified_ad_manager/ad_group_overview/post/",
    ADD_KEYWORD: "/api/v1/ecom/unified_ad_manager/add_keyword/post/",
    CAMPAIGN_OVERVIEW_POST:
      "/api/v1/ecom/unified_ad_manager/campaign_update/post/",
    CAMPAIGN_OVERVIEW:
      "/api/v1/ecom/unified_ad_manager/campaign_overview/post/",
    PRODUCT_OVERVIEW: "/api/v1/ecom/unified_ad_manager/product_overview/post/",
    PRODUCT_OVERVIEW_UPDATE:
      "/api/v1/ecom/unified_ad_manager/product_update/post/",
    AD_GROUP_UPDATE: "/api/v1/ecom/unified_ad_manager/ad_group_update/post/",
    CREATE_CAMPAIGN: "/api/v1/ecom/unified_ad_manager/campaign_create/post/",
    PLATFORM_FILTER: "/api/v1/ecom/unified_ad_manager/filters/platform/post/",
    STATUS_FILTER: "/api/v1/ecom/unified_ad_manager/filters/status/post/",
    CAMPAIGN_TYPE_FILTER:
      "/api/v1/ecom/unified_ad_manager/filters/campaign_type/post/",
    CAMPAIGN_NAME_FILTER:
      "/api/v1/ecom/unified_ad_manager/filters/campaign_name/post/",
    AD_GROUP_NAME_FILTER:
      "/api/v1/ecom/unified_ad_manager/filters/ad_group_name/post/",
    KEYWORD_FILTER: "/api/v1/ecom/unified_ad_manager/filters/keyword/post/",
    MATCH_TYPE_FILTER: "/api/v1/ecom/unified_ad_manager/filters/keyword/post/",
    PRODUCT_CODE_FILTER:
      "/api/v1/ecom/unified_ad_manager/filters/product_code/post/",
    AD_KEYWORD: "/api/v1/ecom/unified_ad_manager/add_keyword/post/",
    AD_PRODUCT: "/api/v1/ecom/unified_ad_manager/add_product/post/",
    // LOGS
    CAMPAIGN_LOGS: "/api/v1/ecom/unified_ad_manager/campaign_logs/post/",
    AD_GROUP_LOGS: "/api/v1/ecom/unified_ad_manager/ad_group_logs/post/",
    KEYWORD_LOGS: "/api/v1/ecom/unified_ad_manager/keyword_logs/post/",
    PRODUCT_LOGS: "/api/v1/ecom/unified_ad_manager/product_logs/post/",
    RULE_LOGS: "/api/v1/ecom/unified_ad_manager/rule_engine_logs/post/",
    RULE_RESPONSE: "/api/v1/ecom/unified_ad_manager/rule_engine_api_response/get/ge",
  },
  BI: {
    PACKAGE : "access_control/user_packages",
    BI_FILTERS: "360_dashboard/brand_infringement/filter/:col",
    DOWNLOAD_CSV: "360_dashboard/brand_infringement/download_csv",
    INCIDENTS_STATS: "360_dashboard/brand_infringement/dashboard/count",
    INCIDENCE_PERCENTAGE: "360_dashboard/brand_infringement/dashboard/incidence-percentage",
    INCIDENT_REPORT:"360_dashboard/brand_infringement/dashboard/incident-volume",
    CASE_CHANNEL:"360_dashboard/brand_infringement/dashboard/case-by-channel",
    CASE_SUB_CHANNEL:"360_dashboard/brand_infringement/dashboard/case-by-sub-channel",
    CASE_CATEGORY:"360_dashboard/brand_infringement/dashboard/case-by-category",
    CASE_PUBLISHER:"360_dashboard/brand_infringement/dashboard/topPublisher",
    PUBLISHER_REPORT:"360_dashboard/brand_infringement/dashboard/publisher-case-report/:publisher",
    TOP_HANDLES:"360_dashboard/brand_infringement/dashboard/top-handles",
    TABLE_DATA:"360_dashboard/brand_infringement/dashboard/table-data",
    INCIDENTS_TABS:"360_dashboard/brand_infringement/dashboard/incidences-tabs",
    PUBLISHER_INSIGHT:"360_dashboard/brand_infringement/dashboard/publisher-insights",
    HANDLE_INSIGHT:"360_dashboard/brand_infringement/dashboard/handle-insights",
    CASE_REPORT:"360_dashboard/brand_infringement/dashboard/publisher-case-report",
    WEB:{
      INCIDENTS_STATS: "360_dashboard/website_app/website_summary",
      WEB_APP_STATUS: "360_dashboard/website_app/website_activity",
      CATEGORY_SHARE: "360_dashboard/website_app/category_share_chart",
      CATEGORY_TREND: "360_dashboard/website_app/category_trend",
      CUSTOMER_CARE:"360_dashboard/website_app/customer_care_bar_chart",
      JOB_PROMOTION: "360_dashboard/website_app/job_promotion",
      OFFERS: "360_dashboard/website_app/get_offers",
      SPONSORS_ADS: "360_dashboard/website_app/google_sponser_ads"
     
  },
  WEB_INSIGHTS:{
    CATEGROY_CARDS:"360_dashboard/website_app/insights/categories_cards",
    HOSTING_PROVIDER:"360_dashboard/website_app/insights/hosting_providers",
    TAKEDOWN:"360_dashboard/website_app/insights/takedown",
    TOP_CATEGORIES:"360_dashboard/website_app/insights/top_categories",
    TOP_PLATFORMS:"360_dashboard/website_app/insights/top_platforms",
  },
  WEB_ANALYTICS:{
    GEOGRAPHICAL_DISTRIBUTION:"360_dashboard/website_app/analytics/geographical_distribution",
    INTERNATIONAL_HOSTING:"360_dashboard/website_app/analytics/international_hosting",
    HOSTING_PROVIDERS:"360_dashboard/website_app/analytics/domain_providers",
    HOSTING_PROVIDERS_INFO:"360_dashboard/website_app/analytics/domain_providers_info",
    PLATFORM_INFO:"360_dashboard/website_app/analytics/platform_info",
    CONTACT_NUMBER:"360_dashboard/website_app/analytics/contact_number",
    DOMAIN_DROPDOWN:"360_dashboard/website_app/analytics/sus_similar_domains_dropdown",
    DOMIANS_TABLE:"360_dashboard/website_app/analytics/sus_similar_domains_table",
    APK_DROPDOWN:"360_dashboard/website_app/analytics/apk_details_dropdown",
    APK_TABLE:"360_dashboard/website_app/analytics/apk_details_table",
 },
  WEB_INCIDENTS:{
    VIEW_INCIDENTS:"360_dashboard/website_app/incidents/view_incidents",
    DOWNLOAD_INCIDENTS:"360_dashboard/website_app/incidents/download_incidents",
    UPDATE_INCIDENT:"360_dashboard/website_app/incidents/update_ticket",
    RAISE_TICKET:"360_dashboard/website_app/incidents/raise_multiple_tickets",
    UPLOAD_CSV:"360_dashboard/website_app/incidents/upload_ticket_file",
    RAISE_TICKET_BULK:"360_dashboard/website_app/incidents/raise_bulk_tickets"
    
  },
  WEB_TICKETS:{
    TABLE_DATA:"360_dashboard/website_app/tickets/view_tickets",
    VIEW_TICKET:"360_dashboard/website_app/tickets/view_ticket_logs",
    EDIT_TICKET:"360_dashboard/website_app/tickets/edit_ticket",
    TICKET_OVERVIEW:"360_dashboard/website_app/tickets/ticket_overview"

  },
  SM_SUMMARY:{
    STAT_CARDS: "360_dashboard/social_media/summary/stat_cards",
    SM_STATUS: "360_dashboard/social_media/summary/social_media_status",
    CATEGORY_SHARE: "360_dashboard/social_media/summary/category_share",
    CATEGORY_TREND: "360_dashboard/social_media/summary/category_trend",
   },
   SM_ANALYTICS:{
    INCIDENT_BY_PLATFORM:"360_dashboard/social_media/analytics/incident_platform",
    INCIDENT_BY_CATEGORY:"360_dashboard/social_media/analytics/incident_category",
    MAJOR_PLATFORM:"360_dashboard/social_media/analytics/major_platforms",
    PENDING_INCIDENT:"360_dashboard/social_media/analytics/pending_incidents",
    CONTACT_NUMBER:"360_dashboard/social_media/analytics/contact_no",
    CATEGORY_TAKEDOWN:"360_dashboard/social_media/analytics/category_takedown",
    SUSPICIOUS_OFFER_CATEGORY: "360_dashboard/social_media/analytics/suspicious_offer_categories",
    CUSTOMER_CARE_TREND: "360_dashboard/social_media/analytics/customer_care_trend",
    PLATFORM_TRENDS: "360_dashboard/social_media/analytics/top_platform_trend",
   },
   SM_INSIGHTS:{
    PLATFORMS_OBSERVED: "360_dashboard/social_media/insights/platforms_observed",
    TAKEDOWN_TREND: "360_dashboard/social_media/insights/takedown_trend",
    INCIDENT_INDUSTRY:"360_dashboard/social_media/insights/incident_industry",
    PLATFORM_TREND: "360_dashboard/social_media/insights/platform_trend",
    TOP_PLATFORMS: "360_dashboard/social_media/insights/top_platforms",
    TOP_CATEGORIES : "360_dashboard/social_media/insights/top_categories",
    TOP_HANDLES: "360_dashboard/social_media/insights/top_handles",
    TAKEDOWN: "360_dashboard/social_media/insights/takedown",
   },
   SM_INCIDENTS:{
    SM_VIEW_INCIDENTS:"360_dashboard/social_media/incidents/view_incidents",
    SM_DOWNLOAD_INCIDENTS:"360_dashboard/social_media/incidents/download_incidents",
    SM_UPDATE_INCIDENT:"360_dashboard/social_media/incidents/update_ticket",
    RAISE_TICKET:"360_dashboard/social_media/incidents/raise_multiple_tickets",
    UPLOAD_CSV:"360_dashboard/social_media/incidents/upload_ticket_file",
    RAISE_TICKET_BULK:"360_dashboard/social_media/incidents/raise_bulk_tickets"

  },
  SM_TICKETS:{
    SM_VIEW_TICKETS:"360_dashboard/social_media/tickets/view_tickets"
  },
  REPORT:{
    GET_CATEGORY:"360_dashboard/reporting_tool/reporting_tool_screen/get_category",
    GET_TEMPLATE:"360_dashboard/reporting_tool/reporting_tool_screen/get_template",
    GET_TEMPLATE_FIELD:"360_dashboard/reporting_tool/reporting_tool_screen/get_template_fields",
    FILTER_COLUMN:"360_dashboard/reporting_tool/reporting_tool_screen/fields/filters/Priority/",
    OCCURANCE_DOWNLOAD:"360_dashboard/reporting_tool/reporting_tool_screen/scheduler/occurance",
    MAILING_LIST:"360_dashboard/reporting_tool/reporting_tool_screen/list_all_mailing_lists",
    REPORT_SUMMARY:"app/reporting_tool/reengagement/summary-table",
    CREATE_REPORT:"360_dashboard/reporting_tool/reporting_tool_screen/create_report",
    VIEW_REPORT:"360_dashboard/reporting_tool/reporting_tool_screen/view_report",
    EDIT_REPORT:"360_dashboard/reporting_tool/reporting_tool_screen/edit_report",
    CREATE_MAILING_LIST:"360_dashboard/reporting_tool/reporting_tool_screen/create_mailing_list",
    EDIT_MAILING_LIST:"360_dashboard/reporting_tool/reporting_tool_screen/edit_mailing_list",
    STATUS_CHANGE:"360_dashboard/reporting_tool/reporting_tool_screen/summary-table"
  }
  
  },
  RULES: {
    GET_RULES_RULESET: "/getRules_RuleSet",
    FILTERS_RULES_RULESET: "/filterRules_RuleSet",
  },
};

export const RULE_ENGINE_URL =
  process.env.NEXT_PUBLIC_RULE_ENGINE_URL || "https://ruleengine.mfilterit.net";

export default Endpoint;

 