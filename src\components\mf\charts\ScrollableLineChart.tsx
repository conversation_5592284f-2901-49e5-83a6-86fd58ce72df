"use client";

import React from "react";
import { <PERSON><PERSON><PERSON>, Line, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, Area, ComposedChart } from "recharts";
import { format } from "date-fns";

interface ScrollableLineChartProps {
  data: any[];
  lines: Array<{
    dataKey: string;
    name: string;
    stroke: string;
    strokeWidth?: number;
    dot?: any;
    activeDot?: any;
    fill?: string;
    fillOpacity?: number;
  }>;
  height?: number;
  enableHorizontalScroll?: boolean;
  xAxisFormatter?: (value: string) => string;
  tooltipFormatter?: (value: any, name: string) => [string, string];
  legendProps?: any;
  margin?: {
    top?: number;
    right?: number;
    left?: number;
    bottom?: number;
  };
  showLegend?: boolean;
  isAreaChart?: boolean;
}

export function ScrollableLineChart({
  data,
  lines,
  height = 250,
  enableHorizontalScroll = false,
  xAxisFormatter,
  tooltipFormatter,
  legendProps = {},
  margin = { top: 10, right: 30, left: 0, bottom: 20 },
  showLegend = true,
  isAreaChart = false
}: ScrollableLineChartProps) {
  const chartWidth = enableHorizontalScroll ? Math.max(data.length * 80, 800) : "100%";

  return (
    <div className="flex flex-col h-full">
      {/* Scrollable Chart Area */}
      <div className={`flex-1 ${enableHorizontalScroll ? 'overflow-x-auto' : 'overflow-hidden'}`}>
        {enableHorizontalScroll ? (
          isAreaChart ? (
            <ComposedChart
              data={data}
              width={chartWidth}
              height={height - (showLegend ? 60 : 0)}
              margin={margin}
            >
              <XAxis
                dataKey="date"
                tickFormatter={xAxisFormatter || ((value: string) => {
                  const d = new Date(value);
                  const yyyy = d.getFullYear();
                  const mm = String(d.getMonth() + 1).padStart(2, '0');
                  const dd = String(d.getDate()).padStart(2, '0');
                  return `${yyyy}-${mm}-${dd}`;
                })}
                style={{ fontSize: '12px' }}
              />
              <YAxis style={{ fontSize: '12px' }} />
              <Tooltip
                content={({ active, payload, label }) => {
                  if (!active || !payload?.length) return null;
                  
                  return (
                    <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                      <div className="font-medium bg-gray-100 text-black dark:text-white">
                        {format(new Date(label), 'dd MMM yyyy')}
                      </div>
                      <div className="grid gap-1.5">
                        {payload.map((item, index) => {
                          const indicatorColor = item.payload.fill || item.color || item.stroke;
                          
                          return (
                            <div
                              key={item.dataKey}
                              className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                            >
                              <div
                                className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                style={
                                  {
                                    "--color-bg": indicatorColor,
                                    "--color-border": indicatorColor,
                                  } as React.CSSProperties
                                }
                              />
                              <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                <span className="text-muted-foreground">
                                  {item.name || item.dataKey}
                                </span>
                                <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                  {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                }}
              />
                              {lines.map((line, index) => (
                  <Area
                    key={line.dataKey}
                    type="linear"
                    dataKey={line.dataKey}
                    name={line.name}
                    stroke={line.stroke}
                    strokeWidth={line.strokeWidth || 3}
                    fill={line.fill || line.stroke}
                    fillOpacity={line.fillOpacity || 0.3}
                    dot={line.dot === false ? false : (line.dot || { fill: line.stroke, strokeWidth: 2, r: 4 })}
                    activeDot={line.activeDot || { r: 6, stroke: line.stroke, strokeWidth: 2, fill: line.stroke }}
                  />
                ))}
            </ComposedChart>
          ) : (
            <LineChart
              data={data}
              width={chartWidth}
              height={height - (showLegend ? 60 : 0)}
              margin={margin}
            >
              <XAxis
                dataKey="date"
                tickFormatter={xAxisFormatter || ((value: string) => {
                  const d = new Date(value);
                  const yyyy = d.getFullYear();
                  const mm = String(d.getMonth() + 1).padStart(2, '0');
                  const dd = String(d.getDate()).padStart(2, '0');
                  return `${yyyy}-${mm}-${dd}`;
                })}
                style={{ fontSize: '12px' }}
              />
              <YAxis style={{ fontSize: '12px' }} />
              <Tooltip
                content={({ active, payload, label }) => {
                  if (!active || !payload?.length) return null;
                  
                  return (
                    <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                      <div className="font-medium bg-gray-100 text-black dark:text-white">
                        {format(new Date(label), 'dd MMM yyyy')}
                      </div>
                      <div className="grid gap-1.5">
                        {payload.map((item, index) => {
                          const indicatorColor = item.payload.fill || item.color || item.stroke;
                          
                          return (
                            <div
                              key={item.dataKey}
                              className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                            >
                              <div
                                className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                style={
                                  {
                                    "--color-bg": indicatorColor,
                                    "--color-border": indicatorColor,
                                  } as React.CSSProperties
                                }
                              />
                              <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                <span className="text-muted-foreground">
                                  {item.name || item.dataKey}
                                </span>
                                <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                  {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                }}
              />
              {lines.map((line, index) => (
                <Line
                  key={line.dataKey}
                  type="linear"
                  dataKey={line.dataKey}
                  name={line.name}
                  stroke={line.stroke}
                  strokeWidth={line.strokeWidth || 3}
                  dot={line.dot === false ? false : (line.dot || { fill: line.stroke, strokeWidth: 2, r: 4 })}
                  activeDot={line.activeDot || { r: 6, stroke: line.stroke, strokeWidth: 2, fill: line.stroke }}
                />
              ))}
            </LineChart>
          )
        ) : (
          isAreaChart ? (
            <ResponsiveContainer width="100%" height={height - (showLegend ? 60 : 0)}>
              <ComposedChart
                data={data}
                margin={margin}
              >
                <XAxis
                  dataKey="date"
                  tickFormatter={xAxisFormatter || ((value: string) => {
                    const d = new Date(value);
                    const yyyy = d.getFullYear();
                    const mm = String(d.getMonth() + 1).padStart(2, '0');
                    const dd = String(d.getDate()).padStart(2, '0');
                    return `${yyyy}-${mm}-${dd}`;
                  })}
                  style={{ fontSize: '12px' }}
                />
                <YAxis style={{ fontSize: '12px' }} />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (!active || !payload?.length) return null;
                    
                    return (
                      <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                        <div className="font-medium bg-gray-100 text-black dark:text-white">
                          {format(new Date(label), 'dd MMM yyyy')}
                        </div>
                        <div className="grid gap-1.5">
                          {payload.map((item, index) => {
                            const indicatorColor = item.payload.fill || item.color || item.stroke;
                            
                            return (
                              <div
                                key={item.dataKey}
                                className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                              >
                                <div
                                  className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                  style={
                                    {
                                      "--color-bg": indicatorColor,
                                      "--color-border": indicatorColor,
                                    } as React.CSSProperties
                                  }
                                />
                                <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                  <span className="text-muted-foreground">
                                    {item.name || item.dataKey}
                                  </span>
                                  <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                    {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  }}
                />
                {lines.map((line, index) => (
                  <Area
                    key={line.dataKey}
                    type="linear"
                    dataKey={line.dataKey}
                    name={line.name}
                    stroke={line.stroke}
                    strokeWidth={line.strokeWidth || 3}
                    fill={line.fill || line.stroke}
                    fillOpacity={line.fillOpacity || 0.3}
                    dot={line.dot === false ? false : (line.dot || { fill: line.stroke, strokeWidth: 2, r: 4 })}
                    activeDot={line.activeDot || { r: 6, stroke: line.stroke, strokeWidth: 2, fill: line.stroke }}
                  />
                ))}
              </ComposedChart>
            </ResponsiveContainer>
          ) : (
            <ResponsiveContainer width="100%" height={height - (showLegend ? 60 : 0)}>
              <LineChart
                data={data}
                margin={margin}
              >
                <XAxis
                  dataKey="date"
                  tickFormatter={xAxisFormatter || ((value: string) => {
                    const d = new Date(value);
                    const yyyy = d.getFullYear();
                    const mm = String(d.getMonth() + 1).padStart(2, '0');
                    const dd = String(d.getDate()).padStart(2, '0');
                    return `${yyyy}-${mm}-${dd}`;
                  })}
                  style={{ fontSize: '12px' }}
                />
                <YAxis style={{ fontSize: '12px' }} />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (!active || !payload?.length) return null;
                    
                    return (
                      <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                        <div className="font-medium bg-gray-100 text-black dark:text-white">
                          {format(new Date(label), 'dd MMM yyyy')}
                        </div>
                        <div className="grid gap-1.5">
                          {payload.map((item, index) => {
                            const indicatorColor = item.payload.fill || item.color || item.stroke;
                            
                            return (
                              <div
                                key={item.dataKey}
                                className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                              >
                                <div
                                  className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                  style={
                                    {
                                      "--color-bg": indicatorColor,
                                      "--color-border": indicatorColor,
                                    } as React.CSSProperties
                                  }
                                />
                                <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                  <span className="text-muted-foreground">
                                    {item.name || item.dataKey}
                                  </span>
                                  <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                    {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  }}
                />
                {lines.map((line, index) => (
                  <Line
                    key={line.dataKey}
                    type="linear"
                    dataKey={line.dataKey}
                    name={line.name}
                    stroke={line.stroke}
                    strokeWidth={line.strokeWidth || 3}
                    dot={line.dot === false ? false : (line.dot || { fill: line.stroke, strokeWidth: 2, r: 4 })}
                    activeDot={line.activeDot || { r: 6, stroke: line.stroke, strokeWidth: 2, fill: line.stroke }}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          )
        )}
      </div>
      
      {/* Fixed Legend below scrollbar */}
      {showLegend && (
        <div className="flex justify-center mt-2">
          <div className="flex flex-wrap justify-center items-center gap-4">
            {lines.map((line, index) => (
              <div key={`legend-${index}`} className="flex items-center gap-2">
                <div
                  style={{
                    width: 12,
                    height: 12,
                    backgroundColor: line.stroke,
                    borderRadius: 6,
                  }}
                ></div>
                <span style={{ fontSize: "12px" }}>{line.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 