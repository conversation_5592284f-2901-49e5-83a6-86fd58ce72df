import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePackage } from './PackageContext';

interface PackageSelectorProps {
  packages: string[];
  className?: string;
}

export function PackageSelector({ packages, className }: PackageSelectorProps) {
  const { selectedPackage, setSelectedPackage } = usePackage();

  return (
    <Select
      value={selectedPackage}
      onValueChange={(value) => {
        setSelectedPackage(value);
      }}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder="Select a package" />
      </SelectTrigger>
      <SelectContent className='w-32 text-body' >
        {packages.map((pkg) => (
          <SelectItem key={pkg} value={pkg}>
            {pkg}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
} 