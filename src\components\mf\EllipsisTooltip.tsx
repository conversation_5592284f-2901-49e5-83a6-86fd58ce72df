"use client";

import React, { useRef, useEffect, useState } from "react";
 
interface EllipsisTooltipProps {
  content: string | number;
  className?: string;
  maxChars?: number; // optional hard cap for character length
}
 
const EllipsisTooltip: React.FC<EllipsisTooltipProps> = ({ content, className, maxChars }) => {

  const spanRef = useRef<HTMLSpanElement>(null);

  const [showTooltip, setShowTooltip] = useState(false);
 
  useEffect(() => {

    const el = spanRef.current;

    if (!el) return;

    // Allow time for layout

    const checkTruncation = () => {

      if (el.scrollWidth > el.clientWidth) {

        setShowTooltip(true);

      } else {

        setShowTooltip(false);

      }

    };

    // Small timeout to allow layout

    const timeout = setTimeout(checkTruncation, 0);

    return () => clearTimeout(timeout);

  }, [content]);
 
  const display = (() => {
    const text = String(content);
    if (maxChars && text.length > maxChars) {
      return text.slice(0, maxChars) + "...";
    }
    return content;
  })();
  
  return (
    <span
      ref={spanRef}
      className={`block overflow-hidden text-ellipsis whitespace-nowrap ${className || ""}`}
      title={String(content)}
    >
      {display}
    </span>
  );

};
 
export default EllipsisTooltip;
 