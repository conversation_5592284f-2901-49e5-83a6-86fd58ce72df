import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import React from 'react';

interface FilterBarProps {
  selectedPublisher: string;
  setSelectedPublisher: (value: string) => void;
  selectedSubPublisher: string;
  setSelectedSubPublisher: (value: string) => void;
  selectedCampaign: string;
  setSelectedCampaign: (value: string) => void;
  selectedChannelFilter: string;
  setSelectedChannelFilter: (value: string) => void;
  selectedEventType: string;
  setSelectedEventType: (value: string) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({
  selectedPublisher,
  setSelectedPublisher,
  selectedSubPublisher,
  setSelectedSubPublisher,
  selectedCampaign,
  setSelectedCampaign,
  selectedChannelFilter,
  setSelectedChannelFilter,
  selectedEventType,
  setSelectedEventType,
}) => {
  // Placeholder options - replace with actual data later
  const publishers = ["All", "Publisher A", "Publisher B"];
  const subPublishers = ["All", "Sub Publisher X", "Sub Publisher Y"];
  const campaigns = ["All", "Campaign 1", "Campaign 2"];
  const channels = ["All", "Channel M", "Channel N"];
  const eventTypes = ["All", "Type 1", "Type 2"];

  return (
    <div className="flex gap-2 mb-4">
      <Select value={selectedPublisher} onValueChange={setSelectedPublisher}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Publishers" />
        </SelectTrigger>
        <SelectContent>
          {publishers.map(option => (
            <SelectItem key={option} value={option}>{option}</SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={selectedSubPublisher} onValueChange={setSelectedSubPublisher}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Sub Publishers" />
        </SelectTrigger>
        <SelectContent>
          {subPublishers.map(option => (
            <SelectItem key={option} value={option}>{option}</SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={selectedCampaign} onValueChange={setSelectedCampaign}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Campaigns" />
        </SelectTrigger>
        <SelectContent>
          {campaigns.map(option => (
            <SelectItem key={option} value={option}>{option}</SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={selectedChannelFilter} onValueChange={setSelectedChannelFilter}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Channels" />
        </SelectTrigger>
        <SelectContent>
          {channels.map(option => (
            <SelectItem key={option} value={option}>{option}</SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={selectedEventType} onValueChange={setSelectedEventType}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Event Type" />
        </SelectTrigger>
        <SelectContent>
          {eventTypes.map(option => (
            <SelectItem key={option} value={option}>{option}</SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default FilterBar; 