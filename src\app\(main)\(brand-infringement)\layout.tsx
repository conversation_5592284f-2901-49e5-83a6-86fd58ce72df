"use client";
import { MFTopBar } from "@/components/mf/MFTopBar";
import MFWebFraudAsideMenu from "@/components/mf/MFWebFraudAsideMenu";
import { useState } from "react";
import React from "react";
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';

export default function BrandInfringementLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <div className="flex h-screen w-full flex-col">
      <MFTopBar isExpanded={isExpanded} onToggle={() => setIsExpanded(!isExpanded)} isDialogOpen={isDialogOpen} />
      <div className="flex flex-1 overflow-hidden">
        <MFWebFraudAsideMenu isExpanded={isExpanded} onHover={setIsExpanded} theme="light" isBlurred={isDialogOpen} />
        <QueryClientProvider client={queryClient}>
          <main className={`flex-1 overflow-auto bg-gray-50 ${isDialogOpen ? 'blur-sm' : ''}`}>
            {children && React.cloneElement(children as React.ReactElement, { setIsDialogOpen, isDialogOpen })}
          </main>
        </QueryClientProvider>
      </div>
    </div>
  );
} 