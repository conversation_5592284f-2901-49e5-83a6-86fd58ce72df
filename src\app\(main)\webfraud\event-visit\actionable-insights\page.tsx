"use client"
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import ResizableTable from '@/components/mf/TableComponent';
import HeaderRow from '@/components/mf/HeaderRow';
import { onExpand, downloadURI,debounce} from '@/lib/utils';
import { useState,useCallback,useRef,useEffect } from 'react';
import domToImage from "dom-to-image";
import Donut<PERSON>hart from '@/components/mf/DonutChart';
import { useApiCall } from "../../queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import { Filter } from "@/components/mf/Filters";
import { useDateRange } from "@/components/mf/DateRangeContext";
import Endpoint from '../../common/endpoint';
import { useLoading } from '@/components/mf/LoadingContext';

interface FilterItem {
    label: string;
    checked: boolean;
  }
  
  interface FilterState {
    filters: FilterItem[];
    is_select_all: boolean;
    selected_count: number;
    loading: boolean;
  }
  
  interface FilterPayload {
    [key: string]: FilterState;
  }
  // Add type for API response
  interface FilterApiResponse {
    data: string[];
    isLoading: boolean;
  }
  
  interface ChartConfig {
    [key: string]: {
      label: string;
      color: string;
    };
  }
  //meta data
  interface MetaData{
    label: string;
    visit: number;
    fill: string;
    [key: string]: string | number;
  }
interface ColumnUserBR {
    title: string,
    key: keyof UserDataBR,
  }
 
  interface UserDataBR {
    inserted_date: string;
    "Total Visits": number;
    "Unique IPs": number;
    "Total Blocked IPs": number;
    "Blocked IP Percentage" :number;
  }
  interface BlockedG {
    data: UserDataBR[];
    total_records: number;
    page_number: number;
    limit: number;
    total_pages: number;
    search_term: string;
  }
  const BlockedRepeatG: ColumnUserBR[] = [
    { title: "Date", key: "inserted_date"},
    { title: "Total Visits", key: "Total Visits" },
    { title: "Unique IPs" , key:  "Unique IPs" },
    { title: "Total Blocked IPs", key: "Total Blocked IPs" },
    { title: "Blocked IP Percentage", key: "Blocked IP Percentage" },
  ]
  //overall placement
  interface ColumnGood {
    title: string,
    key: keyof userDataGoodP,
  }
 interface userDataGoodP{
   // Publisher:string;
    Placement:string;
    Status:string;
    Score:string;
 }
 interface OverallData{
  data: userDataGoodP[];
  total_records: number;
  page_number: number;
  limit: number;
  total_pages: number;
  search_term: string;
 }
 const OverallGoodP : ColumnGood[]= [
  { title: "Placement", key: "Placement" },
  { title: "Status" , key:  "Status" },
  { title: "Score", key: "Score" },
]
 interface ColumnBad{
    title: any,
    key: keyof userDataBad,
 }
 interface userDataBad{
    Placement:string;
    Status:string;
    Score:string;
    Category:string;
 }
 interface BadPData{
  data: userDataBad[];
  total_records: number;
  page_number: number;
  limit: number;
  total_pages: number;
  search_term: string;
 }
 const BadP : ColumnBad[]= [
   { title: "Placement", key: "Placement" },
   { title: "Status" , key:  "Status" },
   { title: "Score", key: "Score" },
   { title: "Category" , key:  "Category" },

]
 interface ColumnGAO {
    title: any,
    key: keyof userDataAO,
  }
 interface userDataAO{
    Date:string;
    "Total Traffic":number;
    "High Intent":number;
    "Medium Intent":number;
    "Low Intent":number;
 }
 interface AO{
  data:userDataAO[];
  total_records: number;
  page_number: number;
  limit: number;
  total_pages: number;
  search_term: string;

 }
 const GoogleAO : ColumnGAO[]= [
  { title: "Date", key: "Date"},
  { title: "Total Traffic", key: "Total Traffic" },
  { title: "High Intent" , key:  "High Intent" },
  { title: "Medium Intent", key: "Medium Intent" },
  { title: "Low Intent" , key:  "Low Intent" },

]
 
//overall placement
interface UserdataP{
label:string;
visit:number;
percentage:string;
fill: string;
[key: string]: string | number;
}
interface OverallP{
  data:UserdataP[];
  total_records:string;
}
  //unsafe
  interface UnsafeP{
    data:UserdataP[];
    total_count:{
      total_count:number;
      count_percentage:string
    }
  }
  // google optimization
  interface UserDataG{
    inent_status:string;
    percentage:number;
  }
  interface OptimG{
    data:UserDataG[];
    total_placements:string;
  }
  
  const chartConfigMetaIP = {
    clean_percentage: {
      label: "Cleaned",
      color: "#d46cb4",
    },
    blocked_percentage: {
      label: " Blocked ",
      color: "#854442",
    },
    blocked_ips_count: {
        label: "Blocked IPs",
        color: "#82735c",
      },
  }

  
  const chartConfigOverallPlacement= {
    "brand_safe": {
      label: "Brand Safe",
      color: " #a459d1",
    },
    "brand_unsafe": {
      label: "Brand Unsafe",
      color: " #3c2f2f",
    }, 
  }

  const chartConfigUnsafePlacement = {
    "Highly Unsafe": {
      label:"Highly Unsafe",
      color: " #ffbe4f",
    },
    "Medium Unsafe": {
      label: "Medium Unsafe",
      color: " #0ea7b5",
    },
    "Low Unsafe": {
      label: "Low Unsafe",
      color: " #0c457d",
    },  
  }

  
  const chartConfigGoogleOptimization = {
    "High Intent": {
      label:"High Intent",
      color: " #cc0086",
    },
    "Medium Intent": {
      label: "Medium Intent",
      color: " #320261",
    },
    "Low Intent": {
      label: "Low Intent",
      color: " #787a41",
    },
    
  }
 const Actionable_insights = () => {
     const cardRefs = useRef<HTMLElement[]>([]);
      const [expandedCard, setExpandedCard] = useState<number | null>(null);
        const { startDate, endDate } = useDateRange();
        const [existingPublisherdata, setExistingPublisherdata] = useState<string[]>([]);
        const [existingSubPublisherdata, setExistingSubPublisherdata] = useState<string[]>([]);
        const [existingCampaigndata, setExistingCampaigndata] = useState<string[]>([]);
        const [existingChanneldata, setExistingChanneldata] = useState<string[]>([]);
        const [loadedFilter, setLoadedFilter] = useState<any>({});
        // const [isLoading, setIsLoading] = useState(true);
         const[OverallPercentage,setOverallPercentage] =useState<string>("");
         const[UnsafePercentage,setUnsafePercentage] =useState<string>("");
         const[Unsafe,setUnsafe ]= useState<UserdataP[]>([]);
         const[blockG,setBlockedG ]= useState<UserdataP[]>([]);
         const[blockM,setBlockedM ]= useState<UserdataP[]>([]);
         const[GoogleP,setGoogleP ]= useState<UserDataG[]>([]);
         const[FacebookP,setFacebookP ]= useState<UserDataG[]>([]);
         const[TotalP,setTotalP] =useState<string>();
         const[TotalF,setTotalF] =useState<string>();
         const[GoogleA,setGoogleA]= useState<userDataAO[]>([]);
          const [currentPageAO, setCurrentPageAO] = useState(1);
          const [limitAO, setLimitAO] = useState(10);
          const [searchTermAO, setSearchTermAO] = useState("");
          const[FacebookAO,setFacebookAO]= useState<userDataAO[]>([]);
          const [currentPageFO, setCurrentPageFO] = useState(1);
          const [limitFO, setLimitFO] = useState(10);
          const [searchTermFO, setSearchTermFO] = useState("");
          const[Overall,setOverall ]= useState<UserdataP[]>([]);
          const [currentPageBR, setCurrentPageBR] = useState(1);
          const [limitBR, setLimitBR] = useState(10);
          const [searchTermBR, setSearchTermBR] = useState("");
          const[UnsafeB,setUnsafeB ]= useState<userDataBad[]>([]);
          const [currentPageB, setCurrentPageB] = useState(1);
          const [limitB, setLimitB] = useState(10);
          const [searchTermB, setSearchTermB] = useState("");
          const[OverallG,setOverallG ]= useState<userDataGoodP[]>([]);
          const [currentPageG, setCurrentPageG] = useState(1);
          const [limitG, setLimitG] = useState(10);
          const [searchTermG, setSearchTermG] = useState("");
          const [currentPageM, setCurrentPageM] = useState(1);
          const [limitM, setLimitM] = useState(10);
          const [searchTermM, setSearchTermM] = useState("");
        const { selectedPackage } = usePackage();
         const [MetaIP, setMetaIP] = useState<string[]>([]);
         const [GoogleIP, setGoogleIP] = useState<string[]>([]);
         const [query, setQuery] = useState({
            publishers: ["all"],
            sub_publishers: ["all"],
            campaigns: ["all"],
            channels: ["all"],
          });
         const loading = useLoading();
                       if (!loading) {
                         throw new Error('Loading context not found');
                       }
                       const { isLoading, startLoading, stopLoading } = loading;
         // const [triggerKey, setTriggerKey] = useState('global');
          const isInitialLoadComplete = useRef(false);
          const isApiInProgress = useRef(false);
          const previousFilterRef = useRef(loadedFilter);
         

          const[TotalRecordAF,setTotalRecordAF] =useState();
          const[TotalRecordAG,setTotalRecordAG]=useState();
          const[TotalRecordBP,setTotalRecordBP] =useState();
          const[TotalRecordOG,setTotalRecordOG]=useState();
          const[TotalRecordBRM,setTotalRecordBRM] =useState();
          const[TotalRecordBRG,setTotalRecordBRG]=useState();

     const onExport = useCallback(
        async (s: string, title: string, index: number) => {
        //  console.log("onExport function called", { s, title, index });
          if (!cardRefs.current[index]) return;
          const ref = cardRefs.current[index];
    
          if (!ref) return;
          switch (s) {
            case "png":
              const screenshot = await domToImage.toPng(ref);
              downloadURI(screenshot, title + ".png");
              break;
            default:
          }
        },
        [cardRefs] // Include `cardRefs` as a dependency here
      );    
         const handleExpand = (index: number) => {
           onExpand(index, cardRefs, expandedCard, setExpandedCard);
         };   
         
           // Publishers Filter API
  const publishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingPublisherdata(data);
      if (data.length > 0) {
             }
             stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });

  // Sub Publishers Filter API
  const subPublishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.SUB_PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingSubPublisherdata(data);
      if (data.length > 0) {
      }
      stopLoading();
    },
    onError: (error) => {
        stopLoading();
    },
  });

  // Campaigns Filter API
  const campaignsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CAMPAIGNS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingCampaigndata(data);
      if (data.length > 0) {
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });

  // Channels Filter API
  const channelsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CHANNELS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingChanneldata(data);
      if (data.length > 0) {
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });

 const filter = React.useMemo(
   () => ({
     Publishers: {
       filters:
         existingPublisherdata?.map((publisher: string) => ({
           label: publisher,
           // Change: Only check if it exists in the current selection
           checked: query.publishers?.includes("all") || 
                  query.publishers?.includes(publisher) || 
                  !query.publishers, // Default true if no selection exists
         })) || [],
       // Change: Determine if all are selected
       is_select_all: !query.publishers || 
                    query.publishers.includes("all") ||
                    query.publishers?.length === existingPublisherdata?.length,
       // Change: Actual selected count
       selected_count: query.publishers?.includes("all") 
                     ? existingPublisherdata?.length ?? 0
                     : query.publishers?.length ?? existingPublisherdata?.length ?? 0,
       loading: false,
     },
     "Sub Publishers": {
       filters:
         existingSubPublisherdata?.map((subPublisher: string) => ({
           label: subPublisher,
           checked: query.sub_publishers?.includes("all") || 
                  query.sub_publishers?.includes(subPublisher) || 
                  !query.sub_publishers,
         })) || [],
       is_select_all: !query.sub_publishers || 
                    query.sub_publishers.includes("all") ||
                    query.sub_publishers?.length === existingSubPublisherdata?.length,
       selected_count: query.sub_publishers?.includes("all")
                     ? existingSubPublisherdata?.length ?? 0
                     : query.sub_publishers?.length ?? existingSubPublisherdata?.length ?? 0,
       loading: false,
     },
     Campaigns: {
       filters:
         existingCampaigndata?.map((campaign: string) => ({
           label: campaign,
           checked: query.campaigns?.includes("all") || 
                  query.campaigns?.includes(campaign) || 
                  !query.campaigns,
         })) || [],
       is_select_all: !query.campaigns || 
                    query.campaigns.includes("all") ||
                    query.campaigns?.length === existingCampaigndata?.length,
       selected_count: query.campaigns?.includes("all")
                     ? existingCampaigndata?.length ?? 0
                     : query.campaigns?.length ?? existingCampaigndata?.length ?? 0,
       loading: false,
     },
     Channels: {
       filters:
         existingChanneldata?.map((channel: string) => ({
           label: channel,
           checked: query.channels?.includes("all") || 
                  query.channels?.includes(channel) || 
                  !query.channels,
         })) || [],
       is_select_all: !query.channels || 
                    query.channels.includes("all") ||
                    query.channels?.length === existingChanneldata?.length,
       selected_count: query.channels?.includes("all")
                     ? existingChanneldata?.length ?? 0
                     : query.channels?.length ?? existingChanneldata?.length ?? 0,
       loading: false,
     },
   }),
   [
     existingPublisherdata,
     existingSubPublisherdata,
     existingCampaigndata,
     existingChanneldata,
     query.publishers,
     query.sub_publishers,
     query.campaigns,
     query.channels,
   ]
 );
     
 //ip details google
 const GoogleIp = useApiCall<MetaData>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.GOOGLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
   // console.log("success google",response);
    
    if (Array.isArray(response)) {
      // Create chart data by mapping over the response
      const updatedChartData = Object.keys(response[0]).map((key) => {
        const config = chartConfigMetaIP[key];
    
        if (config) {
          const percentage = response[0][key]; // Access the value from the API response
    
          return {
            label: config.label,
            percentage: percentage,
            fill: config.color,
          };
        }
    
        return null;
      }).filter(item => item !== null); // Filter out null values
     // console.log(updatedChartData);
      if (JSON.stringify(updatedChartData) !== JSON.stringify(GoogleIP)) {
        setGoogleIP(updatedChartData);
      }
    }
    stopLoading();  },
  onError: (error) => {
    console.error('Error in GoogleIp:', error);
  }
});
//ip Blocked report google
const IPBlockedG = useApiCall<BlockedG>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.BLOCKED_REPORT_GOOGLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    page: currentPageBR,
    limit: limitBR,
    search_term: searchTermBR,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: UserDataBR[] = data.map((topItem: any) => ({
        inserted_date: topItem.inserted_date,
        "Total Visits": topItem.total_visit,
        "Unique IPs":topItem.unique_ip_count,
        "Total Blocked IPs": topItem.blocked_ips_count,
        "Blocked IP Percentage":topItem.blocked_ip_percentage
      }))
      if (JSON.stringify(updatedtop) !== JSON.stringify(blockG)) {
        setBlockedG(updatedtop);
      }
    }
    setTotalRecordBRG(response.total_pages)
      // Stop correct loader based on the trigger
      stopLoading();
  },
  onError: (error) => {
    // Stop correct loader based on the trigger
    stopLoading();
  },
});
 //ip details meta
 const MetaIp = useApiCall<MetaData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.META,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
     // console.log("success meta",response);
      
      if (Array.isArray(response)) {
        // Create chart data by mapping over the response
        const updatedChartData = Object.keys(response[0]).map((key) => {
          const config = chartConfigMetaIP[key];
      
          if (config) {
            const percentage = response[0][key]; // Access the value from the API response
      
            return {
              label: config.label,
              percentage: percentage,
              fill: config.color,
            };
          }
      
          return null;
        }).filter(item => item !== null); // Filter out null values
      //  console.log(updatedChartData);
        if (JSON.stringify(updatedChartData) !== JSON.stringify(MetaIP)) {
          setMetaIP(updatedChartData);
        }
      }
      stopLoading();  },
  onError: (error) => {
 //   setIsLoading(false);
  },
});
//ip Blocked report meta
const IPBlockedM = useApiCall<BlockedG>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.BLOCKED_REPORT_META,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    page: currentPageM,
    limit: limitM,
    search_term: searchTermM,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: UserDataBR[] = data.map((topItem: any) => ({
        inserted_date: topItem.inserted_date,
        "Total Visits": topItem.total_visit,
        "Unique IPs":topItem.unique_ip_count,
        "Total Blocked IPs": topItem.blocked_ips_count,
        "Blocked IP Percentage":topItem.blocked_ip_percentage
      }))
      if (JSON.stringify(updatedtop) !== JSON.stringify(blockM)) {
        setBlockedM(updatedtop);
      }
    }
    setTotalRecordBRM(response.total_pages);
     // Stop correct loader based on the trigger
     stopLoading();
  },

  onError: (error) => {
    // Stop correct loader based on the trigger
    stopLoading();
  },
  
});

//Overall Placement
const OverallPlacement = useApiCall<OverallP>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.OVERALL_PLACEMENT,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
   // console.log("overall placement",response)
    const data = response.data;
    if (Array.isArray(data)) {

      const updatedtop: UserdataP[] = data.map((topItem) => {
        const fraudDesc = topItem.pl_status as keyof typeof chartConfigOverallPlacement;
         const counts =topItem.count;
        const visits = topItem.count_percentage; // Convert percentage string to number
        if (fraudDesc in chartConfigOverallPlacement) {
          return {
            label: fraudDesc,
            visit:counts,
            percentage:`(${visits})`,
            fill: chartConfigOverallPlacement[fraudDesc].color,
          };
        }
        return {
          label: fraudDesc,
          visit:counts,
          percentage:`(${visits})`,
          fill: "#000",
        };
      });
      if (JSON.stringify(updatedtop) !== JSON.stringify(Overall)) {
        setOverall(updatedtop);
      }
      setOverallPercentage(response.total_records);
      stopLoading();    }
  },
  onError: (error) => {
   // setIsLoading(false);
  }
})
//Good Placement
const GoodP= useApiCall<OverallData>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.OVERALL_PLACEMENT_TABLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    page: currentPageG,
    limit: limitG,
    search_term: searchTermG,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: userDataGoodP[] = data.map((topItem: any) => ({
        Placement: topItem.placement_id,
        Status:topItem.pl_status,
        Score: topItem.score_category,
        
      }))
  
      if (JSON.stringify(updatedtop) !== JSON.stringify(OverallG)) {
        setOverallG(updatedtop);
      }
    }
  setTotalRecordOG(response.total_pages);
   // Stop correct loader based on the trigger
   stopLoading();
},
  onError: (error) => {
    // Stop correct loader based on the trigger
    stopLoading();
},
});
//unsafe Placement
const UnsafePlacement = useApiCall<UnsafeP>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.UNSAFE_PLACEMENT,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
   
    const data = response.data;
    if (Array.isArray(data)) {

      const updatedtop: UserdataP[] = data.map((topItem) => {
        const fraudDesc = topItem.score_category as keyof typeof chartConfigUnsafePlacement;
         const counts =topItem.count;
        const visits = topItem.count_percentage; // Convert percentage string to number
        if (fraudDesc in chartConfigUnsafePlacement) {
          return {
            label: fraudDesc,
            visit:counts,
            percentage:`(${visits})`,
            fill: chartConfigUnsafePlacement[fraudDesc].color,
          };
        }
        return {
          label: fraudDesc,
          visit:counts,
          percentage:`(${visits})`,
          fill: "#000",
        };
      });
     // console.log("unsafe placement updatedtop",updatedtop)
      if (JSON.stringify(updatedtop) !== JSON.stringify(Unsafe)) {
        setUnsafe(updatedtop);
      }
      setUnsafePercentage(response.total_count.count_percentage);
      stopLoading();  
  }
},
  onError: (error) => {
   // setIsLoading(false);
  }
})
//Bad Placement
const UnsafeP= useApiCall<BadPData>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.UNSAFE_PLACEMENT_TABLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    page: currentPageB,
    limit: limitB,
    search_term: searchTermB,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: userDataBad[] = data.map((topItem: any) => ({
        Placement: topItem.placement_id,
        Status:topItem.pl_status,
        Score: topItem.score_category,
        Category : topItem.category,
        
      }))
      if (JSON.stringify(updatedtop) !== JSON.stringify(UnsafeB)) {
        setUnsafeB(updatedtop);
      }
    }
    setTotalRecordBP(response.total_pages)
    stopLoading();
  },
  onError: (error) => {
    stopLoading();
  },
});
//Google Optimization
const GoogleOptim = useApiCall<OptimG>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.AUDIENCE_OPTIMIZATION_GOOGLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
   
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: UserDataG[] = data.map((topItem) => {
        const fraudDesc = topItem.intent_status as keyof typeof chartConfigGoogleOptimization;
        const visits = parseFloat(topItem.percentage.replace("%","")); // Convert percentage string to number
        if (fraudDesc in chartConfigGoogleOptimization) {
          return {
            label: fraudDesc,
            percentage:visits,
            fill: chartConfigGoogleOptimization[fraudDesc].color,
          };
        }
        return {
          label: fraudDesc,
          percentage:visits,
          fill: "#000",
        };
      });
      if (JSON.stringify(updatedtop) !== JSON.stringify(GoogleP)) {
        setGoogleP(updatedtop);
      }
      setTotalP(response.total_placements);
      stopLoading();    }
  },
  onError: (error) => {
    //setIsLoading(false);
  }
})
//google optimization table
const GoogleAOs= useApiCall<AO>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.AUDIENCE_OPTIMIZATION_GOOGLE_TABLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    page: currentPageAO,
    limit: limitAO,
    search_term: searchTermAO,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop:userDataAO[] = data.map((topItem: any) => ({
        Date: topItem.inserted_date,
        "Total Traffic":topItem.total_traffic,
        "High Intent": topItem.high_intent,
        "Medium Intent":topItem.medium_intent,
        "Low Intent":topItem.low_intent
        
      }))
      if (JSON.stringify(updatedtop) !== JSON.stringify(GoogleA)) {
        setGoogleA(updatedtop);
      }
    }
    setTotalRecordAG(response.total_pages);
    stopLoading();  },
  onError: (error) => {
    stopLoading();
  },
});
//FaceBook Optimization
const FacebookOptim = useApiCall<OptimG>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.AUDIENCE_OPTIMIZATION_FACEBOOK,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
   
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: UserDataG[] = data.map((topItem) => {
        const fraudDesc = topItem.intent_status as keyof typeof chartConfigGoogleOptimization;
        const visits = parseFloat(topItem.percentage.replace("%","")); // Convert percentage string to number
        if (fraudDesc in chartConfigGoogleOptimization) {
          return {
            label: fraudDesc,
            percentage:visits,
            fill: chartConfigGoogleOptimization[fraudDesc].color,
          };
        }
        return {
          label: fraudDesc,
          percentage:visits,
          fill: "#000",
        };
      });
     
      
     // console.log("unsafe placement updatedtop",updatedtop)
      if (JSON.stringify(updatedtop) !== JSON.stringify(FacebookP)) {
        setFacebookP(updatedtop);
      }
      setTotalF(response.total_placements);
      stopLoading();    }
  },
  onError: (error) => {
    //setIsLoading(false);
  }
})
//Facebook optimization table
const FacebookAOs= useApiCall<AO>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.OPTIMIZATION_FACEBOOK_TABLE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    page: currentPageFO,
    limit: limitFO,
    search_term: searchTermFO,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      const updatedtop: userDataAO[]= data.map((topItem: any) => ({
        Date: topItem.inserted_date,
        "Total Traffic":topItem.total_traffic,
        "High Intent": topItem.high_intent,
        "Medium Intent":topItem.medium_intent,
        "Low Intent":topItem.low_intent
        
      }))
      
      if (JSON.stringify(updatedtop) !== JSON.stringify(FacebookAO)) {
        setFacebookAO(updatedtop);
      }
    }
   setTotalRecordAF(response.total_pages);
   stopLoading();
  },

  onError: (error) => {
    stopLoading();
  },
});

const deepEqual = (arr1: any[], arr2: any[]) => {
  // Check if either arr1 or arr2 is undefined or not an array
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length) return false;
  
  // Proceed with deep comparison
  return arr1.every((item, index) => item.checked === arr2[index].checked && item.label === arr2[index].label);
};
  // Update the handleFilterChange function
  const handleFilterChange = useCallback((newState: Record<string, any>) => {
    if (!newState || deepEqual(newState, loadedFilter)) {
      return;
    }

    const payload = {
      publishers: newState.Publishers?.is_select_all 
        ? ['all']
        : newState.Publishers?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
      sub_publishers: newState['Sub Publishers']?.is_select_all
        ? ['all']
        : newState['Sub Publishers']?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
      campaigns: newState.Campaigns?.is_select_all
        ? ['all']
        : newState.Campaigns?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
      channels: newState.Channels?.is_select_all
        ? ['all']
        : newState.Channels?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label)
    };

    setQuery(payload);
    setLoadedFilter(newState);
  }, [loadedFilter]);

   //fetch Blocked Report Google api
   const fetchBlockedG = useCallback(() => {
    if (IPBlockedG.type === "mutation") {
      startLoading();

      IPBlockedG.result.mutate(); 
    }
  }, [IPBlockedG])
   const debouncedFetchBlockedG = useCallback(debounce(fetchBlockedG, 500), []);

    //fetch Blocked Report meta api
  const fetchBlockedM = useCallback(() => {
    if (IPBlockedM.type === "mutation") {
      startLoading();

      IPBlockedM.result.mutate();
    }
  }, [IPBlockedM])

   const debouncedFetchBlockedM = useCallback(debounce(fetchBlockedM, 500), []);

     //fetch Overall  api
  const fetchUnsafeB = useCallback(() => {
    if (UnsafeP.type === "mutation") {
      startLoading();
      UnsafeP.result.mutate();
    }
  }, [UnsafeP])

   const debouncedFetchUnsafeB = useCallback(debounce(fetchUnsafeB, 500), []);


     //fetch Blocked Report Google api
  const fetchOverallG = useCallback(() => {
    if (GoodP.type === "mutation") {
      startLoading();

      GoodP.result.mutate();
    }
  }, [GoodP])

   const debouncedFetchOverallG = useCallback(debounce(fetchOverallG, 500), []);

   
  const fetchGoogleAO = useCallback(() => {if (GoogleAOs.type === "mutation") {      startLoading();
    GoogleAOs.result.mutate();}}, [GoogleAOs])
   const debouncedFetchGoogleAO = useCallback(debounce(fetchGoogleAO, 5000), []);
   const fetchFacebookAO = useCallback(() => {if (FacebookAOs.type === "mutation") {      startLoading();
    FacebookAOs.result.mutate();} }, [])
   const debouncedFetchFacebookAO = useCallback(debounce(fetchFacebookAO, 5000), []);
    const fetchGoogleData = useCallback(() => {if (GoogleIp.type === "mutation") {      startLoading();
      GoogleIp.result.mutate()}},[]);
  //  const debouncedGD = useCallback(debounce(fetchGoogleData, 5000), []);
    const fetchMetaData = useCallback(() => { if (MetaIp.type === "mutation") {      startLoading();
      MetaIp.result.mutate()}},[]);
   // const debouncedMD = useCallback(debounce(fetchMetaData, 5000), []);
    const fetchOverallData = useCallback(() => { if (OverallPlacement.type === "mutation") {      startLoading();
      OverallPlacement.result.mutate()}},[]);
   // const debouncedOD = useCallback(debounce(fetchOverallData, 5000), []);
    const fetchUnsafeData = useCallback(() => {if (UnsafePlacement.type === "mutation") {      startLoading();
      UnsafePlacement.result.mutate()}},[]);
   // const debouncedUND = useCallback(debounce(fetchUnsafeData, 5000), []);
    const fetchGOptimData = useCallback(() => {if (GoogleOptim.type === "mutation") {      startLoading();
      GoogleOptim.result.mutate()}},[]);
   // const debouncedGOD = useCallback(debounce(fetchGOptimData, 5000), []);
    const fetchFoptimData = useCallback(() => {if (FacebookOptim.type === "mutation") {      startLoading();
      FacebookOptim.result.mutate()}},[]);
  //  const debouncedFOD = useCallback(debounce(fetchFoptimData, 5000), []);

//filter 
  const fetchPublisher = useCallback(() => {if (publishersFilterApi.type === "mutation"){publishersFilterApi.result.mutate()}},[]);
 // const debouncedP= useCallback(debounce(fetchPublisher, 5000), []);
  const fetchSubPublisher = useCallback(() => {if (subPublishersFilterApi.type === "mutation"){subPublishersFilterApi.result.mutate()}},[]);
   // const debouncedSP = useCallback(debounce(fetchSubPublisher, 5000), []);
    const fetchCampaign = useCallback(() => {if (campaignsFilterApi.type === "mutation"){campaignsFilterApi.result.mutate()}},[]);
   // const debouncedC = useCallback(debounce(fetchCampaign, 5000), []);
    const fetchChannel = useCallback(() => { if (channelsFilterApi.type === "mutation"){channelsFilterApi.result.mutate()}},[]);
   // const debouncedCH = useCallback(debounce(fetchChannel, 5000), []);


   useEffect(() => {
      if (limitBR|| currentPageBR||searchTermBR) {
        startLoading();

        debouncedFetchBlockedG();
      }
    }, [limitBR, currentPageBR,searchTermBR]);
    useEffect(() => {
      if (limitM|| currentPageM||searchTermM) {
        startLoading();

        debouncedFetchBlockedM();
      }
    }, [limitM, currentPageM,searchTermM]);
  
    useEffect(() => {
      if (limitG ||currentPageG||searchTermG) {
        startLoading();

        debouncedFetchOverallG();
      }
    }, [ limitG, currentPageG,searchTermG]);

    useEffect(() => {
      if (limitB||currentPageB||searchTermB) {
        startLoading();

        debouncedFetchUnsafeB();
      }
    }, [ limitB, currentPageB,searchTermB]);
  
    useEffect(() => {
      if (limitAO|| currentPageAO||searchTermAO) {
        startLoading();

        debouncedFetchGoogleAO();
      }
    }, [ limitAO, currentPageAO,searchTermAO]);

    useEffect(() => {
      if (limitFO|| currentPageFO||searchTermFO) {
        startLoading();
        debouncedFetchFacebookAO();
      }
    }, [limitFO, currentPageFO,searchTermFO]);

    const fetchAllData = useCallback(() => {
      startLoading();
      fetchBlockedG();
      fetchBlockedM();
      fetchUnsafeB();
      fetchOverallG();
      fetchGoogleAO();
      fetchFacebookAO();
      fetchGoogleData();
      fetchMetaData();
      fetchOverallData();
      fetchUnsafeData();
      fetchGOptimData();
      fetchFoptimData();
      fetchPublisher();
      fetchSubPublisher();
      fetchCampaign();
      fetchChannel();
    }, []);
  

 useEffect(() => {
   if (
     selectedPackage &&
     startDate &&
     endDate ) {
     fetchAllData(); 
   }
 }, [selectedPackage, startDate, endDate]);

useEffect(() => {
  if (
    selectedPackage &&
    startDate &&
    endDate ) {
    fetchAllData();
  }
}, [loadedFilter]);

 
  return (
    <div className='grid gap-2 w-full'>
      <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-4 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5 py-2">
      <Filter filter={filter} onChange={handleFilterChange} />
              </div>
    <div className="gap-1 w-full">
    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>IP Details</div>
    <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2  sm:grid-cols-1 w-full gap-2 min-h-[140px] lg:min-h-[170px] xl:min-h-[90px]">
    <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[0] = el;
    }} className='p-2 h-[330px]'>
      <DonutChart
        chartData={GoogleIP}
        chartConfig={chartConfigMetaIP}
        onExport={() => onExport("png", " Google Ip Details", 0)}
        onExpand={() => handleExpand(0)}
        title="Google"
        dataKey="percentage"   
        nameKey="label" 
        isView={false}
        isPercentage={false}
        isLabelist={false}
        marginTop='mt-0'
        isLoading={isLoading}
      />
      </Card>
      <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[1] = el!;
    }} className='p-2 h-[330px]'>
       <DonutChart
        chartData={MetaIP}
        chartConfig={chartConfigMetaIP}
        onExport={() => onExport("png", "Meta Ip Details", 1)}
        onExpand={() => handleExpand(1)}
        title="Meta"
        dataKey="percentage"   
        nameKey="label" 
        isView={false}
        isPercentage={false}
        isLabelist={false}
        marginTop='mt-0'
        isLoading={isLoading}
      />
     
    </Card>
    <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[2] = el!;
    }} className='p-2'>
         <HeaderRow
        title='Blocked Report'
        onExport={() => onExport("png", "Blocked Report Google", 2)}
        onExpand={() => handleExpand(2)}
        />
      <ResizableTable
        isPaginated={true}
        columns={BlockedRepeatG}
        data={isLoading ? [] :blockG}
        isLoading={isLoading}
        headerColor="#DCDCDC"
        height={300}
        isEdit={false}
        isSearchable={true}
        setSearchTerm={setSearchTermBR}
        SearchTerm={searchTermBR}
        onLimitChange={(newLimit: number) => {
          startLoading();

          setLimitBR(newLimit);
        }}
        onPageChangeP={(newPage: number) => {
          startLoading();

          setCurrentPageBR(newPage);
        }}
        pageNo={currentPageBR}
        totalPages={TotalRecordBRG}
        
      />
      </Card>
      <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[1] = el!;
    }} className='p-2'>
         <HeaderRow
        title='Blocked Report'
        onExport={() => onExport("png", "Blocked Report META", 3)}
        onExpand={() => handleExpand(3)}
        />
      <ResizableTable
        isPaginated={true}
        columns={BlockedRepeatG}
        data={isLoading ? [] :blockM}
        isLoading={isLoading}
        headerColor="#DCDCDC"
        height={300}
        isEdit={false}
        isSearchable={true}
        setSearchTerm={setSearchTermM}
        SearchTerm={searchTermM}
        onLimitChange={(newLimit: number) => {
          startLoading();

          setLimitM(newLimit);
        }}
        onPageChangeP={(newPage: number) => {
          startLoading();

          setCurrentPageM(newPage);
        }}
        pageNo={currentPageM}
        totalPages={TotalRecordBRM}
      />
    </Card>
    </div>
    </div>
    {/* Row 2 */}
    <div className="labelgap-1 w-full">
    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Placement Details</div>
    <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2  sm:grid-cols-1 w-full gap-2 min-h-[140px] lg:min-h-[170px] xl:min-h-[90px]">
    <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[4] = el!;
    }}  className='p-2 h-[330px]'>
      <DonutChart
        chartData={Overall}
        chartConfig={chartConfigOverallPlacement}
        onExport={() => onExport("png", " Overall Placements", 4)}
        onExpand={() => handleExpand(4)}
        title="Overall Placements"
        dataKey="visit"   
        nameKey="label" 
        isdonut={false}
        isView={false}
        isPercentage={true}
        isLabelist={false}
        marginTop='mt-0'
        isLoading={isLoading}
      />
      </Card>
      <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[5] = el!;}}
      className='p-2 h-[330px]'>
         <DonutChart
        chartData={Unsafe}
        chartConfig={chartConfigUnsafePlacement}
        onExport={() => onExport("png", "Unsafe Placements", 5)}
        onExpand={() => handleExpand(5)}
        title="Unsafe Placements"
        dataKey="visit"   
        nameKey="label" 
        isView={false}
        isPercentage={true}
        isLabelist={false}
        marginTop='mt-0'
        isLoading={isLoading}
        
      />
        
    </Card>
    <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[6] = el!;
    }} className='p-2' >
        <HeaderRow
        title='Good Placements'
        onExport={() => onExport("png", "Good Placements", 6)}
        onExpand={() => handleExpand(3)}
        />
      <ResizableTable
        isPaginated={true}
        columns={OverallGoodP}
        data={isLoading ? [] :OverallG}
        isLoading={isLoading}
        headerColor="#DCDCDC"
        height={300}
        isEdit={false}
        isSearchable={true}
        setSearchTerm={setSearchTermG}
        SearchTerm={searchTermG}
        onLimitChange={(newLimit: number) => {
          startLoading();

          setLimitG(newLimit);
        }}
        onPageChangeP={(newPage: number) => {
          startLoading();

          setCurrentPageG(newPage);
        }}
        pageNo={currentPageG}
        totalPages={TotalRecordOG}
        
      />
      </Card>
     <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[6] = el!;}}
       className='p-2'>
     <HeaderRow
        title='Unsafe / Bad Placements'
        onExport={() => onExport("png", "Unsafe / Bad Placements", 6)}
        onExpand={() => handleExpand(6)}
        />
      <ResizableTable
        isPaginated={true}
        columns={BadP}
        data={isLoading ? [] :UnsafeB}
        isLoading={isLoading}
        headerColor="#DCDCDC"
        height={300}
        isEdit={false}
        isSearchable={true}
        setSearchTerm={setSearchTermB}
        SearchTerm={searchTermB}
        onLimitChange={(newLimit: number) => {
          startLoading();

          setLimitB(newLimit);
        }}
        onPageChangeP={(newPage: number) => {
          startLoading();

          setCurrentPageB(newPage);
        }}
        pageNo={currentPageB}
        totalPages={TotalRecordBP}
      />
    </Card>
    </div>
    </div>
    {/* Row 3 */}
    <div className="gap-1 w-full">
    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Audience Optimization</div>
    <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2  sm:grid-cols-1 w-full gap-2 min-h-[140px] lg:min-h-[170px] xl:min-h-[90px]">
    <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[7] = el!;
    }} className='p-2 h-[330px]'>
      <DonutChart
        chartData={GoogleP}
        chartConfig={chartConfigGoogleOptimization}
        onExport={() => onExport("png", "Google Audience Optimization", 7)}
        onExpand={() => handleExpand(7)}
        title="Google"
        dataKey="percentage"   
        nameKey="label" 
        isView={false}
        isPercentage={false}
        isLabelist={true}
        marginTop='mt-0'
        totalV={TotalP}
        isLoading={isLoading}
      />
      </Card>
      <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[8] = el!;
    }} className='p-2 h-[330px]'>
      <DonutChart
        chartData={FacebookP}
        chartConfig={chartConfigGoogleOptimization}
        onExport={() => onExport("png", " FaceBook Audience Optimization", 8)}
        onExpand={() => handleExpand(8)}
        title="Facebook"
        dataKey="percentage"   
        nameKey="label" 
        isView={false}
        isPercentage={false}
        isLabelist={true}
        marginTop='mt-0'
        totalV={TotalF}
        isLoading={isLoading}
      />
     
    </Card>
    <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[9] = el!;
    }} className='p-2 '>
         <HeaderRow
        title='Google'
        onExport={() => onExport("png", "Google", 9)}
        onExpand={() => handleExpand(9)}
        />
       <ResizableTable
        isPaginated={true}
        columns={GoogleAO}
        data={isLoading ? [] :GoogleA}
        isLoading={isLoading}
        headerColor="#DCDCDC"
        height={300}
        isEdit={false}
        isSearchable={true}
        setSearchTerm={setSearchTermAO}
        SearchTerm={searchTermAO}
        onLimitChange={(newLimit: number) => {
          startLoading();

          setLimitAO(newLimit);
        }}
        onPageChangeP={(newPage: number) => {
          startLoading();

          setCurrentPageAO(newPage);
        }}
        pageNo={currentPageG}
        totalPages={TotalRecordAG}
      />
      </Card>
      <Card ref={(el: HTMLDivElement | null): void => {
      if (el) cardRefs.current[10] = el!;
    }} className='p-2 '>
       <HeaderRow
        title='Facebook'
        onExport={() => onExport("png", "Facebook", 10)}
        onExpand={() => handleExpand(10)}
        />
      <ResizableTable
        isPaginated={true}
        columns={GoogleAO}
        data={isLoading ? [] :FacebookAO}
        isLoading={isLoading}
        headerColor="#DCDCDC"
        height={300}
        isEdit={false}
        isSearchable={true}
        setSearchTerm={setSearchTermFO}
        SearchTerm={searchTermFO}
        onLimitChange={(newLimit: number) => {
          startLoading();
          setLimitFO(newLimit);
        }}
        onPageChangeP={(newPage: number) => {
          startLoading();
          setCurrentPageFO(newPage);
        }}
        pageNo={currentPageG}
        totalPages={TotalRecordAF}
      />
    </Card>
    </div>
    </div>
    </div>
  );
}
export default Actionable_insights;
