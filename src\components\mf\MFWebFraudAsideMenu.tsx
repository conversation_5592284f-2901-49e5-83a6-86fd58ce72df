"use client";
import clsx from "clsx";
import {
  ChevronDown,
  Dot,
  Globe,
  LayoutDashboard,
  Settings,
  Download,
  NotebookPen,
  NotepadText,
  Mails,
} from "lucide-react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import React, { useRef, useEffect, useState } from "react";
import { Button } from "../ui/button";
import { useApiCall } from "@/app/(main)/webfraud/queries/api_base";
import Endpoint from "@/app/(main)/webfraud/common/endpoint";
import ToastContent, { ToastType } from "./ToastContent";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { usePackage } from "./PackageContext";
import { fetchMenuWithPackage } from "@/lib/menu-utils";
// Create a client
const queryClient = new QueryClient();

type MenuItem = {
  title: string;
  icon: React.ReactNode;
  subMenu?: MenuItem[];
  route?: string;
  embeddedMenu?: boolean;
  url?: string;
};

type ApiMenuItem = {
  Name: string;
  Route: string;
  Icon: string;
  Permission: string[];
  SubMenus: ApiMenuItem[];
  EmbeddedMenu?: boolean;
  Url?: string;
};

const convertApiResponseToMenu = (apiMenu: ApiMenuItem[]): MenuItem[] => {
  return apiMenu.map((item) => {
    const parseSvgString = (svgString: string) => {
      if (!svgString) return <Dot size={18} />;
      const cleanSvg = svgString
        .replace(/\\"/g, '"')
        .replace(/class=/g, "className=");
      const div = document.createElement("div");
      div.innerHTML = cleanSvg;
      const svgElement = div.firstChild as SVGElement;

      if (svgElement) {
        const attributes: { [key: string]: string } = {};
        Array.from(svgElement.attributes).forEach((attr) => {
          const reactAttr = attr.name.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
          attributes[reactAttr] = attr.value;
        });

        return (
          <svg {...attributes}>
            {Array.from(svgElement.children).map((child, index) => {
              const TagName = child.tagName;
              const props = Object.fromEntries(
                Array.from(child.attributes).map((attr) => [attr.name, attr.value])
              );
              return React.createElement(TagName, { ...props, key: index });
            })}
          </svg>
        );
      }

      return <Dot size={18} />;
    };

    return {
      title: item.Name,
      icon: parseSvgString(item.Icon),
      route: item?.Route || undefined,
      subMenu: item?.SubMenus ? convertApiResponseToMenu(item?.SubMenus) : undefined,
    };
  });
};

function MFWebFraudAsideMenuContent({
  isExpanded = true,
  onHover = (e: boolean) => console.log(e),
  theme = "light",
}) {
  const router = useRouter();
  const pathName = usePathname();

  const [menuData, setMenuData] = useState<MenuItem[]>([]);
  const [toastData, setToastData] = useState<{
    type: ToastType;
    title: string;
    description?: string;
    variant?: "default" | "destructive" | null;
  } | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const { selectedPackage } = usePackage();

  // API call to fetch menu data with package name
  const fetchMenuData = async () => {
    try {
      const token = localStorage.getItem("IDToken");
      if (!token) {
        console.error("No token available");
        return;
      }

      // Get the current product name from the pathname
      let productName = "App Performance"; // fallback
      if (pathName.startsWith("")) {
        productName = "App Performance";
      } else if (pathName.startsWith("/web")) {
        productName = "Web Performance";
      } else if (pathName.startsWith("/brand-infringement")) {
        productName = "Brand Infringement";
      }

      const menuData = await fetchMenuWithPackage(token, productName, selectedPackage);
      const convertedMenu = convertApiResponseToMenu(menuData);
      setMenuData(convertedMenu);
    } catch (error) {
      console.error("Error fetching menu:", error);
      // setToastData({
      //   type: "error",
      //   title: "Error",
      //   description: "Failed to fetch menu data.",
      //   variant: "default",
      // });
    }
  };

  useEffect(() => {
    if (!isInitialized) {
      fetchMenuData();
      setIsInitialized(true);
    }
  }, [isInitialized]);

  

  // ✅ Display hardcoded menu + API menu
  const displayMenu = [ ...menuData];


  // Only call API when selectedPackage changes
  useEffect(() => {
    if (isInitialized && selectedPackage) {
      fetchMenuData();
    }
  }, [selectedPackage]);
 
  // Use fallback menu if API data is empty or failed
 // const displayMenu = menuData;
  
  const getAllRoutes = (menu: MenuItem[]): string[] => {
    let routes: string[] = [];
    menu.forEach((item) => {
      if (item.route) {
        routes.push(item.route);
      }
      if (item.subMenu) {
        routes = routes.concat(getAllRoutes(item.subMenu));
      }
    });
    return routes;
  };

  useEffect(() => {
    if (pathName === "/webfraud/DashboardPage/NotFound") return;

    const skipMenuValidationRoutes = [
      "/web-analytics/reportingtool/generate",
      "/webfraud/User-Management/Product_Mapping",
    ];
    if (skipMenuValidationRoutes.some((route) => pathName === route || pathName.startsWith(route + "/"))) {
      return;
    }

    if (displayMenu.length > 0 && pathName.startsWith("/webfraud/")) {
      const allRoutes = getAllRoutes(displayMenu);
      const isValidRoute = allRoutes.some((route) => route && (pathName === route || pathName.startsWith(route + "/")));
      if (!isValidRoute) {
        router.push("/dashboard/overall-summary");
      }
    }

    if (displayMenu.length > 0 && getAllRoutes(displayMenu).length === 0 && pathName.startsWith("/webfraud/")) {
      router.push("/web-analytics/dashboardPage/NotFound");
    }
    
  }, [displayMenu, pathName, router]);

  return (
    <div
      className={clsx(
        "z-[10] flex h-full flex-col bg-clip-border text-gray-300 shadow-xl transition-all",
        {
          "bg-gray-900": theme === "dark",
          "bg-secondary": theme === "light",
          "fixed bottom-0 left-0 top-14 w-[14rem] max-w-[14rem] p-2 md:relative md:top-auto": isExpanded,
          "w-0 max-w-[4rem] md:w-full md:p-1": !isExpanded,
        }
      )}
      onMouseEnter={() => onHover(true)}
      onMouseLeave={() => onHover(false)}
    >
      {toastData && (
        <ToastContent
          type={toastData.type}
          title={toastData.title}
          description={toastData.description}
          variant={toastData.variant}
        />
      )}

      <nav className="text-blue-gray-700 flex flex-col gap-1 overflow-auto font-sans text-base font-normal">
        {displayMenu.map((v, i) => (
          <MenuItem key={i} {...v} isExpanded={isExpanded} theme={theme} />
        ))}
      </nav>

      <div className="relative z-50 mx-auto mt-auto">
        <h5 className="text-blue-gray-900 block font-sans text-xl font-semibold leading-snug tracking-normal antialiased delay-300">
          {isExpanded ? (
            <Image
              src="https://infringementportalcontent.mfilterit.com/images/media/logos/mfilterit-white-logo.png"
              alt="logo"
              width={200}
              height={30}
            />
          ) : (
            <Image
              src="https://infringementportalcontent.mfilterit.com/images/Icon.png"
              alt="logo"
              width={50}
              height={30}
            />
          )}
        </h5>
      </div>
    </div>
  );
}

function MFWebFraudAsideMenu(props: {
  isExpanded?: boolean;
  onHover?: (e: boolean) => void;
  theme?: string;
}) {
  return (
    <QueryClientProvider client={queryClient}>
      <MFWebFraudAsideMenuContent {...props} />
    </QueryClientProvider>
  );
}

export default MFWebFraudAsideMenu;

type MenuItemProps = MenuItem & { isExpanded?: boolean; theme?: string };

function MenuItem({
  title = "Dashboard",
  icon = "",
  route,
  subMenu = [],
  isExpanded,
  theme,
  embeddedMenu = false,
  url,
}: MenuItemProps & { embeddedMenu?: boolean; url?: string }) {
  const pathName = usePathname();
  const router = useRouter();

  // Helper to check if this menu or any of its submenus is active
  const isRouteActive = (itemRoute: string | undefined, subMenu: MenuItem[]): boolean => {
    if (itemRoute && pathName === itemRoute) return true;
    return subMenu.some((sub) => isRouteActive(sub.route, sub.subMenu || []));
  };

  // Open if this menu or any of its submenus is active
  const [Open, setOpen] = useState(() => isRouteActive(route, subMenu));

  useEffect(() => {
    // Expand/collapse when route changes
    setOpen(isRouteActive(route, subMenu));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathName]);

  return (
    <div className="relative my-1 block w-full">
      <div
        role="button"
        className="flex overflow-hidden rounded-lg p-0 text-start leading-tight outline-none transition-all hover:bg-primary hover:bg-opacity-80"
      >
        <button
          type="button"
          className={clsx(
            "flex w-full select-none items-center border-b-0 py-0 px-1 text-left font-sans text-base font-semibold leading-snug antialiased transition-colors",
            { "justify-start": isExpanded },
            { "justify-center": !isExpanded },
            {
              // Apply hover and selected styles for dark theme
              "bg-gray-700 hover:bg-primary text-white": theme === "dark" && (pathName === route),
              "bg-gray-700 hover:bg-primary": theme === "dark" && (pathName !== route),
              // Apply hover and selected styles for light theme
              "bg-primary text-white": theme === "light" && (pathName === route),
              "bg-secondary hover:bg-primary": theme === "light" && (pathName !== route),
            }
          )}
          onClick={() => {
            if (embeddedMenu && url && route) {
              // Use the route from the API and append the url as a query param
              router.push(`${route}?url=${encodeURIComponent(url)}`);
            } else if (route) {
              router.push(route);
            }
            setOpen(!Open);
          }}
        >
          <div className="mr-2 grid w-[18px] place-items-center">
            {icon || <Dot size={18} />}
          </div>
          {isExpanded && (
            <p
              title={title}
              className="mr-auto block overflow-hidden text-ellipsis whitespace-nowrap font-sans text-base font-normal leading-relaxed antialiased p-2"
              style={{ fontSize: "0.8rem" }}
            >
              {title}
            </p>
          )}
          {subMenu.length > 0 && isExpanded && (
            <span className={clsx("ml-4 transition-all", { "rotate-180": !Open })}>
              <ChevronDown size={18} />
            </span>
          )}
        </button>
      </div>

      {subMenu.length > 0 && isExpanded && (
        <div
          className={clsx("overflow-hidden transition-all", { "h-0": Open === false })}
        >
          <div className="ml-2 block py-1 font-sans text-sm font-light leading-normal antialiased">
            {subMenu.map((v, i) => (
              <MenuItem key={i} {...v} isExpanded={isExpanded} theme={theme} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}