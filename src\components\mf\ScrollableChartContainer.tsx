import React from 'react';

interface ScrollableChartContainerProps {
  children: React.ReactNode;
  dataLength: number;
  itemWidth?: number;
  height?: number;
  showScrollbar?: boolean;
  scrollbarHeight?: number;
  scrollbarColor?: string;
  scrollbarHoverColor?: string;
  minDataLengthForScrollbar?: number;
  forceScrollbar?: boolean;
  responsiveScrollbar?: boolean;
  className?: string;
}

const ScrollableChartContainer: React.FC<ScrollableChartContainerProps> = ({
  children,
  dataLength,
  itemWidth = 80,
  height = 260,
  showScrollbar = true,
  scrollbarHeight = 6,
  scrollbarColor = 'rgba(156, 163, 175, 0.3)',
  scrollbarHoverColor = 'rgba(156, 163, 175, 0.5)',
  minDataLengthForScrollbar = 10,
  forceScrollbar = false,
  responsiveScrollbar = true,
  className = ''
}) => {
  // Calculate width based on data length and item width
  // Always ensure minimum width for proper scrolling behavior
  const calculatedWidth = Math.max(dataLength * itemWidth, 800);
  
  // Generate unique CSS class names to avoid conflicts
  const scrollbarClass = `scrollable-chart-scrollbar-${Math.random().toString(36).substr(2, 9)}`;
  
  // Determine if scrollbar should be visible based on data length and container width
  // Force scrollbar if explicitly requested, otherwise use data length threshold
  const shouldShowScrollbar = showScrollbar && (forceScrollbar || dataLength > minDataLengthForScrollbar);
  
  return (
    <div className={`w-full ${className}`}>
      <div
        className={`w-full overflow-x-auto ${shouldShowScrollbar ? scrollbarClass : ''}`}
        style={{
          scrollbarWidth: shouldShowScrollbar ? 'thin' : 'none',
          scrollbarColor: shouldShowScrollbar ? `${scrollbarColor} transparent` : 'transparent transparent',
          // Ensure scrollbar is always visible when needed
          overflowX: shouldShowScrollbar ? 'auto' : 'hidden',
          // Ensure the container takes full width
          width: '100%',
          minWidth: '100%'
        }}
      >
        {shouldShowScrollbar && (
          <style dangerouslySetInnerHTML={{
            __html: `
              .${scrollbarClass}::-webkit-scrollbar {
                height: ${scrollbarHeight}px !important;
                display: block !important;
                visibility: visible !important;
              }
              .${scrollbarClass}::-webkit-scrollbar-track {
                background: transparent;
                border-radius: ${scrollbarHeight / 2}px;
                display: block !important;
              }
              .${scrollbarClass}::-webkit-scrollbar-thumb {
                background: ${scrollbarColor} !important;
                border-radius: ${scrollbarHeight / 2}px;
                display: block !important;
                visibility: visible !important;
              }
              .${scrollbarClass}::-webkit-scrollbar-thumb:hover {
                background: ${scrollbarHoverColor} !important;
              }
              .${scrollbarClass}::-webkit-scrollbar-thumb:active {
                background: ${scrollbarHoverColor} !important;
              }
              /* Ensure scrollbar is always visible on all screen sizes */
              ${responsiveScrollbar ? `
                @media (max-width: 768px) {
                  .${scrollbarClass}::-webkit-scrollbar {
                    height: ${scrollbarHeight}px !important;
                    display: block !important;
                  }
                }
                @media (max-width: 480px) {
                  .${scrollbarClass}::-webkit-scrollbar {
                    height: ${Math.max(scrollbarHeight - 1, 4)}px !important;
                  }
                }
              ` : ''}
            `
          }} />
        )}
        <div style={{ 
          width: calculatedWidth, 
          height,
          minWidth: '100%',
          // Ensure the content area covers the full available space
          display: 'flex',
          justifyContent: 'flex-start'
        }}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default ScrollableChartContainer; 