import { Card, CardHeader, CardContent } from "@/components/ui/card";
import React from "react";

interface CenteredStatCardProps {
  label: string;
  value: number | string;
  barPercent?: number;
  barColor?: string;
  heightClass?: string;
  valueOnTop?: boolean;
  percentage?: string;
}

const CenteredStatCard: React.FC<CenteredStatCardProps> = ({
  label,
  value,
  barPercent,
  barColor = "#3b82f6",
  heightClass = "h-40",
  valueOnTop = false,
  percentage,
}) => {
  const renderContent = () => (
    <>
      <div className="flex items-center justify-center gap-2">
        <span className="font-semibold text-2xl text-primary">{value}</span>
              {percentage && (
        <span className="px-3 py-1 text-primary rounded-full text-sm font-medium font-semibold" style={{ backgroundColor: 'rgb(241 207 223)' }}>
          {percentage}
        </span>
      )}
        </div>
      <span className="font-medium text-base text-center text-gray-700">{label}</span>
    </>
  );

  return (
    <Card 
      className={`flex flex-col items-center justify-center ${heightClass} relative overflow-hidden`}
      style={{
        borderRight: `5px solid ${barColor}`,
        backgroundColor: `${barColor}15`  // 15 is hex for 8% opacity
      }}
    >
      <CardContent className="w-full flex flex-col items-center justify-center h-full gap-2">
        {renderContent()}
      </CardContent>
    </Card>
  );
};

export default CenteredStatCard; 