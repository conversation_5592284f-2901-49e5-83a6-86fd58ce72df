"use client";

import { useApiCall } from "./api_base";

// import Endpoint from "../common/endpoint";
 
// Types for API responses

export interface CreateTicketResponse {

  ticket_id: string;

  message: string;

  status: string;

}
 
// Ticket data structure from API

export interface TicketData {

  _id: string;

  author_name: string;

  email: string[];

  email_flag: boolean;

  project: string;

  tracker: string;

  subject: string;

  description: string;

  status: "new" | "open" | "in_progress" | "resolved" | "closed" | "cancelled" | "draft";

  priority: "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate";

  assignee: string;

  category: string;

  start_date: string;

  due_date: string;

  estimate_time: number;

  percent_done: string;

  meta: Record<string, any>;

  batch_id?: string;

  ticket_id: string;

  type?: string;
  mfe_id?: string | null;
  created_at: string;
  incident_date?: string | null;

}
 
// Tickets API response

export interface TicketsResponse {

  message: string;

  result: {

    data: TicketData[];

    total_records: number;

    total_pages: number;

    current_page: number;

  };

  status: string;

}
 
export interface CreateTicketRequest {

  project: string;

  tracker: string;

  subject: string;

  email?: string[];

  author_name?: string;

  description: string;

  status: "new" | "open" | "in_progress" | "resolved" | "closed" | "cancelled" | "draft";

  priority: "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate";

  assignee: string;

  category: string;

  start_date: string;

  due_date: string;

  estimate_time: number;

  percent_done: string;

  sub_projects_of?: string;

}
 
 
// Users API types

export interface User {

    email: string;

    name: string;

  }

  export interface UsersResponse {

    message: string;

    status: string;

    data: User[];

  }
 
// Ticket log types

export interface TicketLogDetails {

  title?: string;

  priority?: string;

  category?: string;

  assignee?: string;

  tracker?: string;

  status?: string;

  // New fields for detailed change tracking

  assignee_change?: {

    old: string;

    new: string;

  };

  status_change?: {

    old: string;

    new: string;

  };

  email?: {

    old: string[];

    new: string[];

  } | string[];

  author_name?: {

    old: string;

    new: string;

  } | string;

  subject?: {

    old: string;

    new: string;

  } | string;

  description?: {

    old: string;

    new: string;

  } | string;

  percent_done?: {

    old: string;

    new: string;

  } | string;

  assignee_update_message?: {

    old: string | null;

    new: string | null;

  } | string | null;

}
 
export interface TicketLogState {

  author_name?: string;

  email?: string[];

  project?: string;

  tracker?: string;

  subject?: string;

  description?: string;

  status?: string;

  priority?: string;

  assignee?: string;

  category?: string;

  start_date?: string;

  due_date?: string;

  estimate_time?: number;

  percent_done?: string;

  ticket_id?: string;

  created_at?: string;

  _id?: string;

  // Additional fields from new API response

  email_flag?: boolean;

  meta?: Record<string, any>;

  assignee_update_message?: string;

  updated_at?: string;

  updated_by?: string;

}
 
export interface TicketLogData {

  _id: string;

  ticket_id: string;

  action_by: string;

  action_type: string;

  action_description?: string;

  timestamp: string;

  details: TicketLogDetails;

  previous_state: TicketLogState | null;

  new_state: TicketLogState;

}
 
export interface TicketLogResponse {

  message: string;

  result: TicketLogData[];

  status: string;

}
 
// API endpoints

const TICKET_API_BASE = "https://dev-ticket-protal.mfilterit.net/v1/ticket_portal/ticket_portal";
 
// Create ticket API hook

export const useCreateTicket = () => {

  return useApiCall<CreateTicketResponse>({

    url: `${TICKET_API_BASE}/create_tickets`,

    method: "POST",

    manual: true, // This makes it a mutation that needs to be triggered manually

  });

};
 
// Get tickets API hook with pagination

export const useGetTickets = (pageNumber?: number, recordLimit?: number) => {

  return useApiCall<TicketsResponse>({

    url: "https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/social_media/tickets/view_tickets",

    method: "POST",

    manual: true, // Make it a mutation so we can control when it runs

  });

};
 
// Update ticket API hook

export const useUpdateTicket = (ticketId?: string) => {

  return useApiCall<CreateTicketResponse>({

    url: ticketId ? `${TICKET_API_BASE}/tickets/${ticketId}` : `${TICKET_API_BASE}/tickets/`,

    method: "PUT",

    manual: true,

  });

};
 
// Delete ticket API hook (for future use)

export const useDeleteTicket = () => {

  return useApiCall<{ message: string; status: string }>({

    url: `${TICKET_API_BASE}/delete_ticket`,

    method: "DELETE",

    manual: true,

  });

};
 
// Get ticket by ID API hook

export const useGetTicketById = () => {

  return useApiCall<TicketData>({

    url: "dummy", // Dummy URL, will be overridden in trigger

    method: "GET",

    manual: true, // Make it a mutation so we can control when it runs

  });

};
 
// Get ticket log API hook

export const useGetTicketLog = (ticketId?: string) => {

  console.log("🎯 useGetTicketLog called with ticketId:", ticketId);

  const url = ticketId ? `${TICKET_API_BASE}/ticket_log/${ticketId}` : "";

  console.log("🔗 Constructed URL:", url);

  return useApiCall<TicketLogResponse>({

    url,

    method: "GET",

    queryKey: ["ticket-log", ticketId || ""], // Add query key to ensure refetch when ticketId changes

    // The query will be enabled automatically when ticketId is provided and token is available

  });

};
 
 
// package api

export const useFetchProjects = () => {

  return useApiCall<string[]>({

    url: "https://central-apis-dev.mfilterit.net/dev/api/v1/access_control/user_packages",

    manual: true,

    method: "POST",

    headers: {

      "Content-Type": "application/json",

    },

  });

};
 
 
// Fetch users API hook

export const useFetchUsers = () => {

  return useApiCall<UsersResponse>({

    url: "https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/website_app/incidents/get_assignee",

    method: "POST",

    manual: true, // Make it a mutation so we can control when it runs with package name

  });

};
 
// Ticket overview API hook

export const useTicketOverview = () => {

  return useApiCall({

    url: "https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/social_media/tickets/ticket_overview",

    method: "POST",

    manual: true,

  });

};
 
 
 
 