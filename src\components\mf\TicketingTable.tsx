"use client";
import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  MdEdit,
  MdDelete,
  MdVisibility,
  MdFileDownload,
  MdArrowDropDown,
  MdSearch,
  MdArrowDownward,
  MdArrowUpward,
  MdClose,
  MdUnfoldMore,
  MdMoreHoriz,
  MdAdd,
} from "react-icons/md";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import Pagination from "../ui/pagination";
import {
  Select,
  SelectItem,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Calendar,
  Tag,
  User,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  RefreshCw,
} from "lucide-react";
import { Button } from "../ui/button";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

import EllipsisTooltip from "@/components/mf/EllipsisTooltip";
import { Badge } from "@/components/ui/badge";
// import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// Define the Ticket interface
export interface Ticket {
  id: string;
  title: string;
  description: string;
  environment: string;
  project: string;
  status:
    | "new"
    | "open"
    | "in_progress"
    | "resolved"
    | "closed"
    | "cancelled"
    | "draft";
  priority: "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate";
  author_name: string;
  caller?: string;
  assignee?: string;
  lastUpdated: string;
  createdAt: string;
  eta: string;
  diagnosis: string;
  attachments: number;
  tags: string[];
  percent_done?: string;
  start_date?: string;
  due_date?: string;
  estimate_time?: string;
  tracker?: string;
  takeDown?: string;
  // New fields from API response
  ticket_id?: string;
  subject?: string;
  type?: string;
  mfe_id?: string | null;
  created_at?: string;
  incident_date?: string | null;
  comments: Array<{
    id: string;
    author: string;
    content: string;
    timestamp: string;
    isInternal: boolean;
    isCheckbox?: boolean;
  }>;
  activityHistory?: Array<{
    id: string;
    type:
      | "status_change"
      | "priority_change"
      | "assignee_change"
      | "comment_added"
      | "ticket_created"
      | "ticket_updated"
      | "attachment_added";
    description: string;
    author: string;
    timestamp: string;
    oldValue?: string;
    newValue?: string;
    metadata?: Record<string, any>;
  }>;
}

export type Column<T = any> =
  | { title: string; key: string; width?: number }
  | {
      title: string;
      key: string;
      width?: number;
      render: (data: T) => React.ReactNode;
    };

interface TicketingTableProps {
  data: Ticket[];
  isCheckbox?: boolean;
  ischeckboxbody?: boolean;
  columns: Column<Ticket>[]; // Add columns as a prop
  isLoading?: boolean;
  onView?: (ticket: Ticket) => void;
  onEdit?: (ticket: Ticket) => void;
  onDelete?: (ticketId: string) => void;
  onBulkAction?: (action: string, selectedTickets: string[]) => void;
  onSearch?: (searchTerm: string) => void;
  onStatusFilter?: (status: string) => void;
  onTakeDownFilter?: (takeDown: string) => void;
  onPriorityFilter?: (priority: string) => void;
  onCreate?: () => void;
  showCreateButton?: boolean;
  searchTerm?: string;
  statusFilter?: string;
  takeDownFilter?: string;
  priorityFilter?: string;
  emptyStateMessage?: string;
  height?: number;
  // Pagination props
  isPaginated?: boolean;
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  currentPage?: number;
  itemsPerPage?: number;
  totalPages?: number;
  totalRecords?: number;
}

const ColumnToggleMenu: React.FC<{
  columns: Column<Ticket>[];
  onToggle: (key: string) => void;
  visibleColumns: Column<Ticket>[];
}> = ({ columns, onToggle, visibleColumns }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center justify-between gap-2 h-9 px-3"
        >
          <span>Columns</span>
          <div className="flex items-center">
            <span className="text-xs text-primary">
              {columns.length === visibleColumns.length
                ? "All"
                : visibleColumns.length}
            </span>
            <MdArrowDropDown className="ml-1" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-0">
        <div className="max-h-[300px] overflow-auto">
          {columns.map((column) => (
            <div
              key={column.key}
              className="flex items-center px-4 py-2 hover:bg-muted"
            >
              <Checkbox
                checked={visibleColumns.some((col) => col.key === column.key)}
                onCheckedChange={() => onToggle(column.key)}
                id={`column-${column.key}`}
              />
              <Label
                htmlFor={`column-${column.key}`}
                className="ml-2 cursor-pointer flex-1"
              >
                {column.title}
              </Label>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

const TicketingTable: React.FC<TicketingTableProps> = ({
  data,
  columns, // Add columns prop
  isLoading = false,
  onView,
  onEdit,
  onDelete,
  onBulkAction,
  onSearch,
  onStatusFilter,
  onTakeDownFilter,
  onPriorityFilter,
  onCreate,
  showCreateButton = false,
  searchTerm = "",
  statusFilter = "all",
  takeDownFilter = "all",
  priorityFilter = "all",
  emptyStateMessage = "No tickets found!",
  height = 600,
  // Pagination props
  isPaginated = true,
  onPageChange,
  onLimitChange,
  currentPage = 1,
  itemsPerPage = 10,
  totalPages,
  totalRecords,
  isCheckbox = false,
  ischeckboxbody = false,
}) => {
  // State management
  const [selectedTickets, setSelectedTickets] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [currentPageState, setCurrentPageState] = useState(currentPage);
  const [itemsPerPageState, setItemsPerPageState] = useState(itemsPerPage);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [internalSearchTerm, setInternalSearchTerm] = useState(searchTerm);
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [containerHeight, setContainerHeight] = useState(height);

  // Refs
  const headerRef = useRef<HTMLDivElement>(null);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const tableRef = useRef<HTMLTableElement>(null);

  // Status and priority configurations
  const statusColors = {
    new: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    open: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    in_progress:
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    resolved:
      "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    closed: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    draft: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
  };

  const statusIcons = {
    new: <AlertCircle className="w-3 h-3" />,
    open: <Clock className="w-3 h-3" />,
    in_progress: <RefreshCw className="w-3 h-3" />,
    resolved: <CheckCircle className="w-3 h-3" />,
    closed: <CheckCircle className="w-3 h-3" />,
    cancelled: <XCircle className="w-3 h-3" />,
    draft: <AlertCircle className="w-3 h-3" />,
  };

  const priorityColors = {
    Low: "bg-green-500",
    Normal: "bg-blue-500",
    Medium: "bg-yellow-500",
    High: "bg-orange-500",
    Urgent: "bg-red-500",
    Immediate: "bg-red-600",
  };


  const [visibleColumns, setVisibleColumns] =
    useState<Column<Ticket>[]>(columns);

  // Update visibleColumns when columns prop changes
  useEffect(() => {
    setVisibleColumns(columns);
  }, [columns]);

  // Initialize column widths
  useEffect(() => {
    const initialWidths: Record<string, number> = {};
    columns.forEach((column) => {
      initialWidths[column.key] = column.width || 150;
    });
    setColumnWidths(initialWidths);
  }, [columns]);

  useEffect(() => {
    setIsMounted(true);
    const calculateHeight = () => {
      if (headerRef.current) {
        const headerHeight = headerRef.current.offsetHeight;
        const availableHeight = height - headerHeight - 100; // 100px for pagination
        setContainerHeight(availableHeight);
      }
    };
    calculateHeight();
    window.addEventListener("resize", calculateHeight);
    return () => window.removeEventListener("resize", calculateHeight);
  }, [height]);

  // Sync internal state with external props
  useEffect(() => {
    if (currentPage !== undefined) {
      setCurrentPageState(currentPage);
    }
  }, [currentPage]);

  useEffect(() => {
    if (itemsPerPage !== undefined) {
      setItemsPerPageState(itemsPerPage);
    }
  }, [itemsPerPage]);

  // Filter and sort data
  const filteredData = React.useMemo(() => {
    let filtered = data;

    // Search filter
    if (internalSearchTerm) {
      filtered = filtered.filter(
        (ticket) =>
          ticket.title
            .toLowerCase()
            .includes(internalSearchTerm.toLowerCase()) ||
          ticket.description
            .toLowerCase()
            .includes(internalSearchTerm.toLowerCase()) ||
          ticket.caller
            ?.toLowerCase()
            .includes(internalSearchTerm.toLowerCase()) ||
          ticket.author_name
            ?.toLowerCase()
            .includes(internalSearchTerm.toLowerCase()) ||
          ticket.id.toLowerCase().includes(internalSearchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((ticket) => ticket.status === statusFilter);
    }

    // TakeDown filter
    if (takeDownFilter !== "all") {
      filtered = filtered.filter(
        (ticket) => ticket.takeDown === takeDownFilter
      );
    }

    // Sorting
    if (sortConfig) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[sortConfig.key as keyof Ticket];
        const bValue = b[sortConfig.key as keyof Ticket];
        // Handle undefined values
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined)
          return sortConfig.direction === "asc" ? -1 : 1;
        if (bValue === undefined)
          return sortConfig.direction === "asc" ? 1 : -1;
        if (aValue < bValue) return sortConfig.direction === "asc" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [data, internalSearchTerm, statusFilter, takeDownFilter, sortConfig]);

  // Use external pagination if provided, otherwise use internal
  const totalPagesState =
    totalPages || Math.ceil(filteredData.length / itemsPerPageState);
  const paginatedData = onPageChange
    ? data
    : filteredData.slice(
        (currentPageState - 1) * itemsPerPageState,
        currentPageState * itemsPerPageState
      );

  // Handlers
  const handleColumnToggle = (key: string) => {
    setVisibleColumns((prev) =>
      prev.some((col) => col.key === key)
        ? prev.filter((col) => col.key !== key)
        : [...prev, columns.find((col) => col.key === key)!]
    );
  };

  const handleSort = (key: string) => {
    setSortConfig((current) => ({
      key,
      direction:
        current?.key === key && current.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleTicketSelect = (ticketId: string) => {
    setSelectedTickets((prev) =>
      prev.includes(ticketId)
        ? prev.filter((id) => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleSelectAll = () => {
    if (selectedTickets.length === paginatedData.length) {
      setSelectedTickets([]);
    } else {
      setSelectedTickets(paginatedData.map((t) => t.id));
    }
  };

  const handleSearchChange = (value: string) => {
    setInternalSearchTerm(value);
    onSearch?.(value);
    setCurrentPageState(1); // Reset to first page on search
    if (onPageChange) {
      onPageChange(1);
    }
  };

  const downloadTableAsCSV = useCallback(() => {
    const headers = visibleColumns.map((col) => col.title).join(",");
    const rows = filteredData
      .map((ticket) =>
        visibleColumns
          .map((col) => {
            const value = ticket[col.key as keyof Ticket];
            return typeof value === "string"
              ? `"${value.replace(/"/g, '""')}"`
              : value;
          })
          .join(",")
      )
      .join("\n");
    const csv = `${headers}\n${rows}`;
    const blob = new Blob([csv], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `tickets-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [filteredData, visibleColumns]);

  if (!isMounted) return null;

  const colSpan = visibleColumns.length + 2; // +2 for checkbox and actions

  return (
    <div className="w-full mt-[10px] flex flex-col h-full">
      {/* Table Controls - Top Bar */}
      <div
        ref={headerRef}
        className="flex flex-col md:flex-row w-full gap-2 rounded-lg border bg-card p-2 text-body"
      >
        {/* Left Side Controls */}
        <div className="flex flex-1 flex-wrap md:flex-nowrap items-center gap-2">
          {/* Search Bar */}
          <div
            className={cn(
              "flex items-center space-x-2 p-2 border rounded-md",
              isSearchExpanded ? "w-full" : "w-full md:flex-1"
            )}
          >
            {isSearchExpanded ? (
              <>
                <MdSearch className="text-xl text-card-foreground" />
                <input
                  type="text"
                  placeholder="Search tickets..."
                  value={internalSearchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full bg-card text-card-foreground outline-none"
                  autoFocus
                />
                <button
                  onClick={() => {
                    setIsSearchExpanded(false);
                    handleSearchChange("");
                  }}
                  className="md:hidden"
                >
                  <MdClose className="text-xl" />
                </button>
              </>
            ) : (
              <>
                <MdSearch
                  className="text-xl text-card-foreground md:hidden"
                  onClick={() => {
                    setIsSearchExpanded(true);
                    handleSearchChange("");
                  }}
                />
                <span className="md:hidden">Search</span>
                <div className="hidden md:flex w-full items-center">
                  <MdSearch className="text-xl text-card-foreground" />
                  <input
                    type="text"
                    placeholder="Search tickets..."
                    value={internalSearchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="w-full bg-card text-card-foreground outline-none"
                  />
                </div>
              </>
            )}
          </div>

          {/* Filters */}
          <Select
            value={statusFilter}
            onValueChange={(value) => {
              onStatusFilter?.(value);
              setCurrentPageState(1);
              if (onPageChange) {
                onPageChange(1);
              }
            }}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Ticket Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="raised">Raised</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              {/* <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem> */}
            </SelectContent>
          </Select>

          <Select
            value={takeDownFilter}
            onValueChange={(value) => {
              onTakeDownFilter?.(value);
              setCurrentPageState(1);
              if (onPageChange) {
                onPageChange(1);
              }
            }}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="TakeDown Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="No Action Requested">No Action Requested</SelectItem>
              <SelectItem value="Hold Case">Hold Case</SelectItem>
              <SelectItem value="Initiated">Initiated</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
              {/* <SelectItem value="Urgent">Urgent</SelectItem>
              <SelectItem value="Immediate">Immediate</SelectItem> */}
            </SelectContent>
          </Select>

          <Select
            value={priorityFilter}
            onValueChange={(value) => {
              onPriorityFilter?.(value);
              setCurrentPageState(1);
              if (onPageChange) {
                onPageChange(1);
              }
            }}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="Low">Low</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="High">High</SelectItem>
            </SelectContent>
          </Select>

          {/* Column Toggle */}
          <ColumnToggleMenu
            columns={columns}
            onToggle={handleColumnToggle}
            visibleColumns={visibleColumns}
          />

        </div>

        {/* Right Side Controls */}
        <div className="flex md:justify-end gap-2 flex-wrap">
          {/* Bulk Actions */}
          {selectedTickets.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-9">
                  Bulk Actions
                  <MdArrowDropDown className="ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() => onBulkAction?.("close", selectedTickets)}
                >
                  Close Selected
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onBulkAction?.("delete", selectedTickets)}
                  className="text-red-600"
                >
                  Delete Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Download Button */}
          {/* <Button
            variant="outline"
            size="icon"
            onClick={downloadTableAsCSV}
            title="Download Table Data as CSV"
            className="h-9 w-9"
          >
            <MdFileDownload className="h-4 w-4" />
          </Button> */}

          {/* Create Ticket Button */}
          {showCreateButton && (
            <Button
              onClick={onCreate}
              title="Create New Ticket"
              className=" dark:text-white"
            >
              <MdAdd className="h-4 w-4 mr-2" />
              Create Ticket
            </Button>
          )}
        </div>
      </div>

      {/* Table Container */}
      <div
        ref={tableContainerRef}
        className="relative flex-1 border border-t-0 rounded-b-lg overflow-hidden"
        style={{ height: `${containerHeight}px` }}
      >
        <div className="overflow-auto h-full w-full">
          <Table ref={tableRef} className="min-w-full">
            <TableHeader className="sticky top-0 z-10 bg-gray-50 dark:bg-gray-800 p-0">
              <TableRow>
                {/* Select All Checkbox */}
                {isCheckbox && (
                  <TableHead
                    className="border-r bg-gray-50 dark:bg-gray-800"
                    style={{ width: "50px", minWidth: "50px" }}
                  >
                    <Checkbox
                      checked={
                        selectedTickets.length === paginatedData.length &&
                        paginatedData.length > 0
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                )}

                {/* Column Headers */}
                {visibleColumns.map((column) => (
                  <TableHead
                    key={column.key}
                    className="relative border-r p-0 bg-gray-50 dark:bg-gray-800"
                    style={{
                      width: `${columnWidths[column.key]}px`,
                      whiteSpace: "nowrap",
                    }}
                  >
                    <div className="flex items-center justify-between px-2">
                      <div className="flex-1 overflow-hidden">
                        <span
                          className="block truncate text-sm font-bold text-gray-900 dark:text-white"
                          title={column.title}
                        >
                          {column.title}
                        </span>
                      </div>
                      <div className="flex items-center ml-2">
                        <button
                          onClick={() => handleSort(column.key)}
                          className="cursor-pointer p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                          title={`Sort by ${column.title}`}
                        >
                          {sortConfig?.key === column.key ? (
                            sortConfig.direction === "asc" ? (
                              <MdArrowUpward className="text-primary text-sm" />
                            ) : (
                              <MdArrowDownward className="text-primary text-sm" />
                            )
                          ) : (
                            <MdUnfoldMore className="text-gray-400 text-sm hover:text-gray-600" />
                          )}
                        </button>
                      </div>
                    </div>
                  </TableHead>
                ))}

                {/* Actions Header */}
                <TableHead
                  className="border-r bg-gray-50 dark:bg-gray-800"
                  style={{ width: "100px", minWidth: "100px" }}
                >
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    Actions
                  </span>
                </TableHead>
              </TableRow>
            </TableHeader>

            <TableBody className="border border-border">
              {isLoading ? (
                <TableRow className="h-16">
                  <TableCell colSpan={colSpan} className="h-32">
                    <div className="flex justify-center items-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  </TableCell>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={colSpan} className="h-32">
                    <div className="flex justify-center items-center h-full">
                      <span className="text-small-font font-medium">
                        {internalSearchTerm.trim()
                          ? "No matching tickets found"
                          : emptyStateMessage}
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData.map((ticket, index) => (
                  <TableRow
                    key={ticket.id}
                    className="h-8 hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    {/* Row Checkbox */}
                    {ischeckboxbody && (
                      <TableCell
                        className="border-r p-4"
                        style={{ width: "50px", minWidth: "50px" }}
                      >
                        <Checkbox
                          checked={selectedTickets.includes(ticket.id)}
                          onCheckedChange={() => handleTicketSelect(ticket.id)}
                        />
                      </TableCell>
                    )}

                    {/* Data Cells */}
                    {visibleColumns.map((column) => (
                      <TableCell
                        key={column.key}
                        className="border-r dark:text-white text-base-font p-2"
                        style={{
                          maxWidth: `${columnWidths[column.key]}px`,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {"render" in column ? (
                          <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                            {column.render(ticket)}
                          </div>
                        ) : (
                          <EllipsisTooltip
                            content={String(
                              ticket[column.key as keyof Ticket] || ""
                            )}
                          />
                        )}
                      </TableCell>
                    ))}

                    {/* Actions Cell */}
                    <TableCell
                      className="border-r p-2"
                      style={{ width: "100px", minWidth: "100px" }}
                    >
                      <div className="flex space-x-2 justify-center">
                        {onView && (
                          <button
                            onClick={() => onView(ticket)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdVisibility size={18} />
                          </button>
                        )}
                        {onEdit && (
                          <button
                            onClick={() => onEdit(ticket)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdEdit size={18} />
                          </button>
                        )}
                        {onDelete && (
                          <button
                            onClick={() => onDelete(ticket.id)}
                            className="text-primary hover:text-gray-500"
                          >
                            <MdDelete size={18} />
                          </button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* External Pagination Controls */}
        {isPaginated && !isLoading && data.length > 0 && (
          <div className="mt-2 flex flex-col sm:flex-row items-center justify-between gap-2 p-2 border rounded-lg bg-card">
            <div className="flex items-center gap-2">
              <Select
                value={String(itemsPerPageState)}
                onValueChange={(value) => {
                  const newLimit = Number(value);
                  setItemsPerPageState(newLimit);
                  setCurrentPageState(1);
                  // Call parent handler for external pagination
                  if (onLimitChange) {
                    onLimitChange(newLimit);
                  }
                }}
              >
                <SelectTrigger className="w-[70px] h-[30px] outline-none focus:ring-0 text-small-font dark:text-white">
                  <SelectValue placeholder="Rows" />
                </SelectTrigger>
                <SelectContent className="border-none outline-none focus:ring-0">
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Pagination
              currentPage={currentPageState}
              totalPages={totalPagesState}
              onPageChange={(page) => {
                setCurrentPageState(page);
                if (onPageChange) {
                  onPageChange(page);
                }
              }}
              showFirstLast={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default TicketingTable;