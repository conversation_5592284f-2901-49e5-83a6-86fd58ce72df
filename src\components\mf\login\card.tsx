import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useCallback, useState } from "react";
import LoginForm from "../forms/login";
import { OTPForm } from "../forms/OTP";
import {
  LoginBodyType,
  MFAVerifyBodyType,
  MFAVerifyError,
  useLogin,
  useMFAVerify,
} from "@/queries";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import Report from "./Report";
import { IoIosWarning } from "react-icons/io";

type FormType = "login" | "otp";

const FormCard = () => {
  const router = useRouter();
  const [FormType, setFormType] = useState<FormType>("login");
  const [showReportCard, setShowReportCard] = useState(false);

  const onSuccess = (res: any) => {
    // MFA loginx
    if (res?.data?.challenge_name === "SOFTWARE_TOKEN_MFA") setFormType("otp");
    // Normal login   
    else if (res.data?.auth_response?.AuthenticationResult?.AccessToken) {
      //   TODO: Fetch menu redirect route
      router.push("/dashboard");
      localStorage.setItem(
        "AccessToken",
        res?.data?.auth_response?.AuthenticationResult?.AccessToken,
      );
      localStorage.setItem(
        "IDToken",
        res?.data?.auth_response?.AuthenticationResult?.IdToken,
      );
    }
  };
   
  const loginFn = useLogin(console.debug, onSuccess);

  const onMFAVerifySuccess = (d: any) => {
    const AccessToken =
      d?.data?.auth_response?.AuthenticationResult?.AccessToken;
    if (AccessToken) {
      router.push("/webfraud/event-visit/overall-summary");
      localStorage.setItem("AccessToken", AccessToken);
      localStorage.setItem(
        "IDToken",
        d?.data?.auth_response?.AuthenticationResult?.IdToken,
      );
      localStorage.removeItem("username");
    }
  };

  const onMFAVerifyError = (e: MFAVerifyError) => {
    toast({ title: e.message, variant: "destructive" });
  };

  const MFAVerify = useMFAVerify(onMFAVerifyError, onMFAVerifySuccess);

  const handleSubmit = useCallback((body: LoginBodyType) => {
    localStorage.setItem("username", body.username);
    if (!loginFn.isLoading) loginFn.mutate({ body });
  }, []);

  const handleOTPSubmit = (otp: string) => {
    const body: MFAVerifyBodyType = {
      challenge_name: loginFn.data?.data.challenge_name,
      user_code: otp,
      session_token: loginFn.data?.data.session_token,
      username: localStorage.getItem("username") ?? "",
    };
    if (!MFAVerify.isLoading) MFAVerify.mutate({ body });
  };

  const onReportClick = () => {
    setShowReportCard(true);
  };

  return (
    <div className="flex flex-col items-center justify-center p-8 px-0 py-0 md:w-5/12">
      {showReportCard && (
        <div 
          className="fixed inset-0 backdrop-blur-sm z-0" 
          onClick={() => setShowReportCard(false)}
        />
      )}

      <div 
        className={`z-10 w-full max-w-md space-y-6 rounded-lg bg-white p-8 shadow-lg ${
          showReportCard ? 'blur-sm pointer-events-none' : ''
        }`}
      >
        {FormType === "login" && (
          <LoginFormCard
            buttonText={loginFn.isLoading ? "Logging in..." : "Login"}
            onSubmit={handleSubmit}
            errorMessage={loginFn.error?.message ?? ""}
          />
        )}
        {FormType === "otp" && (
          <OTPForm
            title="MFA Verification"
            description="Enter MFA code"
            buttonText="Verify"
            onSubmit={handleOTPSubmit}
          />
        )}

        <div className="mt-4  flex justify-center items-center cursor-pointer self-center" onClick={onReportClick}>
          <IoIosWarning className="report-icon" size={38} />
        </div>
      </div>

      {showReportCard && <Report onClose={() => setShowReportCard(false)} />}
    </div>
  );
};

export default FormCard;

const LoginFormCard: React.FC<{
  buttonText: string;
  errorMessage: string | null;
  onSubmit: (data: LoginBodyType) => void;
}> = ({ onSubmit, errorMessage, buttonText }) => {
  return (
    <>
      <LoginForm
        beforeForm={<h2 className="text-center text-header">Login</h2>}
        buttonText={buttonText}
        onSubmit={onSubmit}
        errorMessage={errorMessage}
        afterForm={
          <div className="mt-4 flex flex-col items-center space-y-2 text-sm">
            <Link href="/forgot-password">
              <Button variant="link" size="sm" className="m-0 h-fit p-0">
                Forgot Password
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <span>Don&apos;t have an account?</span>
              <Link href="/sign-up" replace>
                <Button variant="link" size="sm" className="m-0 h-fit p-0">
                  Sign Up
                </Button>
              </Link>
            </div>
          </div>
        }
      />
    </>
  );
};
