"use client"
import React, { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import ResizableTable from '@/components/mf/TableComponent';
import HeaderRow from '@/components/mf/HeaderRow';
import { onExpand, downloadURI,debounce } from '@/lib/utils';
import { useState, useCallback, useRef, useEffect } from 'react';
import domToImage from "dom-to-image";
//import { ChartConfig } from '@/components/ui/chart';
import DynamicBarChart from '@/components/mf/DynamicBarChart';
import Donut<PERSON>hart from '@/components/mf/DonutChart';
import HorizontalVerticalBarChart from '@/components/mf/HorizontalVerticalBarChart';
import DoubleLine<PERSON>hart from '@/components/mf/DoubleLineChart';
import StackedBarChart from '@/components/mf/stackedBarChart'
import { useApiCall } from "../../queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import { Filter } from "@/components/mf/Filters";
import { useDateRange } from "@/components/mf/DateRangeContext";
import Endpoint from '../../common/endpoint';
import { useLoading } from '@/components/mf/LoadingContext';

interface FilterItem {
  label: string;
  checked: boolean;
}

interface FilterState {
  filters: FilterItem[];
  is_select_all: boolean;
  selected_count: number;
  loading: boolean;
}

interface FilterPayload {
  [key: string]: FilterState;
}
// Add type for API response
interface FilterApiResponse {
  data: string[];
  isLoading?: boolean;
}

interface ChartConfig {
  [key: string]: {
    label: string;
    color: string;
  };
}
interface ColumnUser {
  title: string,
  key: keyof UserData,
}
interface UserData {
  device_signature: string;
  publisher_name: string;
  sub_publisher_name: string;
  visit: number;
}
interface Repeatuser {
  device_repetition_percentage: string;
  data: UserData[];
  total_records: number;
  page_number: number;
  limit: number;
  total_pages: number;
  search_term: string;
}
const RepeatUser: ColumnUser[] = [
  { title: "Device Signature", key: "device_signature" },
  { title: "Publisher Name", key: "publisher_name" },
  { title: "Sub Publisher", key: "sub_publisher_name" },
  { title: "Visit", key: "visit" },
]
//SuspiciousB
interface Botuser {
  label: string;
  visit: number;
  percentage: number;
  fill: string;
  [key: string]: string | number;
}
interface SuspiciousB {
  suspicious_behaviour: SuspiciousBehaviour;
  bot_fraud_percentage: string;
  data: Botuser[];
}
const chartConfigPiechart: ChartConfig = {

  desktop: {
    label: "Desktop",
    color: "#e76e50",
  },
  mobile: {
    label: "Mobile",
    color: "#2a9d90",
  },
  bot: {
    label: "Bot",
    color: "#a8a032"
  }
} satisfies ChartConfig

//Ip Repeat
interface IPData {
  label: string;
  "Total Event": number;
  "Total Visit": number;
  [key: string]: string | number;
}

interface IPResponse {
  repeat_ip_percentage: string;
  data: IPData[];
}

const chartConfigIpReport: ChartConfig = {
  "Total Event": {
    label: "Total Event",
    color: "#274754",
  },
  "Total Visit": {
    label: "Total Visit",
    color: "#e8c468",
  },
}
//server farm
interface ServerData {
  label: string;
  Desktop: number;
  Mobile: number;
  Total:number;
  [key: string]: string | number;
}

interface ServerResponse {
  //server_farm_percentage: server;
  server_farm_percentage: string;
  data: ServerData[];
}
const chartConfigServerFarm: ChartConfig = {
  Mobile: {
    label: "Mobile",
    color: "#2a9d90",
  },
  Desktop: {
    label: "Desktop",
    color: "#e76e50",
  },
  Total :{
    label: "Total",
    color: "#2dc048",
  }

}
//VPN Proxy
interface VPNData {
  label: string;
  visit: number;
  fill: string;
  [key: string]: string | number;
}
interface VPNProxys {
  vpn_proxy_percentage: string;
  data: VPNData[];
}

const chartConfigVPN: ChartConfig = {
  NA: {
    label: "NA",
    color: "#ef4444"
  },
  DCH: {
    label: "DCH",
    color: "#84cc16",
  },
  PUB: {
    label: "PUB",
    color: "#0d9488",
  },
  VP: {
    label: "VP",
    color: "#0ea5e9",
  },
  SES: {
    label: "SES",
    color: "#9333ea",
  },
  VPN: {
    label: "VPN",
    color: "#d97706",
  }
}
//Invalid GEO
interface GeoData {
  label: string;
  "Fraud %": number;
  fill: string;
  [key: string]: string | number;
}
interface InvalidData {
  data: GeoData[];
  invalid_geo_percentage: string;
}

//pop under
interface PopDataItem {
  device_type: string;
  screen_resolution: string; // e.g. "360|780"
  pop_under_percentage: number | string; // e.g. 3.86 for standard or "0.00%" for demandgen
}

interface PopResponse {
  standard: PopDataItem[];  // Array of PopDataItems for standard
  demandgen: PopDataItem[];  // Array of PopDataItems for demandgen
  pop_under_percentage: {
    pop_under_percentage: string;  // e.g. "0.00%"
  };
}

interface ChartDataItem {
  label: string;
  standard: number;
  demandgen: number;
  [key: string]: string | number;
}


const chartConfigPopUnder = {
  standard: {
    label: "Standard",
    color: "#8b5cf6",
  },
  demandgen: {
    label: "DemandGen",
    color: "#c2410c",
  },
} satisfies ChartConfig

//Imperceptiable Window
interface WindowData {
  label: string;
  count_hasIframe: number;
  device_type: string;
  [key: string]: string | number;
}
interface WindowResponse {
  data: WindowData[];
  percentage: string;
}

const chartConfigWindow = {
  desktop: {
    label: "Desktop",
    color: "#e76e50",
  },
  mobile: {
    label: "Mobile",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig

const yAxisConfig = {
  dataKey: "label", // Assuming you want to use 'label' as the key for the Y-Axis

};

const InformSuspicous =
  [
    {
      title: "Mouse Movement: FALSE",
      desc: "User has not Interacted with the website."
    },
    {
      title: "Touch Support: FALSE",
      desc: "User has not Interacted with the website.",
    },
    {
      title: "HasFocus: FALSE",
      desc: "Page is not Opening in the Active Tab",
    },
  ]

const InformPopUnder =
  [
    {
      title: "HasFocus: FALSE",
      desc: "Page is not Opening in the Active Tab",
    },
    {
      title: "17%",
      desc: "Height & Width of the browser window is small",
    },
    {
      title: "Time Spent On Page: 0 sec",
      desc: "User has not Interacted with the website."
    },
    {
      title: "Slide/ Scroll / Touch: FALSE",
      desc: "User has not Interacted with the website."
    }
  ]

const InformWindow =
  [
    {
      title: "HasFocus: FALSE",
      desc: "Page is not Opening in the Active Tab",
    },
    {
      title: "Has Iframe: 0x0",
      desc: "Height & Width of the browser window is small",
    },
    {
      title: "Time Spent On Page: 0 sec",
      desc: "User has not Interacted with the website."
    },
    {
      title: "Slide/ Scroll / Touch: FALSE",
      desc: "User has not Interacted with the website."
    }
  ]

const Analysis_insights = () => {
  // Guard against static generation issues
  if (typeof window === 'undefined') {
    return <div>Loading...</div>;
  }
  
  const cardRefs = useRef<HTMLElement[]>([]);
  const { startDate, endDate } = useDateRange();
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [existingPublisherdata, setExistingPublisherdata] = useState<string[]>([]);
  const [existingSubPublisherdata, setExistingSubPublisherdata] = useState<string[]>([]);
  const [existingCampaigndata, setExistingCampaigndata] = useState<string[]>([]);
  const [existingChanneldata, setExistingChanneldata] = useState<string[]>([]);
  const [loadedFilter, setLoadedFilter] = useState<any>({});
  const { selectedPackage } = usePackage();
  const [repeatUser, setRepeatUser] = useState<UserData[]>([]);
  const [IPrepeat, setIPRepeat] = useState<IPData[]>([]);
  const [Serverfarm, setServerfarm] = useState<ServerData[]>([]);
  const [Botusers, setBotUser] = useState<Botuser[]>([]);
  const [VPNP, setVPNP] = useState<VPNData[]>([]);
  const [Invalidgeo, setInvalidgeo] = useState<GeoData[]>([]);
  const [Popunder, setPopunder] = useState<ChartDataItem[]>([]);
  const [ImpWindow, setImpWindow] = useState<WindowData[]>([]);
  const [currentPagep, setCurrentPagep] = useState(1);
  const [filteredData, setFilteredData] = useState<ServerData[]>();
  const [limitps, setLimitp] = useState(10);
  const [device_tyeI, setDevice_tyeI] = useState("desktop");
  const [device_tyeP, setDevice_tyeP] = useState("desktop");
  const [label_value, setlabel_value] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(0);
  const [Repeatp, setRepeatp] = useState<string>("");
  const [Suspiciousp, setSuspiciousp] = useState<string>("");
  const [searchTermRU, setSearchTermRU] = useState("");
  const [IPPercentage, setIPPercentage] = useState<string>("");
  const [ServerPercentage, setServerPercentage] = useState<string>("");
  const [VPNPercentage, setVPNPercentage] = useState<string>("");
  const [GeoPercentage, setGeoPercentage] = useState<string>("");
  const [PopPercentage, setPopPercentage] = useState<string>("");
  const [ImpPercentage, setImpPercentage] = useState<string>("");
   const[TotalRecordRU,setTotalRecordRU]=useState();
    const loading = useLoading();
              if (!loading) {
                throw new Error('Loading context not found');
              }
              const { isLoading, startLoading, stopLoading } = loading;
  const [query, setQuery] = useState({
    publishers: ["all"],
    sub_publishers: ["all"],
    campaigns: ["all"],
    channels: ["all"],
  });
    const isInitialLoad = useRef(true);
    
  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];

      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs] // Include `cardRefs` as a dependency here
  );
  const handleExpand = (index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  };
  // Add new state for timeout
  const [showLoader, setShowLoader] = useState(true);

  // Publishers Filter API
  const publishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      if (data && data.data && Array.isArray(data.data)) {
        setExistingPublisherdata(data.data);
        if (data.data.length > 0) {
        }
      } else {
        setExistingPublisherdata([]);
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });

  // Sub Publishers Filter API
  const subPublishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.SUB_PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      if (data && Array.isArray(data)) {
        setExistingSubPublisherdata(data);
        if (data.length > 0) {
        }
      } else {
        setExistingSubPublisherdata([]);
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });

  // Campaigns Filter API
  const campaignsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CAMPAIGNS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      if (data && Array.isArray(data)) {
        setExistingCampaigndata(data);
        if (data.length > 0) {
        }
      } else {
        setExistingCampaigndata([]);
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });

  // Channels Filter API
  const channelsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CHANNELS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingChanneldata(data);
      if (data.length > 0) {

      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  });


  //Repeated User
  const RepeatedUser = useApiCall<Repeatuser>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.REPEAT_USERS_TABLE,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      page: currentPagep,
      limit: limitps,
      search_term: searchTermRU,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      const data = response.data;
      setShowLoader(false);
      if (Array.isArray(data)) {
        const updatedtop: UserData[] = data.map((topItem: any) => ({
          device_signature: topItem.device_signature,
          publisher_name: topItem.publisher_name,
          sub_publisher_name: topItem.sub_publisher_name,
          visit: topItem.visit,
        }))
        if (JSON.stringify(updatedtop) !== JSON.stringify(repeatUser)) {
          setRepeatUser(updatedtop);
        }
        setRepeatp(response.device_repetition_percentage)

      }
      setTotalRecordRU(response.total_pages);
      setTimeout(() => {
        stopLoading(); // Delay stopping loader
      }, 300);
    },
    onError: (error) => {
      setRepeatUser([]);
      stopLoading();
    },
  });

  //Suspicious Behaviour
  const SuspiciousBehaviour = useApiCall<SuspiciousB>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.BOT_BEHAVIOUR,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      const data = response.data;
      if (Array.isArray(data)) {
        const updatedtop: Botuser[] = data.map((topItem: any) => ({
          label: topItem.device_type,
          visit: topItem.count,
          percentage: parseFloat(topItem.percentage.replace("%", "")),
          fill: chartConfigPiechart[topItem.device_type]?.color || '#000',

        }))
        if (JSON.stringify(updatedtop) !== JSON.stringify(Botusers)) {
          setBotUser(updatedtop);
        }
        setSuspiciousp(response.suspicious_behaviour.bot_fraud_percentage);
        
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    }
  })
  // Ip Repeat API call
  const RepeatIP = useApiCall<IPResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.REPEAT_IP,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      const data = response.data;

    if (Array.isArray(data)) {
      let updatedtop: IPData[] = data.map((entry: any) => ({
        label: entry.ip_address,
        "Total Event": entry.event_count,
        "Total Visit": entry.visit_count,
      }))

      if (JSON.stringify(updatedtop) !== JSON.stringify(IPrepeat)) {
        setIPRepeat(updatedtop);
      }

      setIPPercentage(response.repeat_ip_percentage);
      
    }
    stopLoading();
  },
  onError: (error) => {
    stopLoading();
  },
});
  //Server Farm api
  const ServerFarm = useApiCall<ServerResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.HARDWARE_CONCURRENCY,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      const data = response.data;
      if (Array.isArray(data)) {
        const updatedtop: ServerData[] = data.map((topItem: any) => ({
          label: topItem.hardware_concurrency,
          Mobile: topItem.mobile_count,
          Desktop: topItem.desktop_count,
          Total:topItem.total_count,
        }))
    
        if (JSON.stringify(updatedtop) !== JSON.stringify(Serverfarm)) {
          setServerfarm(updatedtop);
          setFilteredData(updatedtop)
        }
        setServerPercentage(response.server_farm_percentage.server_farm_percentage)
        
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    }
  })
    const placeholderSF = "Select Concurrency"
    const selectedOptionSF = [placeholderSF, ...Serverfarm.map((item: any) => item.label)];  
  //  console.log("Selected hardware concurrency:", selectedOptionSF);



// Handle label change from dropdown
const handleLabelChangeSF = (value: string) => {
  setlabel_value(value); 
  if (value === placeholderSF) {
    setFilteredData(Serverfarm);
} else if (value) {
    const filteredData = Serverfarm.filter((item) => item.label === value);
   // console.log("Filtered data:", filteredData);
    setFilteredData(filteredData.length > 0 ? filteredData : []);
} else {
    setFilteredData(Serverfarm);
}
};


 
  //Vpn Proxy
  const VPNProxy = useApiCall<VPNProxys>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.VPN_PROXIES,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      const data = response.data;
      if (Array.isArray(data)) {

        const updatedtop: VPNData[] = data.map((topItem) => {
          const fraudDesc = topItem.additional_fraud_desc as keyof typeof chartConfigVPN;
          const visit = parseFloat(topItem.percentage.replace("%", "")); // Convert percentage string to number
          if (fraudDesc in chartConfigVPN) {
            return {
              label: fraudDesc,
              visit,
              fill: chartConfigVPN[fraudDesc].color,
            };
          }
          return {
            label: fraudDesc,
            visit,
            fill: "#000",
          };
        });
        if (JSON.stringify(updatedtop) !== JSON.stringify(VPNP)) {
          setVPNP(updatedtop);
        }
        setVPNPercentage(response.vpn_proxy_percentage.vpn_proxy_percentage);
       
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    }
  })

  //Invalid Geo api
  const InvalidGeo = useApiCall<InvalidData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.INVALID_GEO,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      const data = response.data;
      if (Array.isArray(data)) {
         // If there are more than 100 labels, limit the result to 100
     // const limitedData = data.length > 10 ? data.slice(0, 10) : data;

        const updatedtop: GeoData[] = data.map((topItem: any) => {
          const country = topItem.country;
          const countryColor = ConfigInvalidGeo[country]?.color || "#000"; // Fallback to black if no match
          return {
            label: country,
            "Fraud %": topItem.fraud_percentage,
            fill: countryColor,  // Assigning the correct color
          };
        });
        if (JSON.stringify(updatedtop) !== JSON.stringify(Invalidgeo)) {
          setInvalidgeo(updatedtop);
        }
        setGeoPercentage(response.invalid_geo_percentage.invalid_geo_percentage)
        
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    }
  })

  const generateColor = (index: number) => {
    const hue = (index * 137) % 360;
    return `hsl(${hue}, 70%, 50%)`;
  };

  // Assuming `data` is your array of items
  const ConfigInvalidGeo: ChartConfig = Invalidgeo.reduce((acc, item, index) => {
    const fraudPercentage = item.fraudPercentage; // This can be string | number

    // Check if fraudPercentage is a number before comparing
    const fillColor = typeof fraudPercentage === 'number' && fraudPercentage > 10 ? "red" : "green";

    acc[item.label] = {
      label: item.label,
      color: generateColor(index),
      fill: fillColor,
    };
    return acc;
  }, {} as ChartConfig);


  //fetch pop Under api
  const handleDeviceChangeP = (value: string) => {
    // Convert frequency to the appropriate format
    startLoading();
    const deviceMap: { [key: string]: string } = {
        Desktop: "desktop",
        Mobile: "mobile"
    };
    
    setDevice_tyeP(deviceMap[value]);
    
  };
  const selectOptionsP = ["Desktop", "Mobile"];
  const PopUnder = useApiCall<PopResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.POP_UNDER,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
      device_type: device_tyeP,
    },
    onSuccess: (response) => {
  
      const { standard, demandgen } = response;
     

    // Map the data to the desired format
    const chartData: ChartDataItem[] = [];

   // Map standard data
   standard.forEach((standardItem) => {
    const formattedLabel = standardItem.screen_resolution;

    const demandgenItem = demandgen.find(
      (item) => item.screen_resolution === standardItem.screen_resolution
    );

    chartData.push({
      label: formattedLabel,
      standard: standardItem.pop_under_percentage,
      demandgen: demandgenItem
        ? parseFloat(demandgenItem.pop_under_percentage.replace('%', ''))
        : 0,
    });
  });

  // Add any demandgen items that don't have a matching standard item
  demandgen.forEach((demandgenItem) => {
    if (!chartData.some((item) => item.label === demandgenItem.screen_resolution)) {
      chartData.push({
        label: demandgenItem.screen_resolution,
        standard: 0,
        demandgen: parseFloat(demandgenItem.pop_under_percentage.replace('%', '')) || 0,
      });
    }
  });

  //console.log("pop under", chartData);

  // Check if the new data is different from the previous data before updating the state
  if (JSON.stringify(chartData) !== JSON.stringify(Popunder)) {
    setPopunder(chartData);
  }

  setPopPercentage(response.pop_under_percentage.pop_under_percentage);
  stopLoading();
},
onError: (error) => {
  stopLoading();
},
});

const selectOptionsI = ["Desktop", "Mobile"];
 const handleDeviceChangeI = (value: string) => {
  // Convert frequency to the appropriate format
  const deviceMap: { [key: string]: string } = {
      Desktop: "desktop",
      Mobile: "mobile"
  };
  setDevice_tyeI(deviceMap[value]);
  //setIsLoading(true);
};
// Imperceptible Window API Call
const Imperceptiable = useApiCall<WindowResponse>({
  url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.IFRAME_SIZE,
  method: "POST",
  params: {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
    publishers: query.publishers,
    sub_publisher: query.sub_publishers,
    campaign: query.campaigns,
    channel: query.channels,
    device_type: device_tyeI,
  
  },
  onSuccess: (response) => {
    const data = response.data;
    if (Array.isArray(data)) {
      let formattedData: any[] = [];
      const groupedData: any = {};

      // Process the data
      data.forEach((entry: any) => {
        const { device_type, count_hasIframe, hasIframe } = entry;
          // Filter the data based on selected device type
          if (device_type === device_tyeI) {
            if (!groupedData[hasIframe]) {
              groupedData[hasIframe] = {};
            }
            groupedData[hasIframe][device_type] = count_hasIframe;
          }
        });
  

      // Convert grouped data into the desired format
      for (const [label, deviceData] of Object.entries(groupedData)) {
        formattedData.push({
          label: label,
          ...deviceData,
        });
      }

      // Check if the new data is different from the previous data before updating the state
      if (JSON.stringify(formattedData) !== JSON.stringify(ImpWindow)) {
        setImpWindow(formattedData);
      }

      setImpPercentage(response.imperceptible_Window.percentage);
     
    }
    stopLoading();
  },
  onError: (error) => {
    stopLoading();
  },
});
   
  
const filter = React.useMemo(
  () => ({
    Publishers: {
      filters:
        existingPublisherdata?.map((publisher: string) => ({
          label: publisher,
          // Change: Only check if it exists in the current selection
          checked: query.publishers?.includes("all") || 
                 query.publishers?.includes(publisher) || 
                 !query.publishers, // Default true if no selection exists
        })) || [],
      // Change: Determine if all are selected
      is_select_all: !query.publishers || 
                   query.publishers.includes("all") ||
                   query.publishers?.length === existingPublisherdata?.length,
      // Change: Actual selected count
      selected_count: query.publishers?.includes("all") 
                    ? existingPublisherdata?.length ?? 0
                    : query.publishers?.length ?? existingPublisherdata?.length ?? 0,
      loading: false,
    },
    "Sub Publishers": {
      filters:
        existingSubPublisherdata?.map((subPublisher: string) => ({
          label: subPublisher,
          checked: query.sub_publishers?.includes("all") || 
                 query.sub_publishers?.includes(subPublisher) || 
                 !query.sub_publishers,
        })) || [],
      is_select_all: !query.sub_publishers || 
                   query.sub_publishers.includes("all") ||
                   query.sub_publishers?.length === existingSubPublisherdata?.length,
      selected_count: query.sub_publishers?.includes("all")
                    ? existingSubPublisherdata?.length ?? 0
                    : query.sub_publishers?.length ?? existingSubPublisherdata?.length ?? 0,
      loading: false,
    },
    Campaigns: {
      filters:
        existingCampaigndata?.map((campaign: string) => ({
          label: campaign,
          checked: query.campaigns?.includes("all") || 
                 query.campaigns?.includes(campaign) || 
                 !query.campaigns,
        })) || [],
      is_select_all: !query.campaigns || 
                   query.campaigns.includes("all") ||
                   query.campaigns?.length === existingCampaigndata?.length,
      selected_count: query.campaigns?.includes("all")
                    ? existingCampaigndata?.length ?? 0
                    : query.campaigns?.length ?? existingCampaigndata?.length ?? 0,
      loading: false,
    },
    Channels: {
      filters:
        existingChanneldata?.map((channel: string) => ({
          label: channel,
          checked: query.channels?.includes("all") || 
                 query.channels?.includes(channel) || 
                 !query.channels,
        })) || [],
      is_select_all: !query.channels || 
                   query.channels.includes("all") ||
                   query.channels?.length === existingChanneldata?.length,
      selected_count: query.channels?.includes("all")
                    ? existingChanneldata?.length ?? 0
                    : query.channels?.length ?? existingChanneldata?.length ?? 0,
      loading: false,
    },
  }),
  [
    existingPublisherdata,
    existingSubPublisherdata,
    existingCampaigndata,
    existingChanneldata,
    query.publishers,
    query.sub_publishers,
    query.campaigns,
    query.channels,
  ]
);
      const deepEqual = (arr1: any[], arr2: any[]) => {
        if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
        if (arr1.length !== arr2.length) return false;
      
        return arr1.every((item, index) =>
          JSON.stringify(item) === JSON.stringify(arr2[index])
        );
      };

  // Add this new function to fetch all APIs
  const fetchSBData = useCallback(() => {if (SuspiciousBehaviour.type === "mutation") { startLoading();SuspiciousBehaviour.result.mutate()}},[]);
  const fetchRIData = useCallback(() => {if (RepeatIP.type === "mutation") {startLoading();RepeatIP.result.mutate()}},[]);
    const fetchVPNData = useCallback(() => {if (VPNProxy.type === "mutation") {startLoading();VPNProxy.result.mutate()}},[]);
      const fetchAIGData = useCallback(() => {if (InvalidGeo.type === "mutation") {startLoading();InvalidGeo.result.mutate()}},[]);
      const fetchPopUnder= useCallback(() => {if (PopUnder.type === "mutation") {startLoading();PopUnder.result.mutate();}; }, [PopUnder])
        // 1. Create the basic fetch function
const fetchRepeatUser = useCallback(() => {
  if (RepeatedUser.type === "mutation") {
    startLoading();
    console.log("🟢 Fetching repeat users...");
    RepeatedUser.result.mutate();
  }
}, [RepeatedUser.type, RepeatedUser.result]);

// 2. Properly memoize the debounced version
const debouncedFetchRepeatUser = useMemo(
  () => debounce(fetchRepeatUser, 200),
  [fetchRepeatUser]
);
              const fetchImperceptiable= useCallback(() => {if (Imperceptiable.type === "mutation") {startLoading();Imperceptiable.result.mutate()};}, [])
        const fetchServerFarm= useCallback(() => {if (ServerFarm.type === "mutation") {startLoading();ServerFarm.result.mutate();setFilteredData(Serverfarm);};}, [])
      

 
           //filter 
                const fetchPublisher = useCallback(() => {if (publishersFilterApi.type === "mutation"){ startLoading();publishersFilterApi.result.mutate()}},[]);
                const fetchSubPublisher = useCallback(() => {if (subPublishersFilterApi.type === "mutation"){ startLoading();subPublishersFilterApi.result.mutate()}},[]);
                  const fetchCampaign = useCallback(() => {if (campaignsFilterApi.type === "mutation"){ startLoading();campaignsFilterApi.result.mutate()}},[]);
                  const fetchChannel = useCallback(() => { if (channelsFilterApi.type === "mutation"){ startLoading();channelsFilterApi.result.mutate()}},[]);
 // Debounced search handler
const debouncedSearch = useCallback(
  debounce((term: string) => {
    setCurrentPagep(1); // Reset to first page on search
    startLoading();
    setSearchTermRU(term);
  }, 1000), // 500ms delay
  []
);

  // Update the handleFilterChange function
   const handleFilterChange = useCallback(
     async (newState: Record<string, any>) => {
      startLoading();

       const payload = {
         publishers: newState.Publishers?.is_select_all
           ? ['all']
           : newState.Publishers?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         sub_publishers: newState['Sub Publishers']?.is_select_all
           ? ['all']
           : newState['Sub Publishers']?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         campaigns: newState.Campaigns?.is_select_all
           ? ['all']
           : newState.Campaigns?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
         channels: newState.Channels?.is_select_all
           ? ['all']
           : newState.Channels?.filters
               .filter((f: any) => f.checked)
               .map((f: any) => f.label),
       };
   
       setQuery(payload);
       const filtersChanged =
       !deepEqual(newState.Publishers?.filters || [], loadedFilter.Publishers?.filters || []) ||
       !deepEqual(newState['Sub Publishers']?.filters || [], loadedFilter['Sub Publishers']?.filters || []) ||
       !deepEqual(newState.Campaigns?.filters || [], loadedFilter.Campaigns?.filters || []) ||
       !deepEqual(newState.Channels?.filters || [], loadedFilter.Channels?.filters || []);

     if (filtersChanged) {
       setLoadedFilter(newState);
     }
   },
   [loadedFilter]
   );   

   const fetchAllData = useCallback(() => {
    startLoading();
    fetchPublisher();
    fetchSubPublisher();
    fetchCampaign();
    fetchChannel();
    fetchSBData(),
    fetchRIData(),
    fetchVPNData(),
    fetchAIGData(),
    fetchRepeatUser(),
    fetchPopUnder(),
    fetchImperceptiable(),
    fetchServerFarm()
   
  }, []);

  useEffect(() => {
   if (limitps || searchTermRU || currentPagep) {
     startLoading();
    debouncedFetchRepeatUser();// Update Repeat User table only
    }
  }, [limitps, searchTermRU, currentPagep]);
  

useEffect(() => {
  if (selectedPackage) {
    startLoading();
    fetchPopUnder();
  }
}, [device_tyeP]);

useEffect(() => {
  if (selectedPackage) {
    startLoading();
    fetchImperceptiable();
  }
}, [device_tyeI]);  

 // Handle initial load + changes for selectedPackage, startDate, and endDate
 useEffect(() => {
  // Only call on initial load when no filters are applied
  if (
    selectedPackage &&
    startDate &&
    endDate ) {
    fetchAllData(); // Initial load without filter
  }
}, [selectedPackage, startDate, endDate]);

useEffect(() => {
  if (selectedPackage && loadedFilter && Object.keys(loadedFilter).length > 0) {
    fetchAllData(); // Refetch all APIs only when filters change
  }
}, [loadedFilter]);


  return (
    <div className='grid gap-2 w-full'>
      <div className="sticky top-0 z-[50] sm:z-[40] w-full flex flex-cols-4 flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5 py-2">
        <Filter filter={filter} onChange={handleFilterChange} />
      </div>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Invalid Traffic(SIVT)</div>
        <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2  sm:grid-cols-1 w-full gap-2 min-h-[140px] ">
          <Card ref={(el) => (cardRefs.current[0] = el!)} className='p-2'>
            <HeaderRow
              title={`Repeated User: ${Repeatp}`}
              onExport={() => onExport("png", "Repeated User", 0)}
              onExpand={() => handleExpand(0)}
              isRadioButton={false}
              isSelect={false}
            />
            <ResizableTable
              isPaginated={true}
              columns={RepeatUser}
              data={isLoading ? [] : repeatUser}              
              isLoading={isLoading}
              headerColor="#DCDCDC"
              height={250}
              isEdit={false}
              isSearchable={true}
              SearchTerm={searchTermRU}
              setSearchTerm={(term: string) => {
                debouncedSearch(term);
              }}
              onLimitChange={(newLimit: number) => {
                
                startLoading();
               //setRepeatUser([]);
                setLimitp(newLimit);
                setCurrentPagep(1);
              }}
              onPageChangeP={(newPage: number) => {
                startLoading();
                //setRepeatUser([]);
                setCurrentPagep(newPage);
              }}
              pageNo={currentPagep}
              totalPages={TotalRecordRU}
            />
          </Card>
          <Card ref={(el) => (cardRefs.current[1] = el!)} className='p-2  h-[450px] md:overflow-x-auto overflow-y-auto scrollbar '>
            {/* <PieCharts
              chartData={Botusers}
              chartConfig={chartConfigPiechart}
              title={`Suspicious Behaviour: ${Suspiciousp}`}
              onExport={() => onExport("png", "Suspicious Behaviour %", 1)}
              onExpand={() => handleExpand(1)}
              isSelect={false}
              piechartitle="Bot Behaviour %"
              isLoading={isLoading}
              InformCard={InformSuspicous}
              isLoading={isLoading}
              datavalue="percentage"
            /> */}
            
             <HorizontalVerticalBarChart
              chartData={Botusers}
              chartConfig={chartConfigPiechart}
              // handleExport={handleExportInvalidGeo}
              title={`Suspicious Behaviour: ${Suspiciousp}`}
              onExport={() => onExport("png", "Suspicious Behaviour %", 1)}
              onExpand={() => handleExpand(1)}
              isSelect={false}
              BarchartTitle="Bot Behaviour %"
              isHorizontal={false}
              isLoading={isLoading}
              InformCard={InformSuspicous}
              formatterType="percentage"
              isRadioButton={false}
              dataKey="percentage"
              namekeys='label'
              position='top'
              barsize={35}
              setheight="370px"

            />
          
          </Card>
          <Card ref={(el) => (cardRefs.current[2] = el!)}>
            <CardContent className='overflow-x-auto scrollbar'>
              <DynamicBarChart
                data={IPrepeat}
                config={chartConfigIpReport}
                title={`IP Repeat: ${IPPercentage}`}
                isSelect={false}
                isLoading={isLoading}
                isRadioButton={false}
                onExport={() => onExport("png", "Ip Repeat", 2)}
                onExpand={() => handleExpand(2)}
                isHorizontal={true}
                dynamicTitle="Repeat IPs"
                formatterType="number"
              />
            </CardContent>
          </Card>
          <Card ref={(el) => (cardRefs.current[3] = el!)}>
            <CardContent className='overflow-x-auto scrollbar'>
              <DynamicBarChart
               // data={Serverfarm}
                data={filteredData}
                config={chartConfigServerFarm}
                title={`Server Farm: ${ServerPercentage}`}
                isLoading={isLoading}
               // isMultiplSelect={true}
                isRadioButton={false}
                onExport={() => onExport("png", "Server Farm", 3)}
                onExpand={() => handleExpand(3)}
                isHorizontal={true}
                dynamicTitle="Hardware Concurrency %"
                formatterType="number"
                AxisLabel="Number"
                multiple="multiple"
                isSelect={true}
                selectoptions={selectedOptionSF}
                 placeholder  = {placeholderSF}
                handleFrequencyChange={handleLabelChangeSF} 
                 selectedFrequency={label_value ?? undefined}
                 width="220px"
                
              />
            </CardContent>
          </Card>
        </div>
      </div>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Compliance Issues</div>
        <div className=" grid grid-cols-1 lg:grid-cols-3 md:grid-cols-3  sm:grid-cols-1 w-full gap-2 min-h-[140px]">
          <Card ref={(el) => (cardRefs.current[3] = el!)} className='p-2 w-full '>
            <DonutChart
              chartData={VPNP}
              chartConfig={chartConfigVPN}
              onExport={() => onExport("png", "VPN Proxy", 3)}
              onExpand={() => handleExpand(3)}
              title={`VPN Proxy (DCH): ${VPNPercentage}`}
              isLoading={isLoading}
              DonutTitle="VPN Proxies %"
              dataKey="visit"
              nameKey="label"
              isView={false}
              isPercentage={false}
              isLabelist={true}
              marginTop='mt-0'
              position='items-center'
            />
          </Card>
          <Card ref={(el) => (cardRefs.current[4] = el!)} className='p-2 w-full  col-span-2' >
            <HorizontalVerticalBarChart
              chartData={Invalidgeo}
              chartConfig={ConfigInvalidGeo}
              // handleExport={handleExportInvalidGeo}
              title={`Invalid Geo: ${GeoPercentage}`}
              onExport={() => onExport("png", "Invalid Geo", 4)}
              onExpand={() => handleExpand(4)}
              BarchartTitle="Invalid Geo %"
              isHorizontal={true}
              isLoading={isLoading}
              formatterType="percentage"
              position='right'
              isRadioButton={false}
              isSelect={false}
              dataKey="Fraud %"
              barsize={10}
              setheight="250px"

            />
          </Card>
        </div>
      </div>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Low Intent User</div>
        <div className=" grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2  sm:grid-cols-1 w-full gap-2 min-h-[140px]">
          <Card ref={(el) => (cardRefs.current[5] = el!)} className='p-2 w-full overflow-y-auto scrollbar '>
            <DoubleLineChart
              chartData={Popunder}
              chartConfig={chartConfigPopUnder}
              //handleExport={handleExportInvalidGeo}
              title={`Pop Under: ${PopPercentage}`}
              onExport={() => onExport("png", "Pop Under", 5)}
              onExpand={() => handleExpand(5)}
             // LinechartTitle="Device Type:Moblie"
              isRadioButton={false}
              isLoading={isLoading}
              isSelect={true}
              AxisLabel="Percentage"
              InformCard={InformPopUnder}
              selectoptions={selectOptionsP}
              handleFrequencyChange={handleDeviceChangeP} 
              selectedFrequency={device_tyeP}
              isInformCard={true}
               placeholder  = "Desktop"
            />
          </Card>
          <Card ref={(el) => (cardRefs.current[6] = el!)} className='p-2 overflow-y-auto scrollbar '>
            <StackedBarChart
              chartData={ImpWindow}
              chartConfig={chartConfigWindow}
              selectoptions={selectOptionsI}
              //  handleExport={handleExportWindow}
              title={`Imperceptiable Window: ${ImpPercentage}`}
              onExport={() => onExport("png", "Imperceptiable Window", 6)}
              onExpand={() => handleExpand(6)}
              isRadioButton={false}
              isSelect={true}
              isLoading={isLoading}
              handleFrequencyChange={handleDeviceChangeI} 
              selectedFrequency={device_tyeI}
              AxisLabel="Percentage"
              InformCard={InformWindow}
              isInformCard={true}
              yAxis={yAxisConfig}
              layoutDirection="flex-col"
              isLegend={true}
              ischangeLegend={false}
              placeholder  = "Desktop"
              isCartesian={false}
            />
          </Card>
        </div>
      </div>
    </div>
  );
}
export default Analysis_insights;
