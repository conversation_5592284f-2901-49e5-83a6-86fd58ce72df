import React from 'react';
import { useFullscreen } from "@/hooks/fullscreen";

interface CustomCategoryCardProps {
  title: string;
  count: string | number;
  percentage: string;
  bgColor: string;
  link: string;
  isLoading?: boolean;
  containerClassName?: string;
  countClassName?: string;
  titleClassName?: string;
  customBgColor?: string;
  onClick?: () => void;
  className?: string;
}

const CustomCategoryCard: React.FC<CustomCategoryCardProps> = ({
  title,
  count,
  percentage,
  bgColor,
  link,
  isLoading = false,
  containerClassName = '',
  countClassName = '',
  titleClassName = '',
  customBgColor,
  onClick,
  className = ''
}) => {
  const isFullscreen = useFullscreen();
  
  // Dynamic card sizing for fullscreen
  const getFullscreenCardSize = () => {
    if (window.innerWidth < 640) {
      return { minHeight: 120, padding: 3, imageSize: 40 }; // Mobile
    } else if (window.innerWidth >= 640 && window.innerWidth <= 1024) {
      return { minHeight: 140, padding: 4, imageSize: 45 }; // Tablet
    } else if (window.innerWidth > 1024 && window.innerWidth < 1280) {
      return { minHeight: 160, padding: 4, imageSize: 50 }; // Desktop
    } else {
      return { minHeight: 180, padding: 5, imageSize: 55 }; // Large screens
    }
  };

  const fullscreenSize = isFullscreen ? getFullscreenCardSize() : { minHeight: 150, padding: 4, imageSize: 50 };

  // Format number to international format
  const formatCount = (value: string | number) => {
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
    if (isNaN(numValue)) return value;
    return new Intl.NumberFormat('en-IN').format(numValue);
  };

  if (isLoading) {
    return (
      <div className={`flex flex-col items-center bg-white rounded-xl border border-gray-200 shadow-sm p-${fullscreenSize.padding} animate-pulse min-h-[${fullscreenSize.minHeight}px] h-auto`}>
        <div className="bg-gray-200 rounded-lg w-full p-3 text-center mb-3 h-[40px]" />
        <div className="bg-gray-200 rounded-full w-[50px] h-[50px] mb-2" />
        <div className="bg-gray-200 h-4 w-24 rounded mt-1" />
      </div>
    );
  }

  return (
    <div 
      className={`flex flex-col items-center bg-white rounded-xl border border-gray-200 shadow-sm p-${fullscreenSize.padding} transition hover:shadow-lg min-h-[${fullscreenSize.minHeight}px] h-auto dark:bg-gray-700 ${className}`}
      onClick={onClick}
    >
    <div
  className={`text-white rounded-lg w-full text-center py-2 px-3 ${containerClassName} bg-[#540094] dark:bg-[#2a004f]`}
  style={{ backgroundColor: customBgColor }}
>
        <div className="flex justify-between items-center">
          <div className={`font-bold ${countClassName}`}>{formatCount(count)}</div>
          {percentage && <div className="text-sm font-semibold">{percentage}</div>}
        </div>
      </div>
      {link && (
        <div className="rounded-full mb-1 flex items-center justify-center shadow-md mt-2 flex-shrink-0">
          <img
            src={link}
            alt={title}
            style={{ width: fullscreenSize.imageSize, height: fullscreenSize.imageSize, objectFit: "contain", borderRadius: "50%" }}
          />
        </div>
      )}
      <div className={`font-semibold text-sm sm:text-base text-center text-blue-900 dark:text-white ${titleClassName} break-words leading-tight mt-auto`}>{title}</div>
    </div>
  );
};

export default CustomCategoryCard; 