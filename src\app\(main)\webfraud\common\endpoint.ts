const Endpoint ={
    TRAFFIC_COUNT : "dev/api/v1/web/performance/traffic_counts",
    EVENT_TRAFFIC : "dev/api/v1/web/performance/event_traffic",
    VISIT_TRAFFIC : "dev/api/v1/web/performance/visit_traffic",
    TOP_CAMPAIGNS : "dev/api/v1/web/performance/top_campaigns",
    TOP_PUBLISHERS : "dev/api/v1/web/performance/top_publishers",
    VISIT_TRAFFIC_PUBLISHER :"dev/api/v1/web/performance/visit_traffic_publisher",
    EVENT_TRAFFIC_PUBLISHER :"dev/api/v1/web/performance/event_traffic_publisher",
    TRAFFIC_TRENDS : "dev/api/v1/web/performance/traffic_trends",
    COLOR_API :"dev/api/v1/web/performance/colors"       ,       
    SUB_PUBLISHERS: "dev/api/v1/web/performance/filters/sub_publishers",
    CAMPAIGNS: "dev/api/v1/web/performance/filters/campaigns",
    CHANNELS: "dev/api/v1/web/performance/filters/channels",
    PUBLISHERS: "dev/api/v1/web/performance/filters/publishers",
    EVENT_TYPE: "dev/api/v1/web/performance/filters/event_type",
    REPEAT_USERS_TABLE: "dev/api/v1/web/performance/repeat_users_table",
    BOT_BEHAVIOUR :"dev/api/v1/web/performance/bot_behaviour",
    REPEAT_IP :"dev/api/v1/web/performance/repeat_ip",
    HARDWARE_CONCURRENCY :"dev/api/v1/web/performance/hardware_concurrency",
    INVALID_GEO :"dev/api/v1/web/performance/invalid_geo",
    IFRAME_SIZE :"dev/api/v1/web/performance/iframe_size",
    POP_UNDER :"dev/api/v1/web/performance/pop_under",
    VPN_PROXIES :"dev/api/v1/web/performance/vpn_proxies",
   TRAFFIC_CONTRIBUTION :"dev/api/v1/web/performance/traffic_contribution",
   TOP_CONTRIBUTING_OS_VERSIONS:"dev/api/v1/web/performance/top_contributing_os_versions",
   STATE_VISIT:"dev/api/v1/web/performance/state_visit",
   STATE_EVENT:"dev/api/v1/web/performance/state_event",
  GOOGLE:"dev/api/v1/web/performance/google",
  META:"dev/api/v1/web/performance/meta",
  BLOCKED_REPORT_GOOGLE:"dev/api/v1/web/performance/blocked_report_google",
  BLOCKED_REPORT_META:"dev/api/v1/web/performance/blocked_report_meta",
  OVERALL_PLACEMENT:"dev/api/v1/web/performance/overall_placements",
  OVERALL_PLACEMENT_TABLE:"dev/api/v1/web/performance/overall_placements_table",
  UNSAFE_PLACEMENT:"dev/api/v1/web/performance/unsafe_placements",
  UNSAFE_PLACEMENT_TABLE:"dev/api/v1/web/performance/unsafe_placements_table",
 USER_INTENT:"dev/api/v1/web/performance/user_intent",
 AUDIENCE_OPTIMIZATION_FACEBOOK:"dev/api/v1/web/performance/audience_optimization_facebook",
 OPTIMIZATION_FACEBOOK_TABLE:"dev/api/v1/web/performance/audience_optimization_facebook_table",
 AUDIENCE_OPTIMIZATION_GOOGLE_TABLE:"dev/api/v1/web/performance/audience_optimization_google_table",
 DEVICE_CONCENTRATION:"dev/api/v1/web/performance/device_concentration",
 AUDIENCE_OPTIMIZATION_GOOGLE:"dev/api/v1/web/performance/audience_optimization_google",
}
export  default Endpoint;