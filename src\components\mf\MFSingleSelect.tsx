import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

interface Item {
  title: string;
  value: string;
}

interface MFSingleSelectProps {
  items: Item[];
  placeholder?: string;
  title?: string;
  className?: string;
  value?: string;
  onValueChange?: (value: string) => void;
}

export function MFSingleSelect({
  items,
  placeholder,
  title,
  className,
  value,
  onValueChange,
}: MFSingleSelectProps) {
  // Find the selected item to get its title
  const selectedItem = items.find(item => item.value === value);
  
  return (
    <div className="flex flex-col gap-1">
      {/* {title && <p className="text-body">{title}</p>} */}
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className={cn("relative", className)}>
          <SelectValue>
            {selectedItem ? selectedItem.title : placeholder}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="z-[200]">
          {items.map((item) => (
            <SelectItem key={item.value} value={item.value}>
              {item.title}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
