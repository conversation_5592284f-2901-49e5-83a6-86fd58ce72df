import React, { useEffect, useState} from 'react';
import {
  Card,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON><PERSON>, Maximize, RotateCw } from 'lucide-react';
import { RadioButtons } from './RadioButton';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MultiSelect } from '@/components/ui/multi-select';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@radix-ui/react-tooltip';
import { Button } from '@/components/ui/button';

interface HeaderRowProps {
  heading?: string;
  sub_heading?: string;
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  selectedType?: string;
  handleFrequencyChange?: (value: string) => void; 
  selectedFrequency?:string;
  selectoptions?: string[];
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  placeholder?: string;
  isMultiplSelect?:boolean;
  width?:string;
  showRefresh?: boolean;
  onRefresh?: () => void;
  showMenuDots?: boolean;
  titleFontSize?: string;
  isExpanded?: boolean;
}

const HeaderRow: React.FC<HeaderRowProps> = ({
  handleTypeChange,
  visitEventOptions,
  selectoptions = [],
  selectedType,
  selectedFrequency,
  handleFrequencyChange,
  handleExport,
  onExport,
  onExpand,
  title,
  width="120px",
  isSelect = false,
  isRadioButton = false,
  isMultiplSelect=false,
  placeholder = "",
  showRefresh = false,
  onRefresh,
  showMenuDots = true,
  titleFontSize = "text-base",
  isExpanded = false,
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleExpandClick = () => {
    onExpand(); // Notify the parent for expansion change
    //console.log(" expand true ");
  };

  const handleRefreshClick = () => {
    if (onRefresh) {
      setIsRefreshing(true);
      onRefresh();
      // Add a small delay before resetting the animation
      setTimeout(() => setIsRefreshing(false), 1000);
    }
  };

  return (
    <Card className='border-none w-full'>
      <div className="flex flex-wrap justify-between">

          <>
            {/* Title */}
            <CardTitle className={`${titleFontSize} font-bold p-1 pl-4 text-black dark:text-white`}>
              {title}
            </CardTitle>
            <CardTitle className="flex flex-wrap space-x-4 sm:space-x-2 justify-between w-full sm:w-auto p-1 items-center">
              {/* Refresh button positioned to the left of the three dots menu */}
              {showRefresh && (
                <div className="flex justify-center items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="p-0 hover:bg-transparent"
                    onClick={handleRefreshClick}
                  >
                    <RotateCw 
                      className={`h-5 w-5 text-primary hover:text-purple-900 dark:text-white transition-all cursor-pointer ${isRefreshing ? 'animate-spin' : ''}`}
                    />
                  </Button>
                </div>
              )}
              {isRadioButton && (
                <div className="flex justify-center items-center">
                  <RadioButtons
                    options={visitEventOptions || []}
                    defaultValue={selectedType}
                    onValueChange={handleTypeChange || (() => {})}
                  />
                </div>
              )}

              {/* Only show frequency filter when NOT in expand mode */}
              {isSelect && !isExpanded && (
                <div className="flex justify-center items-center">
                 <Select value={selectedFrequency} onValueChange={handleFrequencyChange}>
                    <SelectTrigger className={`w-[90px] h-[30px] text-black dark:text-white font-medium`}>
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectGroup className="relative z-100">
                            {selectoptions.map((option, index) => (
                                <SelectItem 
                                    key={index} 
                                    value={option} 
                                    className="text-black dark:text-white font-medium"
                                >
                                    {option}
                                </SelectItem>
                            ))}
                        </SelectGroup>
                    </SelectContent>
                </Select>
                </div>
              )}
                  {/* {isMultiplSelect && (
                   <div className="flex justify-center items-center">
                     <MultiSelect
                       options={selectoptions}
                       selected={selectedFrequency || []}
                       onValueChange={handleFrequencyChange}
                       placeholder={placeholder}
                       
                     />
                   </div>
                  )} */}

              {/* Only show the three dots menu if showMenuDots is true */}
              {showMenuDots && (
                <div className="p-2 flex justify-center items-center">
                  <TooltipProvider>
                    <Tooltip>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <TooltipTrigger>
                            <div className="group">
                              <Ellipsis className="group-hover:text-blue-500" />
                            </div>
                          </TooltipTrigger>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="dark:bg-background">
                          <DropdownMenuGroup>
                            <DropdownMenuItem onClick={handleExport}>Export to CSV</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onExport?.("png", title ?? " ", 1)}>
                              Export to PNG
                            </DropdownMenuItem>
                            {/* <DropdownMenuItem>Export to Excel</DropdownMenuItem> */}
                            <DropdownMenuItem onClick={handleExpandClick}>
                              <span>{isExpanded ? 'Collapse' : 'Expand'}</span>
                              <Maximize size={20} className="ml-3" />
                            </DropdownMenuItem>
                          </DropdownMenuGroup>
                        </DropdownMenuContent>
                      </DropdownMenu>
                      <TooltipContent className="text-small-font">Select Options</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              )}
            </CardTitle>
          </>
      </div>
    </Card>
  );
};

export default HeaderRow;
