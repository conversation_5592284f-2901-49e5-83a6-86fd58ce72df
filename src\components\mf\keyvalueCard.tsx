"use client"
import React, { FC } from "react";
import { Loader2 } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface KeyValueCardProps {
  title?: string;
  leftKey?: string;
  leftValue?: number;
  percentage?: string;
  colors?: string;
  rightKey?: string;
  rightValue?: number;
  percentage1?: string
  backgroundColor?: string;
  isLoading?: boolean;
  className?: string;
  style?: React.CSSProperties;
  showDivider?: boolean;
  compact?: boolean;
  bold?: boolean;
  labelFontSize?: string; // New prop for label font size
  valueFontSize?: string; // New prop for value font size
  [key: string]: any;
}

const formatNumber = (value: number | string): string => {
  if (typeof value === "number") {
    return value.toLocaleString();
  }
  return value.toString();
};

const KeyValueCard: FC<KeyValueCardProps> = ({
  title,
  leftKey,
  leftValue,
  percentage,
  leftSide,
  colors,
  rightKey,
  rightValue,
  percentage1,
  isLoading,
  showDivider = true,
  compact = false,
  bold = true,
  labelFontSize = "text-xs", // Default font size
  valueFontSize = "text-xs", // Default font size

}) => {

  return (
    <div className={compact ? "p-1" : "p-2"}>
      <div className="flex flex-row w-full pl-1 mb-2">
        <div className="font-semibold text-sub-header sm:text-body md:text-small-font lg:text-small-font xl:text-small-font">{title}</div>
      </div>
      <div className="flex justify-between items-center w-full pl-3 text-xs">
        {/* Label */}
        <div className={`${bold ? 'font-semibold' : 'font-normal'} text-black dark:text-white flex-1`}>
          <div className="flex">
            <div className={labelFontSize}>{leftKey}</div>
            <div className={`ml-1 ${labelFontSize}`}>{leftSide}</div>
          </div>
        </div>
        {/* Value and Percentage at the end */}
        <div className={`${bold ? 'font-semibold' : 'font-normal'} flex items-center flex-shrink-0`} style={{ color: colors }}>
          <div className={valueFontSize}>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>{formatNumber(leftValue ?? "")}
                  <TooltipContent>{leftValue?.toLocaleString()}</TooltipContent>
                </TooltipTrigger>
              </Tooltip>
            </TooltipProvider>
            <span className="ml-1">{percentage}</span>
          </div>
        </div>
      </div>
    </div>



  );
};

export default KeyValueCard;
