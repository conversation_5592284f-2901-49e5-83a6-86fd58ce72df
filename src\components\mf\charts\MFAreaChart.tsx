"use client";

import { Area, <PERSON>Chart, CartesianGrid, <PERSON><PERSON><PERSON>s, <PERSON>Axis, Responsive<PERSON><PERSON><PERSON>, Legend, Tooltip } from "recharts";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
} from "@/components/ui/chart";
import HeaderRow from "@/components/mf/HeaderRow";
import { format } from "date-fns";
import { formatNumber } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface AreaChartConfig {
  [key: string]: {
    label: string;
    color: string;
    fillOpacity?: number;
    gradient?: {
      startOpacity?: number;
      endOpacity?: number;
    };
  };
}

interface MFAreaChartProps {
  data: any[];
  config: AreaChartConfig;
  title?: string;
  description?: string;
  onExpand?: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  handleExport?: () => void;
  isLoading?: boolean;
  xAxisKey?: string;
  xAxisFormatter?: (value: any) => string;
  yAxisFormatter?: (value: any) => string;
  tooltipFormatter?: (value: any, name: string) => [string | number, string];
  tooltipLabelFormatter?: (label: string) => string;
  height?: number;
  showLegend?: boolean;
  legendFormatter?: (value: string) => string;
  isStacked?: boolean;
  customMargin?: { top: number; right: number; left: number; bottom: number };
  titleFontSize?: string;
  // Frequency filter props
  showFrequencyFilter?: boolean;
  frequencyOptions?: string[];
  selectedFrequency?: string;
  onFrequencyChange?: (value: string) => void;
  frequencyPlaceholder?: string;
  // Card wrapper props
  showCardWrapper?: boolean;
  customHeader?: React.ReactNode;
  // Grid props
  showGrid?: boolean;
}

const CustomLegendContent = ({ labels, colors }: { labels: string[], colors: string[] }) => {
  return (
    <div className="flex space-x-4 pt-2 justify-center">
      {labels?.map((labelText: string, index) => (
        <div className="flex items-center space-x-2" key={index}>
          <span style={{ backgroundColor: colors[index] }} className="w-3 h-3 rounded-full"></span>
          <span style={{ fontSize: '12px' }}>{labelText}</span>
        </div>
      ))}
    </div>
  );
};

export function MFAreaChart({
  data = [],
  config = {},
  title,
  description,
  onExpand = () => {},
  onExport,
  handleExport,
  isLoading,
  xAxisKey = "date",
  xAxisFormatter = (value) => {
    try {
      const date = new Date(value);
      return format(date, 'dd/MMM');
    } catch {
      return value;
    }
  },
  yAxisFormatter = (value) => formatNumber(value),
  tooltipFormatter = (value, name) => [formatNumber(Number(value)), config[name]?.label || name],
  tooltipLabelFormatter = (label) => {
    try {
      const date = new Date(label);
      return format(date, 'dd MMM yyyy');
    } catch {
      return label;
    }
  },
  height = 300,
  showLegend = true,
  legendFormatter = (value) => config[value]?.label || value,
  isStacked = false,
  customMargin,
  titleFontSize = "text-base",
  // Frequency filter props
  showFrequencyFilter = false,
  frequencyOptions = [],
  selectedFrequency,
  onFrequencyChange,
  frequencyPlaceholder = "Frequency",
  // Card wrapper props
  showCardWrapper = true,
  customHeader,
  // Grid props
  showGrid = true,
}: MFAreaChartProps) {
  // Extract labels and colors from config with safety checks
  const labels = config ? Object.values(config).map(item => item.label) : [];
  const colors = config ? Object.values(config).map(item => item.color) : [];

  const chartContent = (
        <div className="w-full h-full" style={{ height: `${height}px` }}>
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
            </div>
          ) : !data || data.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <span className="text-sm dark:text-white">No Data Found !</span>
            </div>
          ) : (
            <ChartContainer config={Object.entries(config).reduce((acc, [key, cfg]) => ({
              ...acc,
              [key]: {
                label: cfg.label,
                color: cfg.color
              }
            }), {})} className="h-full w-full">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={data}
                  margin={customMargin || { top: 10, right: 30, left: 10, bottom: 0 }}
                >
                  <defs>
                    {Object.entries(config).map(([key, cfg]) => (
                      <linearGradient
                        key={key}
                        id={`color${key}`}
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor={cfg.color}
                          stopOpacity={cfg.gradient?.startOpacity || 0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor={cfg.color}
                          stopOpacity={cfg.gradient?.endOpacity || 0}
                        />
                      </linearGradient>
                    ))}
                  </defs>
              {showGrid && <CartesianGrid strokeDasharray="3 3" vertical={false} />}
                  <XAxis
                    dataKey={xAxisKey}
                    tickFormatter={xAxisFormatter}
                    tick={{ fontSize: 12 }}
                    height={30}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickFormatter={yAxisFormatter}
                    width={60}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={({ active, payload, label }) => {
                      if (!active || !payload?.length) return null;
                      
                      return (
                        <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                          <div className="font-medium bg-gray-100 text-black dark:text-white">
                            {tooltipLabelFormatter(label)}
                          </div>
                          <div className="grid gap-1.5">
                            {payload.map((item, index) => {
                              const itemConfig = config[item.dataKey as keyof typeof config];
                              const indicatorColor = item.payload.fill || item.color || itemConfig?.color || "#540094";
                              
                              return (
                                <div
                                  key={item.dataKey}
                                  className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                                >
                                  <div
                                    className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                    style={
                                      {
                                        "--color-bg": indicatorColor,
                                        "--color-border": indicatorColor,
                                      } as React.CSSProperties
                                    }
                                  />
                                  <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                    <span className="text-muted-foreground">
                                      {itemConfig?.label || item.name || "Count"}
                                    </span>
                                    <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                      {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                    </span>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    }}
                  />
                  {showLegend && (
                    <ChartLegend content={<CustomLegendContent labels={labels} colors={colors} />} />
                  )}
                  {Object.entries(config).map(([key, cfg]) => (
                    <Area
                      key={key}
                      type="linear"
                      dataKey={key}
                      name={cfg.label}
                      stroke={cfg.color}
                      fill={`url(#color${key})`}
                      fillOpacity={cfg.fillOpacity || 1}
                      stackId={isStacked ? "stack" : undefined}
                      strokeWidth={2}
                    />
                  ))}
                </AreaChart>
              </ResponsiveContainer>
            </ChartContainer>
          )}
        </div>
  );

  if (!showCardWrapper) {
    return chartContent;
  }

  return (
    <Card>
      <CardHeader className="p-2">
        {customHeader ? (
          customHeader
        ) : title ? (
          <HeaderRow
            title={title}
            onExpand={onExpand}
            onExport={onExport}
            handleExport={handleExport}
            isRadioButton={false}
            isSelect={showFrequencyFilter}
            selectoptions={frequencyOptions}
            selectedFrequency={selectedFrequency}
            handleFrequencyChange={onFrequencyChange}
            placeholder={frequencyPlaceholder}
            titleFontSize={titleFontSize}
          />
        ) : null}
        {description && <CardTitle className="text-sm text-muted-foreground">{description}</CardTitle>}
      </CardHeader>
      <CardContent className="p-0">
        {chartContent}
      </CardContent>
    </Card>
  );
}
