"use client";
import React, { useState } from "react";
import { useTheme } from "@/components/mf/theme-context";
import MFWebFraudAsideMenu from "@/components/mf/MFWebFraudAsideMenu"
import { MFTopBar } from "@/components/mf";
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient'


export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isDarkMode, toggleTheme } = useTheme();
  const [IsHover, setIsHover] = useState(false);
  const [Toggle, setToggle] = useState(false);

  const currentTheme = isDarkMode ? "dark" : "light";

  return (
    <div className="h-screen dark:bg-black ">
      <MFTopBar
        isExpanded={Toggle || IsHover}
        onToggle={() => setToggle(!Toggle)}
      />
      <div className="flex h-[calc(100vh_-_3.5rem)] w-full">
        <MFWebFraudAsideMenu
          isExpanded={Toggle || IsHover}
          onHover={setIsHover}
          theme={currentTheme}
        />
        <QueryClientProvider client={queryClient}>
        <div className="scrollbar flex-grow overflow-y-auto rounded-xl bg-gray-100 p-2 dark:bg-background w-full">
          {children}
        </div>
        </QueryClientProvider>
      </div>
    </div>
  );
}
