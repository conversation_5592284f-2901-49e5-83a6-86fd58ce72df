"use client"
import React, { useMemo } from 'react';
import RadialBar from '@/components/mf/Radial<PERSON><PERSON>';
import StackedBar<PERSON>hart from '@/components/mf/stackedBarChart';
//import { ChartConfig } from '@/components/ui/chart';
import { onExpand, downloadURI, parsePercentage, debounce } from '@/lib/utils';
import { useCallback, useRef, useState, useEffect } from 'react';
import domToImage from "dom-to-image";
import { Card } from '@/components/ui/card';
import DynamicBar<PERSON>hart from '@/components/mf/DynamicBarChart';
import HorizontalVerticalBarChart from '@/components/mf/HorizontalVerticalBarChart';
import { useApiCall } from "../../queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import { Filter } from "@/components/mf/Filters";
import { useDateRange } from "@/components/mf/DateRangeContext";
import Endpoint from '../../common/endpoint';
import { useLoading } from '@/components/mf/LoadingContext';

interface FilterItem {
  label: string;
  checked: boolean;
}

interface FilterState {
  filters: FilterItem[];
  is_select_all: boolean;
  selected_count: number;
  loading: boolean;
}

interface FilterPayload {
  [key: string]: FilterState;
}
//TrafficContributor
interface TrafficContributor {
  data: any;
  label: string;
  "Visit %": number;
  "Conversion %": number;
  Conversion_type: string;

}
//TopContributor
interface TopContributor {
  label: string;
  "Visit %": number;
  "Event %": number;
}
//Visit & eventConcentration
interface VisitEventData {
  label: string;
  // total:number;
  percentage: number | string;
  fill: string;
  [key: string]: string | number;
}

interface ChartConfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

// Add type for API response
interface FilterApiResponse {
  data: string[];
  isLoading: boolean;
}

interface UserIntentData {
  label: string;
  "Visit %": number;
  "Event %": number;
}

//Radial chart
const chartDataR = [{ month: "january", desktop: 1260, mobile: 570 }]

const chartConfigR = {
  "Visit %": {
    label: "Visit %",
    color: "#2090a7",
  },
  "Event %": {
    label: "Event %",
    color: "#14b7d6",
  },
} satisfies ChartConfig

//Traffic contributor

const chartConfigTC = {
  "Visit %": {
    label: "Visit %",
    color: "#2090a7",
  },
  "Conversion %": {
    label: "Conversion %",
    color: "#7e2632",
  },
} satisfies ChartConfig

//Top Contributor
const chartDataTopC = [
  { label: "Android~10", "% of Visits": 38, "% of Events": 46 },
  { label: "ISO~18.1", "% of Visits": 19, "% of Events": 16 },
  { label: "Android~14", "% of Visits": 15, "% of Events": 3 },
  { label: "ISO~18.0", "% of Visits": 6, "% of Events": 4 },
  { label: "Android~13", "% of Visits": 5, "% of Events": 1 },
]
const chartConfigTopC = {
  "Visit %": {
    label: "Visits %",
    color: "#2090a7",
  },
  "Event %": {
    label: "Events %",
    color: "#14b7d6",
  },
} satisfies ChartConfig
const Traffic_insights = () => {
  const cardRefs = useRef<HTMLElement[]>([]);
  const { startDate, endDate } = useDateRange();
  const [existingPublisherdata, setExistingPublisherdata] = useState<string[]>([]);
  const [existingSubPublisherdata, setExistingSubPublisherdata] = useState<string[]>([]);
  const [existingCampaigndata, setExistingCampaigndata] = useState<string[]>([]);
  const [existingChanneldata, setExistingChanneldata] = useState<string[]>([]);
  const [TrafficContribution, setTrafficContribution] = useState<string[]>([]);
  const [TopContribution, setTopContribution] = useState<string[]>([]);
  const [highIntent, setHighIntent] = useState<UserIntentData | null>(null);
  const [mediumIntent, setMediumIntent] = useState<UserIntentData | null>(null);
  const [lowIntent, setLowIntent] = useState<UserIntentData | null>(null);
  const [highDevice, setHighDevice] = useState<UserIntentData | null>(null);
  const [mediumDevice, setMediumDevice] = useState<UserIntentData | null>(null);
  const [lowDevice, setLowDevice] = useState<UserIntentData | null>(null);
  const [VisitC, setVisitC] = useState<string[]>([]);
  const [EventC, setEventC] = useState<string[]>([]);
  const [loadedFilter, setLoadedFilter] = useState<any>({});
  const { selectedPackage } = usePackage();
  const loading = useLoading();
  if (!loading) {
    throw new Error('Loading context not found');
  }
  const { isLoading, startLoading, stopLoading } = loading;
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [query, setQuery] = useState({
    publishers: ["all"],
    sub_publishers: ["all"],
    campaigns: ["all"],
    channels: ["all"],
  });
  const isInitialLoad = useRef(true);

  const handleExpand = (index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  };

  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];
      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs]
  );
  // Publishers Filter API
  const publishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingPublisherdata(data);
      if (data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    },
  });
  // Sub Publishers Filter API
  const subPublishersFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.SUB_PUBLISHERS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingSubPublisherdata(data);
      if (data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    },
  });
  // Campaigns Filter API
  const campaignsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CAMPAIGNS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingCampaigndata(data);
      if (data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    },
  });
  // Channels Filter API
  const channelsFilterApi = useApiCall<FilterApiResponse>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.CHANNELS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    },
    onSuccess: (data) => {
      setExistingChanneldata(data);
      if (data.length > 0) {
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    },
  });
  //user intent
  const UserIntentApi = useApiCall<UserIntentData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.USER_INTENT,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        const highIntentData = response.find((item: any) => item.intent_status === "High Intent");
        const mediumIntentData = response.find((item: any) => item.intent_status === "Medium Intent");
        const lowIntentData = response.find((item: any) => item.intent_status === "Low Intent");
        // Prepare data for High, Medium, and Low Intent
        const highIntentDetails = highIntentData ? {
          label: highIntentData.intent_status,
          "Visit %": parsePercentage(highIntentData.visit_percentage),
          "Event %": parsePercentage(highIntentData.event_percentage),
        } : null;
        // console.log("high intend:",highIntentDetails);
        if (JSON.stringify(highIntentDetails) !== JSON.stringify(highIntent)) {
          setHighIntent(highIntentDetails)
        }
        const mediumIntentDetails = mediumIntentData ? {
          label: mediumIntentData.intent_status,
          "Visit %": parsePercentage(mediumIntentData.visit_percentage),
          "Event %": parsePercentage(mediumIntentData.event_percentage),
        } : null;
        if (JSON.stringify(mediumIntentDetails) !== JSON.stringify(mediumIntent)) {
          setMediumIntent(mediumIntentDetails)
        }
        const lowIntentDetails = lowIntentData ? {
          label: lowIntentData.intent_status,
          "Visit %": parsePercentage(lowIntentData.visit_percentage),
          "Event %": parsePercentage(lowIntentData.event_percentage),
        } : null;
        if (JSON.stringify(lowIntentDetails) !== JSON.stringify(lowIntent)) {
          setLowIntent(lowIntentDetails);
        }
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    },
  })

  //Traffic Contributor
  const TrafficCApi = useApiCall<TrafficContributor>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TRAFFIC_CONTRIBUTION,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        const updatedtop = response.map((items: any) => {
          return {
            label: items.device_type,
            "Visit %": parsePercentage(items.visit_percentage), // Convert visit percentage to number
            "Conversion %": parsePercentage(items.conversion_percentage), // Convert conversion percentage to number
            Conversion_type: items.conversion_type, // Keep the conversion type
          }
        });
        if (JSON.stringify(updatedtop) !== JSON.stringify(TrafficContribution)) {
          setTrafficContribution(updatedtop)
        }
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  })
  //Top contributing os version
  const TopCApi = useApiCall<TopContributor>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.TOP_CONTRIBUTING_OS_VERSIONS,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        const updatedtop = response.map((items: any) => {
          return {
            label: items.ua_os,
            "Visit %": parsePercentage(items.visit_percentage),
            "Event %": parsePercentage(items.event_percentage),
          }
        });
        if (JSON.stringify(updatedtop) !== JSON.stringify(TopContribution)) {
          setTopContribution(updatedtop)
        }
      }
      stopLoading();
    },
    onError: (error) => {
      stopLoading();
    },
  })

  //Device Concentration Api
  const DeviceConcentrationApi = useApiCall<UserIntentData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.DEVICE_CONCENTRATION,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        const highData = response.find((item: any) => item.device_status === "High");
        const mediumData = response.find((item: any) => item.device_status === "Medium");
        const lowData = response.find((item: any) => item.device_status === "Low");
        // Prepare data for High, Medium, and Low Intent
        const highDetails = highData ? {
          label: highData.device_status,
          "Visit %": parsePercentage(highData.visit_percentage),
          "Event %": parsePercentage(highData.event_percentage),
        } : null;
        // console.log("high intend:",highIntentDetails);
        if (
          highDetails &&
          (highDetails["Visit %"] !== highDevice?.["Visit %"] ||
            highDetails["Event %"] !== highDevice?.["Event %"])
        ) {
          setHighDevice(highDetails);
        }
        const mediumDetails = mediumData ? {
          label: mediumData.device_status,
          "Visit %": parsePercentage(mediumData.visit_percentage),
          "Event %": parsePercentage(mediumData.event_percentage),
        } : null;
        if (
          mediumDetails &&
          (mediumDetails["Visit %"] !== mediumDevice?.["Visit %"] ||
            mediumDetails["Event %"] !== mediumDevice?.["Event %"])
        ) {
          setMediumDevice(mediumDetails);
        }
        const lowDetails = lowData ? {
          label: lowData.device_status,
          "Visit %": parsePercentage(lowData.visit_percentage),
          "Event %": parsePercentage(lowData.event_percentage),
        } : null;
        if (
          lowDetails &&
          (lowDetails["Visit %"] !== lowDevice?.["Visit %"] ||
            lowDetails["Event %"] !== lowDevice?.["Event %"])
        ) {
          setLowDevice(lowDetails);
        }
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    },
  })

  //Visit Concentration api
  const VisitCAPI = useApiCall<VisitEventData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.STATE_VISIT,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        // If there are more than 100 labels, limit the result to 100
        const limitedData = response.length > 10 ? response.slice(0, 10) : response;

        const updatedtop = limitedData.map((topItem: any) => {
          const state = topItem.state;
          const stateColor = ConfigVistC[state]?.color || "#000"; // Fallback to black if no match
          return {
            label: state,
            percentage: parsePercentage(topItem.percentage),
            fill: stateColor,
          };
        });
        if (JSON.stringify(updatedtop) !== JSON.stringify(VisitC)) {
          setVisitC(updatedtop);
        }
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    }
  })

  const generateColorV = (index: number) => {
    const hue = (index * 125) % 360;
    return `hsl(${hue}, 40%, 50%)`;
  };

  // Assuming `data` is your array of items
  const ConfigVistC: ChartConfig = VisitC.reduce((acc, item, index) => {
    const Percentage = parsePercentage(item.percentage); // This can be string | number

    // Check if fraudPercentage is a number before comparing
    const fillColor = typeof Percentage === 'number' && Percentage > 10 ? "red" : "green";

    acc[item.label] = {
      label: item.label,
      color: generateColorV(index),
      fill: fillColor,
    };
    return acc;
  }, {} as ChartConfig);

  //Event Concentration api
  const EventCAPI = useApiCall<VisitEventData>({
    url: process.env.NEXT_PUBLIC_WEB_PERF + Endpoint.STATE_EVENT,
    method: "POST",
    params: {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
      publishers: query.publishers,
      sub_publisher: query.sub_publishers,
      campaign: query.campaigns,
      channel: query.channels,
    },
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        const limitedData = response.length > 10 ? response.slice(0, 10) : response;
        const updatedtop = limitedData.map((topItem: any) => {
          const state = topItem.ip_country;
          const stateColor = ConfigEventC[state]?.color || "#000"; // Fallback to black if no match
          return {
            label: state,
            percentage: parsePercentage(topItem.percentage),
            fill: stateColor, // Assigning the correct color
          };
        });
        // console.log("updated values event ",updatedtop);
        if (JSON.stringify(updatedtop) !== JSON.stringify(EventC)) {
          setEventC(updatedtop);
        }
        stopLoading();
      }
    },
    onError: (error) => {
      stopLoading();
    }
  })

  const generateColorE = (index: number) => {
    const hue = (index * 107) % 360;
    return `hsl(${hue}, 90%, 60%)`;
  };

  // Assuming `data` is your array of items
  const ConfigEventC: ChartConfig = EventC.reduce((acc, item, index) => {
    const Percentage = parsePercentage(item.percentage); // This can be string | number

    const fillColor = typeof Percentage === 'number' && Percentage > 10 ? "red" : "green";

    acc[item.label] = {
      label: item.label,
      color: generateColorE(index),
      fill: fillColor,
    };
    return acc;
  }, {} as ChartConfig);

  const filter = React.useMemo(
    () => ({
      Publishers: {
        filters:
          existingPublisherdata?.map((publisher: string) => ({
            label: publisher,
            // Change: Only check if it exists in the current selection
            checked: query.publishers?.includes("all") ||
              query.publishers?.includes(publisher) ||
              !query.publishers, // Default true if no selection exists
          })) || [],
        // Change: Determine if all are selected
        is_select_all: !query.publishers ||
          query.publishers.includes("all") ||
          query.publishers?.length === existingPublisherdata?.length,
        // Change: Actual selected count
        selected_count: query.publishers?.includes("all")
          ? existingPublisherdata?.length ?? 0
          : query.publishers?.length ?? existingPublisherdata?.length ?? 0,
        loading: false,
      },
      "Sub Publishers": {
        filters:
          existingSubPublisherdata?.map((subPublisher: string) => ({
            label: subPublisher,
            checked: query.sub_publishers?.includes("all") ||
              query.sub_publishers?.includes(subPublisher) ||
              !query.sub_publishers,
          })) || [],
        is_select_all: !query.sub_publishers ||
          query.sub_publishers.includes("all") ||
          query.sub_publishers?.length === existingSubPublisherdata?.length,
        selected_count: query.sub_publishers?.includes("all")
          ? existingSubPublisherdata?.length ?? 0
          : query.sub_publishers?.length ?? existingSubPublisherdata?.length ?? 0,
        loading: false,
      },
      Campaigns: {
        filters:
          existingCampaigndata?.map((campaign: string) => ({
            label: campaign,
            checked: query.campaigns?.includes("all") ||
              query.campaigns?.includes(campaign) ||
              !query.campaigns,
          })) || [],
        is_select_all: !query.campaigns ||
          query.campaigns.includes("all") ||
          query.campaigns?.length === existingCampaigndata?.length,
        selected_count: query.campaigns?.includes("all")
          ? existingCampaigndata?.length ?? 0
          : query.campaigns?.length ?? existingCampaigndata?.length ?? 0,
        loading: false,
      },
      Channels: {
        filters:
          existingChanneldata?.map((channel: string) => ({
            label: channel,
            checked: query.channels?.includes("all") ||
              query.channels?.includes(channel) ||
              !query.channels,
          })) || [],
        is_select_all: !query.channels ||
          query.channels.includes("all") ||
          query.channels?.length === existingChanneldata?.length,
        selected_count: query.channels?.includes("all")
          ? existingChanneldata?.length ?? 0
          : query.channels?.length ?? existingChanneldata?.length ?? 0,
        loading: false,
      },
    }),
    [
      existingPublisherdata,
      existingSubPublisherdata,
      existingCampaigndata,
      existingChanneldata,
      query.publishers,
      query.sub_publishers,
      query.campaigns,
      query.channels,
    ]
  );
  const deepEqual = (arr1: any[], arr2: any[]) => {
    if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
    if (arr1.length !== arr2.length) return false;
    return arr1.every((item, index) =>
      JSON.stringify(item) === JSON.stringify(arr2[index])
    );
  };
  //filter 
  const fetchPublisher = useCallback(() => { if (publishersFilterApi.type === "mutation") { startLoading(); publishersFilterApi.result.mutate() } }, []);
  const fetchSubPublisher = useCallback(() => { if (subPublishersFilterApi.type === "mutation") { startLoading(); subPublishersFilterApi.result.mutate() } }, []);
  const fetchCampaign = useCallback(() => { if (campaignsFilterApi.type === "mutation") { startLoading(); campaignsFilterApi.result.mutate() } }, []);
  const fetchChannel = useCallback(() => { if (channelsFilterApi.type === "mutation") { startLoading(); channelsFilterApi.result.mutate() } }, []);
  // Add this new function to fetch all APIs
  const fetchTCData = useCallback(() => { if (TrafficCApi.type === "mutation") { startLoading(); TrafficCApi.result.mutate() } }, []);
  const fetchTopData = useCallback(() => { if (TopCApi.type === "mutation") { startLoading(); TopCApi.result.mutate() } }, []);
  const fetchVData = useCallback(() => { if (VisitCAPI.type === "mutation") { startLoading(); VisitCAPI.result.mutate() } }, []);
  const fetchECData = useCallback(() => { if (EventCAPI.type === "mutation") { startLoading(); EventCAPI.result.mutate() } }, []);
  const fetchUIData = useCallback(() => { if (UserIntentApi.type === "mutation") { startLoading(); UserIntentApi.result.mutate() } }, []);
  const fetchDCData = useCallback(() => { if (DeviceConcentrationApi.type === "mutation") { startLoading(); DeviceConcentrationApi.result.mutate() } }, []);


  // Simplified triggerAPIs without the skipNextAPICall logic
  const triggerAPIs = useCallback(
    debounce(async () => {
      startLoading();
      console.log("🚀 Triggering APIs...");

      try {
        await Promise.all([
          fetchPublisher(),
          fetchSubPublisher(),
          fetchCampaign(),
          fetchChannel(),
          fetchUIData(),
          fetchECData(),
          fetchTopData(),
          fetchVData(),
          fetchTCData(),
          fetchDCData(),
        ]);
        console.log("🟢 All APIs finished loading");
      } catch (error) {
        console.error("❌ Error while triggering APIs:", error);
      } finally {
        stopLoading();
      }
    }, 300), // Increased debounce time to 300ms
    [
      fetchPublisher,
      fetchSubPublisher,
      fetchCampaign,
      fetchChannel,
      fetchUIData,
      fetchECData,
      fetchTopData,
      fetchVData,
      fetchTCData,
      fetchDCData,
    ]
  );

  const handleFilterChange = useCallback(
    async (newState: Record<string, any>) => {
      console.log("🔄 New State after filter change:", newState);

      const payload = {
        publishers: newState.Publishers?.is_select_all
          ? ["all"]
          : newState.Publishers?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
        sub_publishers: newState["Sub Publishers"]?.is_select_all
          ? ["all"]
          : newState["Sub Publishers"]?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
        campaigns: newState.Campaigns?.is_select_all
          ? ["all"]
          : newState.Campaigns?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
        channels: newState.Channels?.is_select_all
          ? ["all"]
          : newState.Channels?.filters
            .filter((f: any) => f.checked)
            .map((f: any) => f.label),
      };

      setQuery(payload);

      const filtersChanged =
        !deepEqual(
          newState.Publishers?.filters || [],
          loadedFilter.Publishers?.filters || []
        ) ||
        !deepEqual(
          newState["Sub Publishers"]?.filters || [],
          loadedFilter["Sub Publishers"]?.filters || []
        ) ||
        !deepEqual(
          newState.Campaigns?.filters || [],
          loadedFilter.Campaigns?.filters || []
        ) ||
        !deepEqual(
          newState.Channels?.filters || [],
          loadedFilter.Channels?.filters || []
        );

      if (filtersChanged) {
        console.log("✅ Filters changed, triggering API...");
        setLoadedFilter(newState);
        triggerAPIs(); // Trigger API immediately
      } else {
        console.log("🔕 No change in filters, skipping API.");
      }
    },
    [loadedFilter, triggerAPIs]
  );

  useEffect(() => {
    if (selectedPackage) {
      triggerAPIs();
    }
  }, [selectedPackage, startDate, endDate, triggerAPIs]);

  useEffect(() => {
    if (selectedPackage && !isInitialLoad.current) {
      triggerAPIs();
    }
  }, [loadedFilter, selectedPackage, triggerAPIs]);

  const yAxisConfigV = {
    dataKey: "label",
    //title: "Visit Publisher Name",
  };
  return (
    <div className='grid gap-2'>
      <div className="container sticky top-0 z-50 flex max-w-full items-center justify-start gap-2 rounded-md bg-background px-5 py-1 sm:flex-col-2 md:flex-col-2">
        <Filter filter={filter} onChange={handleFilterChange} />
      </div>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>User Intent</div>
        <div className='grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 md:grid-cols-2 w-full gap-2 '>
          <Card className='min-h-[200px]'>
            <RadialBar
              chartData={highIntent ? [{ ...highIntent }] : []} // Pass the highIntent object directly
              chartConfig={chartConfigR}
              Device={highIntent?.label || "High Intent User"}
              Vname="Visits"
              Vvalue={highIntent?.["Visit %"] || 0}
              Oname="Events"
              Ovalue={highIntent?.["Event %"] || 0}
              textcolors="#2a9d90"
              value1='Visit %'
              value2='Event %'
              isLoading={isLoading}
            />
          </Card>
          <Card className='min-h-[200px]'>
            <RadialBar
              chartData={mediumIntent ? [{ ...mediumIntent }] : []} // Pass the mediumIntent object directly
              chartConfig={chartConfigR}
              Device={mediumIntent?.label || "Medium Intent User"}
              Vname="Visits"
              Vvalue={mediumIntent?.["Visit %"] || 0}
              Oname="Events"
              Ovalue={mediumIntent?.["Event %"] || 0}
              textcolors="#e76e50"
              value1='Visit %'
              value2='Event %'
              isLoading={isLoading}
            />
          </Card>
          <Card className='min-h-[200px]'>
            <RadialBar
              chartData={lowIntent ? [{ ...lowIntent }] : []} // Pass the lowIntent object directly
              chartConfig={chartConfigR}
              Device={lowIntent?.label || "Low Intent User"}
              Vname="Visits"
              Vvalue={lowIntent?.["Visit %"] || 0}
              Oname="Events"
              Ovalue={lowIntent?.["Event %"] || 0}
              textcolors="#ff0000"
              value1='Visit %'
              value2='Event %'
              isLoading={isLoading}
            />
          </Card>

        </div>
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl-grid-cols-2 gap-2' >
        <Card ref={(el) => (cardRefs.current[0] = el!)} className='p-2 h-[350px]'>
          <StackedBarChart
            chartData={TrafficContribution}
            chartConfig={chartConfigTC}
            onExport={() => onExport("png", "Traffic Contribution", 0)}
            onExpand={() => handleExpand(0)}
            title='Traffic Contribution % Basis Device Type'
            yAxis={yAxisConfigV}
            isLoading={isLoading}
          />
        </Card>
        <Card ref={(el) => (cardRefs.current[1] = el!)} className='p-2 h-[350px]'>
          <DynamicBarChart
            data={TopContribution}
            config={chartConfigTopC}
            title="Top Contributing OS Version"
            onExport={() => onExport("png", "Top Contributing", 1)}
            onExpand={() => handleExpand(1)}
            isHorizontal={false}
            isRadioButton={false}
            isSelect={false}
            position="top"
            isLoading={isLoading}
          />

        </Card>

      </div>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Device Concentration %</div>
        <div className='grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 md:grid-cols-2 w-full gap-2 '>
          <Card className='min-h-[200px]'>
            <RadialBar
              chartData={highDevice ? [{ ...highDevice }] : []}
              chartConfig={chartConfigR}
              Device={highDevice?.label || "High"}
              Vname="Visits"
              Vvalue={highDevice?.["Visit %"] || 0}
              Oname="Events"
              Ovalue={highDevice?.["Event %"] || 0}
              //backgroundcolors="bg-yellow-300"
              textcolors="#2a9d90"
              value1='Visit %'
              value2='Event %'
              isLoading={isLoading}
            />
          </Card>
          <Card className='min-h-[200px]'>
            <RadialBar
              chartData={mediumDevice ? [{ ...mediumDevice }] : []}
              chartConfig={chartConfigR}
              Device={mediumDevice?.label || "Medium"}
              Vname="Visits"
              Vvalue={mediumDevice?.["Visit %"] || 0}
              Oname="Events"
              Ovalue={mediumDevice?.["Event %"] || 0}
              textcolors="#e76e50"
              value1='Visit %'
              value2='Event %'
              isLoading={isLoading}
            />
          </Card>
          <Card className='min-h-[200px]'>
            <RadialBar
              chartData={lowDevice ? [{ ...lowDevice }] : []}
              chartConfig={chartConfigR}
              Device={lowDevice?.label || "Low"}
              Vname="Visits"
              Vvalue={lowDevice?.["Visit %"] || 0}
              Oname="Events"
              Ovalue={lowDevice?.["Event %"] || 0}
              textcolors='#ff0000'
              value1='Visit %'
              value2='Event %'
              isLoading={isLoading}
            />
          </Card>

        </div>
      </div>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Region Wise Concentration %</div>
        <div className='grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl-grid-cols-2 gap-2'>
          <Card ref={(el) => (cardRefs.current[2] = el!)} className='h-[350px] p-2'>
            <HorizontalVerticalBarChart
              chartData={VisitC}
              chartConfig={ConfigVistC}
              title="Visit Concentration %"
              onExport={() => onExport("png", "Visit Concentration", 2)}
              onExpand={() => handleExpand(2)}
              isHorizontal={true}
              formatterType="percentage"
              isRadioButton={false}
              isLoading={isLoading}
              isSelect={false}
              dataKey="percentage"
              namekeys="label"
              position='right'
              barsize={10}
               setheight='280px'
            />

          </Card>
          <Card ref={(el) => (cardRefs.current[3] = el!)} className='h-[350px] p-2'>
            <HorizontalVerticalBarChart
              chartData={EventC}
              chartConfig={ConfigEventC}
              title="Event Concentration %"
              onExport={() => onExport("png", "Visit Concentration", 3)}
              onExpand={() => handleExpand(3)}
              isHorizontal={true}
              formatterType="percentage"
              isRadioButton={false}
              isLoading={isLoading}
              isSelect={false}
              dataKey="percentage"
              namekeys="label"
              position='right'
              barsize={10}
              setheight='280px'

            />
          </Card>
        </div>
      </div>
    </div>
  )
}
export default Traffic_insights;

