"use client";
 
import * as React from "react";
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON>onsive<PERSON><PERSON><PERSON>, LabelList, Cell } from "recharts";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { formatNumber } from "@/lib/utils";
import HeaderRow from "./HeaderRow";
import { Loader2 } from "lucide-react";
import { FolderOpen, Download, RotateCw } from "lucide-react";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useFullscreen } from "@/hooks/fullscreen";
 
 
interface ChartDataItem {
  label?: string;
  visit?: number;
  value?: number;
  percentage?: string | number;
  fill?: string;
  [key: string]: string | number | undefined;
}
 
interface DonutChartProps {
  chartData?: ChartDataItem[];
  dataKey: string;  // Dynamic key for chart data
  nameKey?: string;  // Dynamic key for name
  chartConfig?: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
  marginTop?: string;
  isdonut?: boolean;
  isLoading?: boolean;
  isView: boolean;
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  selectedType?: string;
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  selectoptions?: string[];
  placeholder?: string;
  DonutTitle?: string;
  isPercentage?: boolean;
  isLabelist?: boolean;
  direction?: string;
  totalV?: number;
  position?: 'items-start' | 'items-center' | 'items-end' | 'bottom';
  innerRadius?: number;
  outerRadius?: number;
  onSegmentClick?: (segment: { label: string; value: number }) => void;
  selectedSegment?: string | null;
  onInvestigateClick?: () => void;
  onCaseReportClick?
  onDownloadReportClick?: () => void;
  hideScrollbar?: boolean;  // New prop to control scrollbar visibility
  legendsTitle?: string;  // New prop for legends title
  showHeaderRow?: boolean; // New prop to control HeaderRow rendering
  legendFontSize?: number; // New prop for legend font size
  centerLabel?: string; // Label below the value
  centerValue?: string; // Main value in the center
  legendPosition?: 'right' | 'bottom' | 'left' | 'top';
  showRefresh?: boolean; // New prop to show refresh button
  onRefresh?: () => void; // New prop for refresh callback
  legendsTitleFontSize?: string; // New prop for legends title font size
  titleFontSize?: string; // New prop for title font size
  formatterType?: "number" | "percentage"; // New prop for tooltip formatting
  isExpanded?: boolean; // New prop to control expand state
  disableLegendClick?: boolean; // New prop to disable legend click functionality
  enableHorizontalScroll?: boolean; // New prop to enable horizontal scrolling for bottom legends
  scrollbarGap?: number; // New prop to control gap between card content and scrollbar
}
 
const DonutChart: React.FC<DonutChartProps> = ({
  chartData=[],
  chartConfig,
  handleTypeChange,
  visitEventOptions,
  selectedType,
  selectoptions = [],
  handleExport,
  onExport,
  onExpand,
  title,
  legendPosition,
  isSelect = false,
  isRadioButton = false,
  placeholder = "",
  dataKey,
  nameKey,
  totalV,
  DonutTitle,
  isView = true,
  isPercentage = true,
  isLabelist = false,
  direction = "flex-col",
  isLoading = false,
  isdonut = false,
  marginTop = "",
  position = "items-start",
  innerRadius,
  outerRadius,
  onSegmentClick,
  selectedSegment,
  onInvestigateClick,
  onCaseReportClick,
  onDownloadReportClick,
  hideScrollbar = false,
  legendsTitle,
  showHeaderRow = true,
  legendFontSize,
  centerLabel,
  centerValue,
  showRefresh = false,
  onRefresh,
  legendsTitleFontSize,
  titleFontSize = "text-base",
  formatterType = "number",
  isExpanded = false,
  disableLegendClick = false,
  enableHorizontalScroll = false,
  scrollbarGap = 0,
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isFullscreen = useFullscreen();
  const getExpandedRadii = () => {
    if (window.innerWidth < 640) {
      return { inner: 35, outer: 70 }; // Mobile
    } else if (window.innerWidth >= 640 && window.innerWidth <= 1024) {
      return { inner: 50, outer: 90 }; // Tablet
    } else if (window.innerWidth > 1024 && window.innerWidth < 1280) {
      return { inner: 60, outer: 110 }; // Desktop
    } else {
      return { inner: 80, outer: 140 }; // Large screens
    }
  };

  // Use responsive radii when no specific radius is provided, or use provided values
  const currentRadii = useMemo(() => {
    if (isFullscreen) {
      return getExpandedRadii();
    }

    // If specific radii are provided, use them
    if (typeof innerRadius !== 'undefined' && typeof outerRadius !== 'undefined') {
      return { inner: innerRadius, outer: outerRadius };
    }

    // Otherwise use responsive state values
    return { inner: innerRadiusState, outer: outerRadiusState };
  }, [isFullscreen, innerRadius, outerRadius, innerRadiusState, outerRadiusState]);

  const handleRefreshClick = () => {
    if (onRefresh) {
      setIsRefreshing(true);
      onRefresh();
      setTimeout(() => setIsRefreshing(false), 1000);
    }
  };
 
  const innerRadiusProp = typeof innerRadius !== 'undefined' ? innerRadius : undefined;
  const outerRadiusProp = typeof outerRadius !== 'undefined' ? outerRadius : undefined;
  
 
  const totalVisitors = React.useMemo(() => {
    return chartData?.reduce((acc, curr) => {
      const visit = Number(curr.visit);
      return !isNaN(visit) ? acc + visit : acc;
    }, 0);
  }, [chartData]);
 
  const formatVisitors = React.useMemo(() => (value: number) => {
    if (value >= 1000) {
      return `${(value / 1000).toFixed(0)}k`;
    }
    return value.toString();
  }, []);
 
  const [innerRadiusState, setInnerRadiusState] = React.useState(60);
  const [outerRadiusState, setOuterRadiusState] = React.useState(70);
  const [chartHeight, setChartHeight] = React.useState(200);
  const [centerValueFontSize, setCenterValueFontSize] = React.useState(22);
  const [centerLabelFontSize, setCenterLabelFontSize] = React.useState(13);
  const [containerDimensions, setContainerDimensions] = React.useState({ width: 0, height: 0 });

  // Consolidated resize effect for better performance and consistency
  React.useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      // Calculate responsive dimensions based on screen size
      let responsiveInner, responsiveOuter, responsiveHeight, responsiveCenterValue, responsiveCenterLabel;

      if (width < 640) {
        // Mobile
        responsiveInner = 25;
        responsiveOuter = 50;
        responsiveHeight = Math.min(height * 0.25, 180); // 25% of screen height, max 180px
        responsiveCenterValue = 14;
        responsiveCenterLabel = 10;
      } else if (width >= 640 && width <= 1024) {
        // Tablet
        responsiveInner = 35;
        responsiveOuter = 65;
        responsiveHeight = Math.min(height * 0.3, 220); // 30% of screen height, max 220px
        responsiveCenterValue = 16;
        responsiveCenterLabel = 11;
      } else if (width > 1024 && width < 1450) {
        // Desktop
        responsiveInner = 45;
        responsiveOuter = 80;
        responsiveHeight = Math.min(height * 0.35, 250); // 35% of screen height, max 250px
        responsiveCenterValue = 18;
        responsiveCenterLabel = 12;
      } else if (width >= 1450 && width < 1920) {
        // Large screens
        responsiveInner = 55;
        responsiveOuter = 95;
        responsiveHeight = Math.min(height * 0.4, 280); // 40% of screen height, max 280px
        responsiveCenterValue = 20;
        responsiveCenterLabel = 13;
      } else {
        // XL screens
        responsiveInner = 65;
        responsiveOuter = 110;
        responsiveHeight = Math.min(height * 0.45, 320); // 45% of screen height, max 320px
        responsiveCenterValue = 22;
        responsiveCenterLabel = 14;
      }

      setInnerRadiusState(responsiveInner);
      setOuterRadiusState(responsiveOuter);
      setChartHeight(responsiveHeight);
      setCenterValueFontSize(responsiveCenterValue);
      setCenterLabelFontSize(responsiveCenterLabel);
      setContainerDimensions({ width, height });
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial call

    return () => window.removeEventListener("resize", handleResize);
  }, []);
 
  // if (!chartData || chartData.length === 0) {
  //   return <div>No data available</div>;
  // }
 
  return (
    <Card className="flex flex-wrap justify-between border-none w-full h-full">
 
<div className="w-full flex items-center justify-between ">
  {/* Left: Title */}
  <div className={`${titleFontSize} items-start font-semibold`}>
    {title}
  </div>
 
  {/* Right: Action buttons + HeaderRow */}
  <div className="flex items-center gap-4">
    {onInvestigateClick && (
      <button
        onClick={onInvestigateClick}
        className="flex items-center gap-1 text-primary hover:underline text-xs font-medium"
      >
        {/* <FolderOpen className="h-5 w-5" /> */}
        {/* Investigate More */}
      </button>

    )}
    {onCaseReportClick && (
      <button
        onClick={onCaseReportClick}
        className="flex items-center gap-1 text-primary hover:underline text-xs font-medium"
      >
        <FolderOpen className="h-5 w-5" />
        Case Report
      </button>

    )}
    
        {onDownloadReportClick && (
      <button
        onClick={onDownloadReportClick}
        className="flex items-center gap-1 text-primary hover:underline text-xs font-medium"
      >
        <Download className="h-5 w-5" />
        Download Report
      </button>
    )}
    {/* Only show refresh button when NOT in expand mode */}
    {showRefresh && !isExpanded && (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              onClick={handleRefreshClick}
              className="p-0 hover:bg-transparent"
            >
              <RotateCw 
                className={`h-5 w-5 text-primary dark:text-white hover:text-700 transition-all cursor-pointer ${isRefreshing ? 'animate-spin' : ''}`}
              />
            </button>
          </TooltipTrigger>
          <TooltipContent side="bottom" align="center">
            Refresh
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )}

    {/* Three-dot menu (HeaderRow) */}
    {showHeaderRow && (
    <HeaderRow
      visitEventOptions={visitEventOptions}
      handleTypeChange={handleTypeChange}
      selectedType={selectedType}
      onExpand={onExpand}
      handleExport={handleExport}
      isRadioButton={isRadioButton}
      isSelect={isSelect}
      onExport={onExport}
      selectoptions={selectoptions}
      placeholder={placeholder}
    />
    )}
  </div>
</div>
 
 
 
      {isdonut && (
        <CardHeader className=" w-full  p-1 t-0">
 
          <CardTitle className="text-body  p-0 font-semibold">{DonutTitle}</CardTitle>
        </CardHeader>
      )}
      <CardContent className={`h-full w-full relative p-0 ${isFullscreen ? 'h-[100vh]' : ''}`}>
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
          </div>
        ) : chartData?.length > 0 ? (
          <div className={`flex flex-col h-full ${legendPosition === 'bottom' ? 'justify-between' : ''}`}>
            <div className={`${legendPosition === 'bottom' ? 'h-[65%]' : 'h-full'} grid ${legendPosition === 'bottom' ? 'grid-cols-1' : 'grid-cols-2'} gap-2`}>
              <div className="h-full w-full flex justify-center items-center min-h-0 donut-chart-container">
                {chartConfig ? (
                  <ChartContainer
                    config={chartConfig}
                    className="mt-0 flex justify-center items-center w-full h-full min-h-0 donut-chart-container"
                  >
                    <ResponsiveContainer
                      width="100%"
                      height={isFullscreen ? Math.min(containerDimensions.height * 0.6, 500) : chartHeight}
                      className="flex items-center"
                      minHeight={Math.max(120, chartHeight * 0.8)}
                    >
                      <PieChart margin={{
                        top: Math.max(10, chartHeight * 0.05),
                        right: Math.max(10, chartHeight * 0.05),
                        bottom: Math.max(10, chartHeight * 0.05),
                        left: Math.max(10, chartHeight * 0.05)
                      }}>
                        <ChartTooltip
                          cursor={false}
                          content={<ChartTooltipContent hideLabel formatterType={formatterType} />}
                        />
                        <Pie
                          data={chartData}
                          dataKey={dataKey}
                          nameKey={nameKey}
                          innerRadius={currentRadii.inner || innerRadiusState}
                          outerRadius={currentRadii.outer || outerRadiusState}
                          strokeWidth={2}
                          fill="#8884d8"
                          cy="50%"
                          cx="50%"
                          onClick={(data) => {
                            if (onSegmentClick && data.label) {
                              onSegmentClick({ label: data.label, value: data[dataKey] as number });
                            }
                          }}
                        >
                          {chartData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.fill || chartConfig[entry.label || '']?.color}
                              style={{
                                cursor: onSegmentClick ? 'pointer' : 'default',
                                opacity: selectedSegment === entry.label ? 1 : 0.7,
                                transition: 'opacity 0.2s ease-in-out'
                              }}
                            />
                          ))}
                          {isLabelist && (
                            <LabelList
                              dataKey={dataKey}
                              position="inside"
                              style={{
                                fontSize: '8px',
                                fill: '#fff',
                                fontWeight: 'bold'
                              }}
                              stroke="none"
                              formatter={(value: number) => `${value}%`}
                            />
                          )}
                          <Label
                            content={({ viewBox }) => {
                              if (centerValue !== undefined && viewBox && "cx" in viewBox && "cy" in viewBox && typeof viewBox.cy === 'number') {
                                if (centerValue === "") {
                                  return null; // Return null when centerValue is empty string
                                }
                                return (
                                  <g>
                                    <text
                                      x={viewBox.cx}
                                      y={viewBox.cy - 8}
                                      textAnchor="middle"
                                      dominantBaseline="middle"
                                      className="fill-foreground font-bold"
                                      style={{ fontSize: `${centerValueFontSize}px` }}
                                    >
                                      {centerValue}
                                    </text>
                                    {centerLabel && (
                                      <text
                                        x={viewBox.cx}
                                        y={viewBox.cy + 14}
                                        textAnchor="middle"
                                        dominantBaseline="middle"
                                        className="fill-foreground"
                                        style={{ fontSize: `${centerLabelFontSize}px`, fontWeight: 500 }}
                                      >
                                        {centerLabel}
                                      </text>
                                    )}
                                  </g>
                                );
                              }
                              const displayedVisitors = totalVisitors || totalV || 0;
                              if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                                if (displayedVisitors > 0) {
                                  const formattedValue = typeof displayedVisitors === 'string' ? parseFloat(displayedVisitors) : displayedVisitors;
                                  return (
                                    <text
                                      x={viewBox.cx}
                                      y={viewBox.cy}
                                      textAnchor="middle"
                                      dominantBaseline="middle"
                                    >
                                      <tspan
                                        x={viewBox.cx}
                                        y={viewBox.cy}
                                        className="fill-foreground lg:text-2xl md:text-text-body font-bold sm:text-body"
                                        style={{ fontSize: '15px' }}
                                      >
                                        {formatNumber(formattedValue)}
                                      </tspan>
                                    </text>
                                  );
                                }
                                return null;
                              }
                              return null;
                            }}
                          />
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                ) : (
                  <div className="flex items-center justify-center h-full w-full">
                    <span className="text-body">No chart configuration available</span>
                  </div>
                )}
              </div>
              
              {/* Side Legend */}
              {(legendPosition === 'right' || (!legendPosition && legendPosition !== 'bottom')) && (
                <div className={`flex flex-col justify-start sm:col-span-1 md:col-span-1 lg:col-span-1 sm:text-body border-none ${marginTop} xl:mt-3 w-full min-h-0 ${hideScrollbar ? '' : 'overflow-y-auto scrollbar'}`}
                     style={{
                       height: `${chartHeight}px`,
                       maxHeight: isFullscreen ? `${containerDimensions.height * 0.6}px` : `${chartHeight}px`
                     }}>
                  {legendsTitle && (
                    <div
                      className={`font-semibold mb-1 px-2 flex-shrink-0`}
                      style={{ fontSize: legendsTitleFontSize || '1rem' }}
                    >
                      {legendsTitle}
                    </div>
                  )}
                  <div className={`flex ${direction} md:${direction} sm:${direction} lg:flex-col xl:flex-col mt-1 ${position} flex-1 min-h-0 ${hideScrollbar ? '' : 'overflow-y-auto'}`}>
                    {chartData?.map((item) => (
                      <div
                        key={item.label}
                        className={`flex items-center gap-2 p-1 text-black transition-opacity duration-200 flex-shrink-0 ${selectedSegment === item.label ? 'opacity-100' : 'opacity-70'} ${!disableLegendClick ? 'cursor-pointer' : ''}`}
                        onClick={!disableLegendClick ? () => onSegmentClick && item.label && onSegmentClick({ label: item.label, value: (item[dataKey] as number) || 0 }) : undefined}
                      >
                        <span
                          className="inline-block w-3 h-3 rounded-full border-r p-1 flex-shrink-0"
                          style={{ backgroundColor: item.fill || chartConfig?.[item.label || '']?.color }}
                        />
                        <p className="text-xs break-words donut-legend-item" style={{ fontSize: legendFontSize ? `${legendFontSize}px` : undefined, color: 'black' }}>
                          {String(item.label || '').charAt(0).toUpperCase() + String(item.label || '').slice(1)}
                          {isPercentage && item.value !== undefined && (
                            <span>
                              :{" "}
                              {typeof item.value === 'number' ? `${item.value.toFixed(2)}%` : `${item.value}%`}
                            </span>
                          )}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {/* Bottom Legend */}
            {legendPosition === 'bottom' && (
              <div className="h-[35%] w-full flex justify-center items-center px-2 min-h-0">
                <div className={`flex ${enableHorizontalScroll ? 'flex-nowrap overflow-x-auto' : 'flex-wrap justify-center'} items-center gap-2 sm:gap-3 md:gap-4 max-h-[120px] w-full ${enableHorizontalScroll ? 'scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100' : 'overflow-y-auto scrollbar'} legend-scroll-small`}
                     style={{
                       paddingBottom: `${scrollbarGap}px`,
                       maxHeight: isFullscreen ? `${containerDimensions.height * 0.3}px` : '120px'
                     }}>
                  {chartData?.map((item) => (
                    <div
                      key={item.label}
                      className={`flex items-center justify-center gap-1 sm:gap-2 text-black transition-opacity duration-200 ${selectedSegment === item.label ? 'opacity-100' : 'opacity-70'} ${!disableLegendClick ? 'cursor-pointer' : ''} ${enableHorizontalScroll ? 'flex-shrink-0' : 'min-w-0'}`}
                      onClick={!disableLegendClick ? () => onSegmentClick && item.label && onSegmentClick({ label: item.label, value: (item[dataKey] as number) || 0 }) : undefined}
                    >
                      <span
                        className="inline-block w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: item.fill || chartConfig?.[item.label || '']?.color }}
                      />
                      <p className={`${enableHorizontalScroll ? 'whitespace-nowrap' : 'break-words text-center'} text-xs sm:text-sm donut-legend-item`}
                         style={{ fontSize: legendFontSize ? `${legendFontSize}px` : undefined, color: 'black' }}>
                        {String(item.label || '').charAt(0).toUpperCase() + String(item.label || '').slice(1)}
                        {isPercentage && item.value !== undefined && (
                          <span>
                            :{" "}
                            {typeof item.value === 'number' ? `${item.value.toFixed(2)}%` : `${item.value}%`}
                          </span>
                        )}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-sm">No Data Found !</span>
          </div>
        )}
      </CardContent>
      {/* <CardFooter className=" flex justify-end w-full p-0">
      {isView &&(
            <div className="text-small-font ">
              <a href="#" className="text-blue-500 hover:text-blue-700 cursor-pointer underline">View All</a>
            </div>
            )}
      </CardFooter> */}
    </Card>
  );
};
 
export default DonutChart;
 
 