import React from 'react';
import { <PERSON>,
    Card<PERSON>ontent,
    
    CardHeader,
    CardTitle,

 } from '@/components/ui/card';

 interface InformationCardProps{
    InformTitle?:string;
    informDescription?:string;
 }

export const InformationCard :React.FC<InformationCardProps>= ({
    InformTitle,
    informDescription
}) => {
  return (
    <>
    <Card className='shadow-md rounded-md  w-[120px] xl:w-[200px] h-15 xl:h-20 mb-2 dark:bg-background'>
        <CardHeader className='p-1 bg-yellow-300'>
            <CardTitle className='text-tiny-font sm:text-tiny-font lg:text-small-font xl:text-small-font md:text-tiny-font font-semibold'>
                {InformTitle}
            </CardTitle>
        </CardHeader>
        <CardContent className='text-tiny-font sm:text-tiny-font lg:text-small-font xl:text-small-font md:text-tiny-font mt-0'>
            {informDescription}
        </CardContent>

    </Card>
    </>
  )
}
