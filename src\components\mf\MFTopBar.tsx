"use client";
import { useTheme } from "./theme-context";
import {
  Sun,
  Moon,
  PanelLeftClose,
  PanelLeftOpen,
  User,
  Settings,
  PackageSearch,
  Bell,
  HelpCircle,
} from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import Link from "next/link";
import { Button } from "../ui/button";
import { MFSingleSelect } from "./MFSingleSelect";
import { MFDateRangePicker } from "./MFDateRangePicker";
import SignOutButton from "./SignOut";
import { usePathname } from "next/navigation";
import { getToken } from "@/lib/token";
import { useEffect, useState, } from "react";
// import { useApiCall } from "@/app/(main)/webfraud/queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import { useRouter } from "next/navigation";
import Endpoint from "@/app/(main)/webfraud/common/endpoint";
// function findFirstSubMenuRoute(menus: any[]): string | null {
//   for (const menu of menus) {
//     if (menu.Route && menu.Route !== "") {
//       console.log(`1${menu.Route}`,"eeeeeeeeeeeeee")
//       return menu.Route;
//     }
//     if (menu.SubMenus && menu.SubMenus.length > 0) {
//       const subRoute = findFirstSubMenuRoute(menu.SubMenus);
//       if (subRoute) 
//         console.log(`2${subRoute}`,"eeeeeeee111")
//         return subRoute;
//     }
//   }
//   return null;
// }
type ErrorResponse = {
  message: string;
};

type PackageResponse = string[];

type MFTopBarType = {
  isExpanded: boolean;
  onToggle: () => void;
  isCalender?: boolean;
  isToggle?:boolean;
};
const enable: string[] = [
  "app/dashboard/install",
  "/webfraud/Dashboard/overall-summary",
  "/webfraud/Dashboard/analysis-insights",
  "/webfraud/Dashboard/traffic-insights",
  "/webfraud/Dashboard/actionable-insights",
  "/webfraud/Configuration/WhiteListing-IVT-Category",
  "/webfraud/Configuration/Real-Time-Protection",
  "/webfraud/Download-Ivt-Report/LandingPage-wise",
  "/webfraud/Download-Ivt-Report/Campaign-wise",
  "/webfraud/Configuration/Call-Recommendation",
  // "/webfraud/User-Management/Users-Config",
  // "/webfraud/User-Management/Package-Config",   
  //"/webfraud/User-Management/User-Package-Config",
  "/web-analytics/reportingtool/report",
  "/web-analytics/reportingtool/generate",
  "/app-analytics/Publisher",
  "/app-analytics/dashboard/overall-summary",
  "/app-analytics/analytics",
  "/app-analytics/contributing-publishers/original",
  "/app-analytics/contributing-publishers/reattribution",
 
  "/web-analytics/configuration/ad-manager-apiAccess",

  "/app-analytics/reportingrool/generate",
  "/app-analytics/reportingtool/report",
  "/app-analytics/reportingtool/mail",
  "/integrity/reportingtool/report",
  "/integrity/reportingtool/mail",
  "/integrity/dashboard/overall-summary",
  "/integrity/configuration",
  "/integrity/customConfiguration",
  "/re-engagement/dashboard/overall-summary",
  "/re-engagement/reportingtool/generate",
  "/re-engagement/reportingtool/report",
  "/re-engagement/reportingtool/mail",

  // Brand Infringement - Web and Social Media pages
  "/dashboard",
  "/website/summary",
  "/website/insights",
  "/website/analytics",
  "/website/incidents",
  "/website/tickets",
  "/socialmedia/summary",
  "/socialmedia/insights",
  "/socialmedia/analytics",
  "/socialmedia/incidents",
  "/socialmedia/tickets",
  "/configuration/whitelistingstatus",
  "/configuration/monitoringbots",
  "/configuration/riskcategorization",
  "/reportingtool/report",
  "/reportingtool/mail",
  

  
  

  

];

// export function MFTopBar({
//   isExpanded,
//   onToggle,
//   isCalender = true,
// }: MFTopBarType) {
//   const pathname = usePathname();
//   const { isDarkMode, toggleTheme } = useTheme();

//   // Add console.log to debug the actual pathname
//   // console.log('Current pathname:', pathname);

//   // Check specifically for WhiteListing-IVT-Category page
//   const isWhiteListingPage =
//     pathname === "/webfraud/Configuration/WhiteListing-IVT-Category";
//     const isRealtimeProtection =
//      pathname ===  "/webfraud/Configuration/Real-Time-Protection";
//   const isCallRecommendationPage =
//     pathname === "/webfraud/Configuration/Call-Recommendation";
//   const isGeneratePage = pathname === "/webfraud/ReportingTool/generate";
//   // Check if the current path is enabled
//   const isEnabled = enable.some((path) => pathname.includes(path));
//   const isMailPage = pathname === "/app-analytics/ReportingTool/Mail";
//   const isIntegrityMailPage = pathname === "/Integrity/ReportingTool/Mail";
  

//   return (
//     <div className="shadow-blue-gray-900/5 col-span-2 h-auto bg-background dark:bg-gray-900 dark:text-white w-full p-2">
//       <div className="flex flex-col sm:flex-row items-center gap-2 w-full">
//         <Button
//           title="Toggle Menu"
//           variant="ghost"
//           className="w-full sm:w-14 rounded-md border text-center dark:bg-gray-900 dark:text-white"
//           size="icon"
//           onClick={onToggle}
//         >
//           {isExpanded ? <PanelLeftOpen /> : <PanelLeftClose />}
//         </Button>

//         {isEnabled && (
//           <div className="flex flex-col sm:flex-row gap-2 w-full">
//             {/* Package Select - Always show for enabled pages */}
//             <div className="w-full sm:w-auto">
//               <PackageSelect />
//             </div>

//             {/* Date Range Picker - Hide for WhiteListing and CallRecommendation pages */}
//             {!isWhiteListingPage && !isCallRecommendationPage && !isGeneratePage && !isRealtimeProtection && !isMailPage && !isIntegrityMailPage && (
//               <div className="w-full sm:w-auto">
//                 <MFDateRangePicker className="rounded-md border text-body dark:bg-background  w-full" />
//               </div>
//             )}
//           </div>
//         )}

//         <div className="ml-auto flex items-center gap-2 w-full sm:w-auto justify-between">
//           {/* Theme Toggle Button */}
//           <Button
//             onClick={toggleTheme}
//             variant="ghost"
//             size="icon"
//             title={isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
//             className="rounded-md border"
//           >
//             {isDarkMode ? <Moon /> : <Sun />}
//           </Button>

//           {/* User PopUp */}
//           <UserPopUp />
//         </div>
//       </div>
//     </div>
//   );
// }

// function UserPopUp() {
//   const [Uname, setUname] = useState("");

//   useEffect(() => {
//     const { username } = getToken();
//     setUname(username);
//   }, []);

//   return (
//     <Popover>
//       <PopoverTrigger asChild>
//         <Button
//           className="ml-auto mr-2 rounded-md border"
//           variant="ghost"
//           size="icon"
//           title="User"
//         >
//           <User />
//         </Button>
//       </PopoverTrigger>
//       <PopoverContent className="mr-4 w-fit overflow-clip p-0">
//         <div className="flex flex-col">
//           <div className="bg-slate-200 p-4 dark:bg-slate-700">
//             <p className="text-header">{Uname}</p>
//             <p className="text-body"><EMAIL></p>
//           </div>
//           <ul className="flex justify-between gap-2 px-4 py-2">
//             <li>
//               <Link href="/user-details">
//                 <Button title="Settings" variant="ghost" size="icon">
//                   <Settings />
//                 </Button>
//               </Link>
//             </li>
//             <li className="hover:text-red-500">
//               <SignOutButton />
//             </li>
//           </ul>
//         </div>
//       </PopoverContent>
//     </Popover>
//   );
// }

// function PackageSelect({ compact = false }: { compact?: boolean }) {
//   const [packages, setPackages] = useState<string[]>([]);
//   const [isLoading, setIsLoading] = useState(false);
//   const { selectedPackage, setSelectedPackage } = usePackage();
//   useEffect(() => {
//     const fetchPackages = async () => {
//       setIsLoading(true);
//       try {
//         const token = localStorage.getItem("IDToken");
//         const response = await fetch(
//           "https://central-apis-dev.mfilterit.net/dev/api/v1/access_control/user_packages?product_name=App Performance",
//           {
//             method: "POST",
//             headers: {
//               "Content-Type": "application/json",
//               Authorization: token || "",
//             },
//             body: JSON.stringify({
//               "product_name": "App Performance"
//             }),
//           }
//         );
//         const data = await response.json();
//         console.log('API Response:', data);

//         if (Array.isArray(data)) {
//           // Extract PackageName from each object in the response
//           // Handle both string and object responses
//           const packageNames = data.map((item: any) => {
//             if (typeof item === 'string') {
//               return item;
//             } else if (item && typeof item === 'object' && item.PackageName) {
//               return item.PackageName;
//             }
//             return null;
//           }).filter(Boolean);
          
//           console.log('Extracted package names:', packageNames);
//           setPackages(packageNames);

//           if (!selectedPackage) {
//             const savedPackage = localStorage.getItem("selectedPackage");
//             console.log('Saved package from localStorage:', savedPackage);
//             // Ensure savedPackage is a string, not an object
//             const packageToSelect =
//               savedPackage && packageNames.includes(savedPackage)
//                 ? savedPackage
//                 : packageNames[0];
             
//             if (packageToSelect) {
//               console.log('Setting initial package to:', packageToSelect);
//               setSelectedPackage(packageToSelect);
//             }
//           }
//         }
//       } catch (error) {
//         console.error("Error fetching packages:", error);
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     fetchPackages();
//   }, [selectedPackage, setSelectedPackage]);

//   const items = packages.map((pkg) => ({
//     title: pkg,
//     value: pkg,
//   }));

//   const handlePackageChange = (value: string) => {
//     console.log("Package changed to:", value);
//     setSelectedPackage(value);
//     localStorage.setItem("selectedPackage", value);
//   };

//   // Ensure selectedPackage is always a string
//   const displayValue = typeof selectedPackage === 'string' ? selectedPackage : '';
//   console.log('Display value:', displayValue, 'Type:', typeof displayValue);

//   return (
//     <MFSingleSelect
//       items={items}
//       placeholder={isLoading ? "Loading..." : "Select Package"}
//       title={compact ? displayValue?.slice(0, 8) + "..." : displayValue || ""}
//       className="max-w-40"
//       value={displayValue}
//       onValueChange={handlePackageChange}
//     />
//   );
// }
export function MFTopBar({
  isExpanded,
  onToggle,
  isCalender = true,
  isToggle=true
}: MFTopBarType) {
  const pathname = usePathname();
  const { isDarkMode, toggleTheme } = useTheme();
  const router = useRouter();
  
  // Debug: Log the actual pathname
  console.log('Current pathname:', pathname);
  
  // Function to generate page title based on pathname
  const getPageTitle = (pathname: string): string => {
    if (!pathname) return "";
    
    // Remove leading slash and split by '/'
    const pathParts = pathname.replace(/^\//, '').split('/');
    
    // Handle social media pages
    if (pathname.includes('/socialmedia/')) {
      const section = pathParts[pathParts.length - 1];
      switch (section) {
        case 'summary':
          return 'Social Media Summary';
        case 'insights':
          return 'Social Media Insights';
        case 'analytics':
          return 'Social Media Analytics';
        case 'incidents':
          return 'Social Media Incidents';
        case 'tickets':
          return 'Social Media Tickets';
        default:
          return 'Social Media';
      }
    }
    
    // Handle website/app pages
    if (pathname.includes('/website/')) {
      const section = pathParts[pathParts.length - 1];
      switch (section) {
        case 'summary':
          return 'Web / App Summary';
        case 'insights':
          return 'Web / App Insights';
        case 'analytics':
          return 'Web / App Analytics';
        case 'incidents':
          return 'Web / App Incidents';
        case 'tickets':
          return 'Web / App Tickets';
        default:
          return 'Web / App';
      }
    }

    // Handle configuration pages
    if (pathname.includes('/configuration/')) {
      const section = pathParts[pathParts.length - 1];
      switch (section) {
        case 'monitoringbots':
          return 'Configuration - Monitoring Bots';
        case 'riskcategorization':
          return 'Configuration - Risk Categorization';
        case 'whitelistingstatus':
          return 'Configuration - Whitelisting Status';
        default:
          return 'Configuration';
      }
    }

    // Handle reporting tool pages
    if (pathname.includes('/reportingtool/')) {
      const section = pathParts[pathParts.length - 1];
      switch (section) {
        case 'report':
          return 'Reporting Tool - Report';
        case 'generate':
          return 'Reporting Tool - Generate';
        case 'mail':
          return 'Reporting Tool - Mail';
        default:
          return 'Reporting Tool';
      }
    }

    // Handle brand infringement dashboard
    if (pathname === '/dashboard' || pathname === '/brand-infringement') {
      return 'Brand Infringement Dashboard';
    }
    
    if (pathname.includes('/app/dashboard/install')) {
      return 'Dashboard';
    }

    if (pathname.includes('/user-details')) {
      return 'User Details';
    }

    if (pathname.includes('/ticketing')) {
      return 'Support & Ticketing';
    }

    if (pathname.includes('/unified-ad-manager')) {
      return 'Unified Ad Manager';
    }

    if (pathname.includes('/webfraud')) {
      return 'Web Fraud Detection';
    }
    
    // Default: capitalize first letter of each path part
    return pathParts.map(part => 
      part.charAt(0).toUpperCase() + part.slice(1).replace(/-/g, ' ')
    ).join(' ');
  };

  const pageTitle = getPageTitle(pathname || '');
  console.log('Generated page title:', pageTitle);
 
 
  // Check specifically for WhiteListing-IVT-Category page
  const isWhiteListingPage =
    pathname === "/webfraud/Configuration/WhiteListing-IVT-Category";
  const isRealtimeProtection =
    pathname === "/webfraud/Configuration/Real-Time-Protection";
  const isCallRecommendationPage =
    pathname === "/webfraud/Configuration/Call-Recommendation";
  const isGeneratePage = pathname === "/web-analytics/reportingtool/generate";
  const isWebsiteTicketsPage = pathname === "/website/tickets" || pathname.includes("/brand-infringement/website/tickets");
  const isSocialMediaTicketsPage = pathname === "/socialmedia/tickets" || pathname.includes("/brand-infringement/socialmedia/tickets");
  const isWebsiteInsightsPage = pathname === "/website/insights" || pathname.includes("/brand-infringement/website/insights");
  const isSocialMediaInsightsPage = pathname === "/socialmedia/insights" || pathname.includes("/brand-infringement/socialmedia/insights");
  // Check if the current path is enabled
  const isEnabled = enable.some((path) => pathname.includes(path));
 
  return (
    <div className="shadow-blue-gray-900/5 col-span-2 h-auto bg-background dark:bg-gray-900 dark:text-white w-full p-2">
      <div className="flex flex-col sm:flex-row items-center gap-2 w-full relative">
        {/* {isToggle && ( */}
        <Button
          title="Toggle Menu"
          variant="ghost"
          className="w-full sm:w-14 rounded-md border text-center dark:bg-gray-900 dark:text-white z-10"
          size="icon"
          onClick={onToggle}
        >
          {isExpanded ? <PanelLeftOpen /> : <PanelLeftClose />}
        </Button>
        {/* )} */}
 
        {isEnabled && (
          <div className="flex flex-col sm:flex-row gap-2 w-full">
            {/* Package Select - Always show for enabled pages */}
            <div className="w-full sm:w-auto">
              <PackageSelect />
            </div>
 
            {/* Date Range Picker - Hide for WhiteListing, CallRecommendation pages */}
            {!isWhiteListingPage && !isCallRecommendationPage && !isGeneratePage && !isRealtimeProtection && !isWebsiteInsightsPage && !isSocialMediaInsightsPage && (
              <div className="w-full sm:w-auto">
                <MFDateRangePicker className="rounded-md  border text-body dark:bg-background  w-full" />
              </div>
            )}
           {/* <div className=" flex flex-grow text-black justify-center items-center font-semibold text-header dark:text-white">{dynamicTitle}</div> */}
          </div>
        )}
         
        {/* Center: Page heading - Improved positioning */}
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-0">
          <h1 className="text-lg font-bold text-gray-900 dark:text-white whitespace-nowrap px-6 py-2 bg-white/95 dark:bg-gray-800/95 rounded-lg">
            {pageTitle}
          </h1>
        </div>
 
        <div className="ml-auto flex items-center gap-2 w-full sm:w-auto justify-between z-10">
          {/* Theme Toggle Button */}

          <Button
            variant="ghost"
            size="icon"
            title="Support "
            className="rounded-md border"
            onClick={() => {
              router.push("/ticketing/dashboard/overall-summary");
            }}
          >
            
            <HelpCircle className="text-foreground" />
          </Button>
          <Button
            onClick={toggleTheme}
            variant="ghost"
            size="icon"
            title={isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
            className="rounded-md border"
          >
            {isDarkMode ? <Moon /> : <Sun />}
          </Button>
          <KebabMenu />
          
          {/* User PopUp */}
          <UserPopUp />
        </div>
      </div>
    </div>
  );                             
}
 
function UserPopUp() {
  const [Uname, setUname] = useState("");
const [username, setUsername] = useState<string | null>(null);
 
  useEffect(() => {
    
  }, []);
  useEffect(() => {
    const { username } = getToken();
    setUname(username);
    const storedUsername = localStorage.getItem("username");
    setUsername(storedUsername);
  
  }, []);
  // console.log("user value", Uname,email);
  
 
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="ml-auto mr-2 rounded-md border"
          variant="ghost"
          size="icon"
          title="User"
        >
          <User />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="mr-4 w-fit overflow-clip p-0">
        <div className="flex flex-col">
          <div className="bg-slate-200 p-4 dark:bg-slate-700">
            {/* <p className="text-header">{Uname}</p> */}
            <p className="text-header"> {`Hello, ${Uname}`}</p>
               <p className="text-small-font">{username}</p>          
               </div>
          <ul className="flex justify-between gap-2 px-4 py-2">
            <li>
              <Link href="/user-details/security">
                <Button title="Settings" variant="ghost" className="text-xs pl-1 text-blue-500 hover:underline hover:text-blue-500">
                  {/* <Settings /> */}
                  Change Password
                </Button>
              </Link>
            </li>
            <li className="hover:text-red-500">
              <SignOutButton />
            </li>
          </ul>
        </div>
      </PopoverContent>
    </Popover>
  );
}
 
interface PackageType {
  PackageName: string;
  PackageTitle: string;
}
 
function PackageSelect({ compact = false }: { compact?: boolean }) {
  const [packages, setPackages] = useState<PackageType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { selectedPackage, setSelectedPackage } = usePackage();
 
  useEffect(() => {
    const fetchPackages = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("IDToken");
        const response = await fetch(
          "https://central-apis-dev.mfilterit.net/dev/api/v1/access_control/user_packages?product_name=Brand Infringement",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: token || "",
            },
            body: JSON.stringify({
              "product_name": "Brand Infringement"
            }),
          }
        );
        const data = await response.json();
        if (Array.isArray(data)) {
          // Filter out packages with empty PackageName values
          const validPackages = data.filter((pkg: PackageType) => pkg.PackageName && pkg.PackageName.trim() !== "");

          setPackages(validPackages);

          if (!selectedPackage) {
            const savedPackage = localStorage.getItem("selectedPackage");
            const packageToSelect = savedPackage && validPackages.some(pkg => pkg.PackageName === savedPackage)
              ? savedPackage
              : validPackages[0]?.PackageName;

            if (packageToSelect) {
              setSelectedPackage(packageToSelect);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setIsLoading(false);
      }
    };
 
    fetchPackages();
  }, []);
 
  const items = packages.map((pkg) => ({
    title: pkg.PackageTitle || pkg.PackageName,
    value: pkg.PackageName,
  }));
 
  const handlePackageChange = (value: string) => {
    console.log("Package changed to:", value);
    setSelectedPackage(value);
    localStorage.setItem("selectedPackage", value);
  };
 
  const selectedPackageTitle = packages.find(pkg => pkg.PackageName === selectedPackage)?.PackageTitle || selectedPackage;
 
  return (
    <MFSingleSelect
      items={items}
      placeholder={isLoading ? "Loading..." : "Select Package"}
      className="max-w-40 h-9"
      value={selectedPackage}
      onValueChange={handlePackageChange}
    />
  );
}
 
function KebabMenu() {

  function findFirstSubMenuRoute(menus: any[]): string | null {
  for (const menu of menus) {
    if (menu.Route && menu.Route !== "") {
      console.log(`1${menu.Route}`,"eeeeeeeeeeeeee")
      return menu.Route;
    }
    if (menu.SubMenus && menu.SubMenus.length > 0) {
      const subRoute = findFirstSubMenuRoute(menu.SubMenus);
      if (subRoute) 
        console.log(`2${subRoute}`,"eeeeeeee111")
        return subRoute;
    }
  }
  return null;
}

  // Function to handle more products redirection
  const handleMoreProductClick = (product: any) => {
    if (product.redirect_link) {
      window.open(product.redirect_link, '_blank');
    }
  };

  // Simulated API response (replace with actual API call in production)
  const [availableProducts, setAvailableProducts] = useState<Array<{
    icon: string;
    label: string;
    route: string;
    name?: string;
  }>>([]);
  const [moreProducts, setMoreProducts] = useState<Array<{
    icon: string;
    label: string;
    route?: string;
    name?: string;
    redirect_link?: string;
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMenuLoading, setIsMenuLoading] = useState(false);
  const [selectedProductIdx, setSelectedProductIdx] = useState<number>(0); // Track selected product index
  const router = useRouter();
  const pathname = usePathname();
 
  // Function to fetch menu data and redirect with first submenu route
  const fetchMenuAndRedirect = async (productRoute: string, productName: string) => {

    setIsMenuLoading(true);
    // window.location.href = "https://uat-dashboard.mfilterit.net/web-analytics/Dashboard/overall-summary"
    
    try {
      const token = localStorage.getItem("IDToken");
      
      console.log("Calling menu API with product name:", productName);
      
      // Call the menu API with the product name in the POST body
      const menuResponse = await fetch(
        `${process.env.NEXT_PUBLIC_PRODUCT}access_control/menus`,
        {
          method: "POST",
          headers: {
            Authorization: token || "",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ product_name: productName }),
        }
      );
      
      if (!menuResponse.ok) {
        throw new Error(`Menu API failed with status: ${menuResponse.status}`);
      }
      
      const menuData = await menuResponse.json();
      console.log("Menu API response:", menuData);
 console.log(`3${menuData}`,"eeeeeeeee2222")
      let firstSubMenuRoute = findFirstSubMenuRoute(menuData) || "";
      console.log("First submenu route found:", firstSubMenuRoute);
      console.log(`4${firstSubMenuRoute}`,"eeeeeeee444444444")
      // Remove the first segment if it starts with a slash and has more than one segment
      if (firstSubMenuRoute.startsWith("/")) {
        const parts = firstSubMenuRoute.split("/");
        // if (parts.length > 2) {
        //   // parts[0] is '', parts[1] is the first segment to remove
        //   firstSubMenuRoute = "/" + parts.slice(2).join("/");
        // }
      }
 
      const finalRoute = firstSubMenuRoute
        ? `${productRoute}${firstSubMenuRoute.startsWith("/") ? "" : "/"}${firstSubMenuRoute}`
        : productRoute;
      
      console.log("Final route to navigate:", finalRoute);
      window.location.href =`${finalRoute}`
      // router.push(finalRoute);
    } catch (error) {
      console.error("Error fetching menu data:", error);
      console.log("Final route to navigate:", error);
      // fallback to just the product route if menu API fails
      
      // router.push(productRoute);
    } finally {
      // setIsMenuLoading(false);
    }
  };
 
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("IDToken");
        // Replace with your actual API endpoint
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_PRODUCT}access_control/products?config=true`,
          {
            method: "GET",
            headers: {
              Authorization: token || "",
            },
          }
        );
        const data = await response.json();
        
        // Expecting data to have available_products and more_products arrays
        setAvailableProducts(Array.isArray(data.available_products) ? data.available_products : []);
        setMoreProducts(Array.isArray(data.more_products) ? data.more_products : []);
        const index = data?.available_products?.findIndex((item: any) => item.label === "Brand Infringement");
        setSelectedProductIdx(index); // Always select the first product after fetch
        console.log(index,"index")
      } catch (error) {
        setAvailableProducts([]);
        setMoreProducts([]);
        // setSelectedProductIdx(0);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, []);
 console.log(availableProducts,selectedProductIdx,"qqqqqqqqqqqqqqqq")
  useEffect(() => {
    // Find the index of the product whose route matches the current pathname
    const idx = availableProducts.findIndex((app) =>
      pathname.startsWith(app.route)
    );
    if (idx !== -1 && idx !== selectedProductIdx) {
      setSelectedProductIdx(idx);
    }
    // Optionally, if you have a context for selected product, update it here too
  }, [pathname, availableProducts]);
 
  // Helper to render SVG from string
  const renderSVG = (svgString: string) => (
    <span
      className="mb-1"
      dangerouslySetInnerHTML={{ __html: svgString }}
    />
  );
 
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className=" rounded-md border"
          variant="ghost"
          size="icon"
          title="Select Product"
        >
          <PackageSearch />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[320px] p-4 bg-white dark:bg-gray-900 rounded-2xl shadow-lg">
        {/* Section 1: Subscribed/Available Products */}
        <div>
          <div className="text-xs font-semibold text-gray-500 dark:text-gray-300 mb-2 px-1">
            {isLoading ? "Loading..." : "Your Subscriptions"}
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            {availableProducts.map((app, idx) => {
              const isSelected = idx === selectedProductIdx;
              return (
                <div
                  key={idx}
                  className={`flex flex-col items-center justify-center p-3 rounded-xl transition-colors ${
                    isSelected
                      ? 'bg-blue-100 dark:bg-secondary-900 border-2 border-secondary cursor-not-allowed opacity-70' // Highlighted and disabled
                      : 'hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer'
                  } ${isMenuLoading ? 'opacity-50' : ''}`}
                  style={isSelected ? { pointerEvents: 'none' } : {}}
                  onClick={async () => {
                    if (!isSelected && app.route && !isMenuLoading) {
                      setSelectedProductIdx(idx); // Highlight and disable the clicked product
                      // Use the product name if available, otherwise use the label as fallback
                      const productName = app.name || app.label;
                      await fetchMenuAndRedirect(app.route, productName);
                    }
                  }}
                >
                  {renderSVG(app.icon)}
                  <span className="text-xs text-center mt-1">{app.label}</span>
                </div>
              );
            })}
          </div>
        </div>
        {/* Divider */}
        <div className="border-t border-gray-200 dark:border-gray-700 my-2" />
        {/* Section 2: More Products */}
        <div>
          <div className="text-xs font-semibold text-gray-500 dark:text-gray-300 mb-2 px-1">
            {isLoading ? "Loading..." : "Available Add-ons"}
          </div>
          <div className="grid grid-cols-2 gap-4">
            {moreProducts.map((app, idx) => (
              <div
                key={idx}
                className={`flex flex-col items-center justify-center p-3 rounded-xl ${app.redirect_link ? 'hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer' : 'opacity-60 cursor-not-allowed'} transition-colors ${isMenuLoading ? 'opacity-50' : ''}`}
                onClick={() => {
                  if (app.redirect_link) {
                    handleMoreProductClick(app);
                  }
                }}
              >
                {renderSVG(app.icon)}
                <span className="text-xs text-center mt-1">{app.label}</span>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}