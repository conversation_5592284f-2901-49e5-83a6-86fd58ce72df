import React from 'react';
import KeyValueCard from './keyvalueCard';

interface CustomKeyValueCardProps {
  title: string;
  value: string;
  change: string;
  colorClass: string;
}

const CustomKeyValueCard: React.FC<CustomKeyValueCardProps> = ({
  title,
  value,
  change,
  colorClass
}) => {
  return (
    <div className="bg-white rounded-md shadow-sm border border-gray-300 p-2">
      <div className="flex justify-between items-center mb-1">
        <div className="text-sm font-semibold bg-white inline-block">
          {title}
        </div>
        {change && (
          <span
            className={`text-base font-medium ${
              title === "Total Incident" ? "text-[#540094]" :
              title === "Under Review" ? "text-[#E54030]" :
              title === "In Progress" ? "text-[#4CAF50]" :
              title === "Closed" ? "text-[#8B0000]" : ""
            }`}
          >
            {change}
          </span>
        )}
      </div>
      <div className={`text-2xl font-bold text-center my-1`}>{value}</div>
      <div className="w-full h-2 bg-gray-200 rounded-full mt-1">
        <div
          className="h-2 rounded-full"
          style={{
            width:
              title === "Closed"
                ? "100%"
                : title === "In Progress"
                  ? "50%"
                  : title === "Under Review"
                    ? "27%"
                    : "100%",
            backgroundColor: colorClass,
          }}
        ></div>
      </div>
    </div>
  );
};

export default CustomKeyValueCard; 