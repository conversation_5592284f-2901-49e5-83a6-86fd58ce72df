import * as React from "react";
 
import { cn } from "@/lib/utils";
import { cva, VariantProps } from "class-variance-authority";
 
const textareaVariants = cva(
  "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      sx: {
        default: "min-h-[80px]",
        sm: "min-h-[60px] text-xs",
        lg: "min-h-[120px] text-base",
      },
    },
    defaultVariants: {
      sx: "default",
    },
  },
);
 
export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof textareaVariants> {}
 
const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ sx, className, ...props }, ref) => {
    return (
<textarea
        className={cn(textareaVariants({ sx, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Textarea.displayName = "Textarea";
 
export { Textarea };