"use client";

import React, { createContext, useContext, useState } from 'react';
import { subDays, format } from 'date-fns';
import { DateRange } from 'react-day-picker';

interface DateRangeContextType {
  startDate: string;
  endDate: string;
  setDateRange: (start: string, end: string) => void;
  dateRangeToString: (range: DateRange | undefined) => { start: string; end: string } | null;
  stringToDateRange: (start: string, end: string) => DateRange;
}

const DateRangeContext = createContext<DateRangeContextType | undefined>(undefined);

export function DateRangeProvider({ children }: { children: React.ReactNode }) {
  const [startDate, setStartDate] = useState(format(subDays(new Date(), 7), 'yyyy-MM-dd'));
  const [endDate, setEndDate] = useState(format(new Date(), 'yyyy-MM-dd'));

  const setDateRange = (start: string, end: string) => {
    setStartDate(start);
    setEndDate(end);
  };

  // Convert DateRange to string dates in yyyy-MM-dd format
  const dateRangeToString = (range: DateRange | undefined) => {
    if (!range?.from || !range?.to) return null;
    
    // Ensure dates are in local timezone at midnight
    const fromDate = new Date(range.from);
    fromDate.setHours(0, 0, 0, 0);
    
    const toDate = new Date(range.to);
    toDate.setHours(23, 59, 59, 999);
    
    return {
      start: format(fromDate, 'yyyy-MM-dd'),
      end: format(toDate, 'yyyy-MM-dd')
    };
  };

  // Convert string dates to DateRange
  const stringToDateRange = (start: string, end: string): DateRange => {
    const fromDate = new Date(start);
    fromDate.setHours(0, 0, 0, 0);
    
    const toDate = new Date(end);
    toDate.setHours(23, 59, 59, 999);
    
    return {
      from: fromDate,
      to: toDate
    };
  };

  return (
    <DateRangeContext.Provider value={{ 
      startDate, 
      endDate, 
      setDateRange,
      dateRangeToString,
      stringToDateRange
    }}>
      {children}
    </DateRangeContext.Provider>
  );
}

export function useDateRange() {
  const context = useContext(DateRangeContext);
  if (context === undefined) {
    throw new Error('useDateRange must be used within a DateRangeProvider');
  }
  return context;
} 