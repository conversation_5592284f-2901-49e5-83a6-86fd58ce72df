"use client";
import React, {
  useState,
  useMemo,
  useRef,
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
} from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FolderSearch } from "lucide-react";
import { FilterPill } from "@/components/mf/Filters";
import { useQuery } from "react-query";
import axios from "axios";
import Endpoint from "@/common/endpoint";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { usePackage } from "@/components/mf/PackageContext";
import { FilterState } from "@/components/mf/Filters/FilterPill";
import { StackedBarWithLine } from "@/components/mf/charts/StackedBarwithLine";
import { format, subDays } from "date-fns";
import { subMonths } from "date-fns";
import {
  Pie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  CartesianGrid,
  XAxis,
  YAxis,
  Legend,
  Tooltip,
  Area,
  AreaChart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts";
import HeaderRow from "@/components/mf/HeaderRow";
import { formatNumber, handleCSVDownloadFromResponse, onExpand } from "@/lib/utils";
import DynamicBarChart from "@/components/mf/DynamicBarChart";
import ResizableTable from "@/components/mf/TableComponent";
import DonutChart from "@/components/mf/DonutChart";
import ProgressBarChart1 from "@/components/mf/charts/ProgressBar";

interface FilterResponse {
  data: string[];
  status: boolean;
}

interface TrafficTrendData {
  date: string;
  Active: number;
  "In Progress": number;
  Closed: number;
}

// Types for geographical map data
interface GeographicalMapData {
  region: string;
  Active: number;
  "In Progress": number;
  Closed: number;
}

interface GeographicalMapConfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

// Hardcoded data for the Geographical Map chart
const geographicalMapData: GeographicalMapData[] = [
  { region: "North India", Active: 45, "In Progress": 35, Closed: 30 },
  { region: "South India", Active: 38, "In Progress": 28, Closed: 25 },
  { region: "East India", Active: 32, "In Progress": 24, Closed: 20 },
  { region: "West India", Active: 42, "In Progress": 32, Closed: 28 },
  { region: "Central India", Active: 35, "In Progress": 26, Closed: 22 },
  { region: "International", Active: 28, "In Progress": 20, Closed: 18 },
];

const geographicalMapChartConfig: GeographicalMapConfig = {
  Active: { label: "Active", color: "#E54030" },
  "In Progress": { label: "In Progress", color: "#FFBB28" },
  Closed: { label: "Closed", color: "#4CAF50" },
};

// Hardcoded data for the pie chart with better labels
const pieChartData = [
  { name: "Indian", value: 15, color: "#0088CC" },
  { name: "International", value: 5, color: "#FF8042" },
];

// Custom legend renderer for pie chart
const CustomLegend = (props: any) => {
  const { payload } = props;

  return (
    <div className="flex justify-center gap-4 mt-4">
      {payload.map((entry: any, index: number) => (
        <div key={`legend-${index}`} className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span className="text-sm">
            {entry.value} - {pieChartData[index].value}
          </span>
        </div>
      ))}
    </div>
  );
};

// Add interfaces for the table data
interface WebsiteData extends Record<string, string | number> {
  website: string;
  ip_address: string;
  creation_date: string;
  update_date: string;
  brand_logo: string;
}

interface Column<T> {
  key: string;
  title: string;
  render?: (item: T) => React.ReactNode;
}

// Sample data for the websites table
const websitesData: WebsiteData[] = [
  {
    website: "example1.com",
    ip_address: "***********",
    creation_date: "2024-01-15",
    update_date: "2024-03-20",
    brand_logo: "Brand A",
  },
  {
    website: "example2.com",
    ip_address: "***********",
    creation_date: "2024-02-01",
    update_date: "2024-03-19",
    brand_logo: "Brand B",
  },
  {
    website: "example3.com",
    ip_address: "***********",
    creation_date: "2024-02-15",
    update_date: "2024-03-18",
    brand_logo: "Brand C",
  },
];

// Sample data mapping for each product's website details
const defaultProductWebsitesData: { [key: string]: WebsiteData[] } = {
  "Product A": [
    {
      website: "productA-store1.com",
      ip_address: "***********",
      creation_date: "2024-01-15",
      update_date: "2024-03-20",
      brand_logo: "Product A",
    },
  ],
  "Product B": [
    {
      website: "productB-store1.com",
      ip_address: "***********",
      creation_date: "2024-01-20",
      update_date: "2024-03-18",
      brand_logo: "Product B",
    },
  ],
};

const Analytics = () => {
  // States for filters
  const [selectedBrand, setSelectedBrand] = useState<string[]>(["all"]);
  const [selectedPriority, setSelectedPriority] = useState<string[]>([
    "all",
  ]);
  const [selectedCountry, setSelectedCountry] = useState<string[]>(["all"]);
  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [productWebsitesData] = useState<{ [key: string]: WebsiteData[] }>(
    defaultProductWebsitesData
  );
  const [searchTerm, setSearchTerm] = useState("");

  // Get date range and package from context
  const { startDate: fromDate, endDate: toDate } = useDateRange();
  const { selectedPackage } = usePackage();

  // Query params for API calls
  const queryParams = useMemo(() => {
    const getFilterArray = (selected: string[]) => {
      if (selected.length === 0 || selected.includes("all")) {
        return ["all"];
      }
      return selected;
    };

    return {
      package_name: selectedPackage,
      fromDate,
      toDate,
      brand: getFilterArray(selectedBrand),
      priority: getFilterArray(selectedPriority),
      country: getFilterArray(selectedCountry),
    };
  }, [
    fromDate,
    toDate,
    selectedBrand,
    selectedPriority,
    selectedCountry,
    selectedPackage,
  ]);

  // Fetch filter data
  const { data: brandFilterData } = useQuery<FilterResponse>({
    queryKey: ["brandFilter",  selectedPackage,fromDate, toDate],
    queryFn: async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
          Endpoint.BI.BI_FILTERS.replace(":col", "brand"),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
  });

  const { data: priorityFilterData } = useQuery<FilterResponse>({
    queryKey: ["priorityFilter",  selectedPackage,fromDate, toDate],
    queryFn: async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
          Endpoint.BI.BI_FILTERS.replace(":col", "priority"),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
  });

  const { data: countryFilterData } = useQuery<FilterResponse>({
    queryKey: ["countryFilter", fromDate, toDate, selectedPackage],
    queryFn: async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
          Endpoint.BI.BI_FILTERS.replace(":col", "country"),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
  });

  // Memoized filter data
  const brandFilters = useMemo(
    () =>
      (brandFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedBrand.includes(label) || selectedBrand.includes("all"),
      })),
    [brandFilterData?.data, selectedBrand]
  );

  const priorityFilters = useMemo(
    () =>
      (priorityFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedPriority.includes(label) ||
          selectedPriority.includes("all"),
      })),
    [priorityFilterData?.data, selectedPriority]
  );

  const countryFilters = useMemo(
    () =>
      (countryFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedCountry.includes(label) ||
          selectedCountry.includes("all"),
      })),
    [countryFilterData?.data, selectedCountry]
  );

  // Handle filter changes
  const handleFilterSubmit = (id: string, data: FilterState) => {
    switch (id) {
      case "brand":
        setSelectedBrand(
          data.is_select_all
            ? ["all"]
            : data.filters.filter((f) => f.checked).map((f) => f.label)
        );
        break;
      case "priority":
        setSelectedPriority(
          data.is_select_all
            ? ["all"]
            : data.filters.filter((f) => f.checked).map((f) => f.label)
        );
        break;
      case "country":
          setSelectedCountry(
          data.is_select_all
            ? ["all"]
            : data.filters.filter((f) => f.checked).map((f) => f.label)
        );
        break;
    }
  };

  // Handle filter search
  const handleFilterSearch = (id: string, query: string) => {
    console.log(`Searching ${id} with query: ${query}`);
  };

  // Hardcoded chart data
  const chartData = useMemo(() => geographicalMapData, []);

  const cardRefs = useRef<HTMLElement[]>([]);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  // Handle fullscreen exit to reset expandedCard state
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && 
          !(document as any).webkitFullscreenElement && 
          !(document as any).mozFullScreenElement && 
          !(document as any).msFullscreenElement) {
        if (expandedCard !== null) {
          setExpandedCard(null);
        }
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [expandedCard]);

  // Add API call for Major Platforms
  const { data: majorPlatformsApiData, isLoading: isMajorPlatformsLoading } = useQuery(
    ["majorPlatforms", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.MAJOR_PLATFORM,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Map API data to ProgressBarChart1 format
  const productSalesData = React.useMemo(() => {
    if (!majorPlatformsApiData?.data) return [];
    return majorPlatformsApiData.data.map((item: any) => ({
      label: item.platform,
      visit: item.count,
      percentage: `${item.takedown_percentage}%`,
      fill: ""
    }));
  }, [majorPlatformsApiData?.data]);

  // Handle bar click in Product Sales chart
  const handleBarClick = (data: { label: string }) => {
    setSelectedProduct(data.label);
  };

  // Configuration for Product Sales chart
  const productSalesConfig = {
    Product: {
      label: "Sales",
      color: "#147878", // Orange color
    },
  };

  // Chart config for each platform (preserve original colors)
  const PlatformChartConfig = {
    incidents: { label: "Incidents", color: "#3B82F6" },
    unique: { label: "Unique", color: "#60A5FA" },
  };
  const TelegramChartConfig = {
    incidents: { label: "Incidents", color: "#24A1DE" }, // Telegram blue
    unique: { label: "Unique", color: "#A7D8F0" },
  };
  const XChartConfig = {
    incidents: { label: "Incidents", color: "#000000" }, // X black
    unique: { label: "Unique", color: "#A9A9A9" },
  };
  const InstagramChartConfig = {
    incidents: { label: "Incidents", color: "#E1306C" }, // Instagram pink
    unique: { label: "Unique", color: "#FBB1C8" },
  };

  // Telegram chart data and config
  // Generate last 4 months and MTD for Telegram chart
  const getLast4MonthsAndMTDTelegramChart = () => {
    const now = new Date();
    const months: string[] = [];
    for (let i = 4; i > 0; i--) {
      const d = subMonths(now, i);
      months.push(format(d, "MMM"));
    }
    months.push("MTD");
    // For demo, generate dummy data for each category
    return months.map((label, idx) =>
      label === "MTD"
        ? { label: label, value: 40 + idx * 2, Unique: 30 + idx * 2, Incidents: 18 + idx * 2 }
        : { label: label, value: 32 + idx * 5, Unique: 32 + idx * 5, Incidents: 18 + idx * 2 }
    );
  };
  const TelegramChartData = useMemo(() => getLast4MonthsAndMTDTelegramChart(), []);

  // X (Twitter) chart data and config
  // Generate last 4 months and MTD for X (Twitter) chart
  const getLast4MonthsAndMTDXChart = () => {
    const now = new Date();
    const months: string[] = [];
    for (let i = 4; i > 0; i--) {
      const d = subMonths(now, i);
      months.push(format(d, "MMM"));
    }
    months.push("MTD");
    // For demo, generate dummy data for each category
    return months.map((label, idx) =>
      label === "MTD"
        ? { label: label, value: 50 + idx * 2, Unique: 40 + idx * 2, Incidents: 22 + idx * 2 }
        : { label: label, value: 38 + idx * 5, Unique: 38 + idx * 5, Incidents: 22 + idx * 2 }
    );
  };
  const XChartData = useMemo(() => getLast4MonthsAndMTDXChart(), []);

  // Instagram chart data and config
  // Generate last 4 months and MTD for Instagram chart
  const getLast4MonthsAndMTDInstagramChart = () => {
    const now = new Date();
    const months: string[] = [];
    for (let i = 4; i > 0; i--) {
      const d = subMonths(now, i);
      months.push(format(d, "MMM"));
    }
    months.push("MTD");
    // For demo, generate dummy data for each category
    return months.map((label, idx) =>
      label === "MTD"
        ? { label: label, value: 70 + idx * 2, Unique: 60 + idx * 2, Incidents: 35 + idx * 2 }
        : { label: label, value: 55 + idx * 5, Unique: 55 + idx * 5, Incidents: 35 + idx * 2 }
    );
  };
  const InstagramChartData = useMemo(() => getLast4MonthsAndMTDInstagramChart(), []);

  const areaChartData = [
    { date: "2024-03-01", incidents: 120, unique: 30 },
    { date: "2024-03-02", incidents: 150, unique: 25 },
    { date: "2024-03-03", incidents: 180, unique: 35 },
    { date: "2024-03-04", incidents: 160, unique: 40 },
    { date: "2024-03-05", incidents: 200, unique: 20 },
    { date: "2024-03-06", incidents: 220, unique: 15 },
    { date: "2024-03-07", incidents: 190, unique: 45 },
  ];

  // Area chart config
  const areaChartConfig = {
    incidents: {
      label: "Incidents",
      color: "#540094",
      fillOpacity: 0.4,
    },
    unique: {
      label: "Unique",
      color: "#E54030",
      fillOpacity: 0.4,
    },
  };

  // Add CSV download function
  const handleCSVDownload = useCallback(async (apiDetail: string, cardTitle: string) => {
    try {
      setLoading(true);
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const getFilterArray = (selected: string[]) => {
        if (selected.length === 0 || (selected.length === 1 && selected.includes("All"))) {
          return ["All"];
        }
        return selected.filter(item => item !== "All");
      };

      const basePayload: any = {
        package_name: selectedPackage,
        fromDate: fromDate,
        toDate: toDate,
        country: getFilterArray(selectedCountry),
        priority: getFilterArray(selectedPriority),
        brand: getFilterArray(selectedBrand),
        export_type: "csv"
      };

      let endpoint = '';
      let payload: any = { ...basePayload };

      switch (apiDetail) {
        case "incident_platform":
          endpoint = Endpoint.BI.SM_ANALYTICS.INCIDENT_BY_PLATFORM;
          break;
        case "incident_category":
          endpoint = Endpoint.BI.SM_ANALYTICS.INCIDENT_BY_CATEGORY;
          break;
        case "major_platforms":
          endpoint = Endpoint.BI.SM_ANALYTICS.MAJOR_PLATFORM;
          break;
        case "pending_incidents":
          endpoint = Endpoint.BI.SM_ANALYTICS.PENDING_INCIDENT;
          break;
        case "contact_no":
          endpoint = Endpoint.BI.SM_ANALYTICS.CONTACT_NUMBER;
          break;
        case "category_takedown":
          endpoint = Endpoint.BI.SM_ANALYTICS.CATEGORY_TAKEDOWN;
          break;
        case "suspicious_offer_categories":
          endpoint = Endpoint.BI.SM_ANALYTICS.SUSPICIOUS_OFFER_CATEGORY;
          break;
        case "customer_care_trend":
          endpoint = Endpoint.BI.SM_ANALYTICS.CUSTOMER_CARE_TREND;
          break;
        case "top_platform_trend":
          endpoint = Endpoint.BI.SM_ANALYTICS.PLATFORM_TRENDS;
          break;
        // Add more cases as needed
        default:
          throw new Error(`Unknown API detail: ${apiDetail}`);
      }

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + endpoint,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          // responseType: 'blob'
        }
      );

       const fileName = `${cardTitle}.csv`;
       const success = await handleCSVDownloadFromResponse(response, fileName);
       
       if (!success) {
         throw new Error('Failed to download CSV file');
       }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    } finally {
      setLoading(false);
    }
  }, [selectedPackage, fromDate, toDate, selectedBrand, selectedPriority, selectedCountry]);

  // Base expand handler
  const handleExpandBase = useCallback(
    (index: number) => {
      onExpand(index, cardRefs, expandedCard, setExpandedCard);
    },
    [expandedCard]
  );

  // Individual expand handlers for each chart
  const handleGeographicalMapExpand = useCallback(() => {
    handleExpandBase(3);
  }, [handleExpandBase]);

  const handlePieChartExpand = useCallback(() => {
    handleExpandBase(4);
  }, [handleExpandBase]);

  const handleTopDomainProvidersExpand = useCallback(() => {
    handleExpandBase(5);
  }, [handleExpandBase]);

  const handleWebsitesTableExpand = useCallback(() => {
    handleExpandBase(6);
  }, [handleExpandBase]);

  const handleSample1Expand = useCallback(() => {
    handleExpandBase(15);
  }, [handleExpandBase]);

  const handleSuspiciousOffersExpand = useCallback(() => {
    handleExpandBase(16);
  }, [handleExpandBase]);

  const handleCustomerCareExpand = useCallback(() => {
    handleExpandBase(17);
  }, [handleExpandBase]);

  const handleContactNoExpand = useCallback(() => {
    handleExpandBase(18);
  }, [handleExpandBase]);

  const handleInvestigateClick = (item: WebsiteData) => {
    console.log("Investigating item:", item);
    // Add your investigation logic here
  };

  // Update the columns type
  const websitesColumns: Column<WebsiteData>[] = [
    {
      key: "website",
      title: "List of Websites",
    },
    {
      key: "ip_address",
      title: "IP Address",
    },
    {
      key: "creation_date",
      title: "Creation Date",
    },
    {
      key: "update_date",
      title: "Update Date",
    },
    {
      key: "brand_logo",
      title: "Brand Logo Name Used",
    },
    {
      key: "investigate",
      title: "Investigation",
      render: (item: WebsiteData) => (
        <div
          className="flex items-center gap-2 cursor-pointer hover:text-blue-600"
          onClick={() => handleInvestigateClick(item)}
        >
          <FolderSearch className="w-4 h-4" />
          {/* <span className="text-sm">Investigate More</span> */}
        </div>
      ),
    },
  ];

  // Donut chart data for Incidents By Platform
  // Add API call for Incidents By Platform
  const { data: incidentsByPlatformApiData, isLoading: isIncidentsByPlatformLoading } = useQuery(
    ["incidentsByPlatform", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.INCIDENT_BY_PLATFORM,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for platforms
  const incidentsByPlatformColorPalette = [
    "#3B82F6", "#E1306C", "#10B981", "#8B5CF6", "#F59E0B", "#64748B", "#F472B6", "#6366F1", "#FBBF24", "#A78BFA"
  ];

  // Dynamically generate config for Incidents By Platform
  const incidentsByPlatformKeys = React.useMemo(() => {
    const dataArr = incidentsByPlatformApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique labels
    return dataArr.map((row: any) => row.label || row.platform || row.name);
  }, [incidentsByPlatformApiData?.data]);

  const incidentsByPlatformChartConfig = React.useMemo(() => {
    return incidentsByPlatformKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      acc[key] = { label: key, color: incidentsByPlatformColorPalette[idx % incidentsByPlatformColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [incidentsByPlatformKeys]);

  // Map API response to DonutChart expected format (use only percentage for value)
  const incidentsByPlatformChartData = React.useMemo(() => {
    if (!incidentsByPlatformApiData?.data) return [];
    return incidentsByPlatformApiData.data.map((item: any) => ({
      label: item.platform,
      value: item.percentage,
      percentage: item.percentage
    }));
  }, [incidentsByPlatformApiData?.data]);
  const incidentsByPlatformTotal = incidentsByPlatformApiData?.total_counts || incidentsByPlatformChartData.reduce((sum: number, item: any) => sum + (item.value || 0), 0);
  
  // Format total count in K format for center value
  const formattedTotalCount = React.useMemo(() => {
    if (!incidentsByPlatformTotal) return "0";
    if (incidentsByPlatformTotal >= 1000) {
      return `${(incidentsByPlatformTotal / 1000).toFixed(1)}K`;
    }
    return incidentsByPlatformTotal.toString();
  }, [incidentsByPlatformTotal]);

  // Add API call for Incidents By Category
  const { data: incidentsByCategoryApiData, isLoading: isIncidentsByCategoryLoading } = useQuery(
    ["incidentsByCategory", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.INCIDENT_BY_CATEGORY,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for categories
  const incidentsByCategoryColorPalette = [
    "#3B82F6", "#E1306C", "#10B981", "#8B5CF6", "#F59E0B", "#64748B", "#F472B6", "#6366F1", "#FBBF24", "#A78BFA"
  ];

  // Dynamically generate config for Incidents By Category
  const incidentsByCategoryKeys = React.useMemo(() => {
    const dataArr = incidentsByCategoryApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique labels
    return dataArr.map((row: any) => row.label || row.category || row.name);
  }, [incidentsByCategoryApiData?.data]);

  const incidentsByCategoryChartConfig = React.useMemo(() => {
    return incidentsByCategoryKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      acc[key] = { label: key, color: incidentsByCategoryColorPalette[idx % incidentsByCategoryColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [incidentsByCategoryKeys]);

  // Map API response to DonutChart expected format (use only percentage for value)
  const incidentsByCategoryChartData = React.useMemo(() => {
    if (!incidentsByCategoryApiData?.data) return [];
    return incidentsByCategoryApiData.data.map((item: any, index: number) => ({
      label: item.platform,
      value: item.percentage,
      percentage: item.percentage,
      fill: incidentsByCategoryColorPalette[index % incidentsByCategoryColorPalette.length]
    }));
  }, [incidentsByCategoryApiData?.data]);
  const incidentsByCategoryTotal = incidentsByCategoryApiData?.total_counts || incidentsByCategoryChartData.reduce((sum: number, item: any) => sum + (item.value || 0), 0);

  // Format total count in K format for category center value
  const formattedCategoryTotalCount = React.useMemo(() => {
    if (!incidentsByCategoryTotal) return "0";
    if (incidentsByCategoryTotal >= 1000) {
      return `${(incidentsByCategoryTotal / 1000).toFixed(1)}K`;
    }
    return incidentsByCategoryTotal.toString();
  }, [incidentsByCategoryTotal]);

  // Add API call for Category Takedown %
  const { data: categoryTakedownApiData, isLoading: isCategoryTakedownLoading } = useQuery(
    ["categoryTakedown", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.CATEGORY_TAKEDOWN,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for category takedown
  const categoryTakedownColorPalette = [
    "#E1306C", "#DC2626", "#F59E0B", "#8B5CF6", "#EF4444", "#10B981", "#3B82F6", "#FF0000", "#06B6D4", "#84CC16"
  ];

  // Dynamically generate config for Category Takedown %
  const categoryTakedownKeys = React.useMemo(() => {
    const dataArr = categoryTakedownApiData?.data || [];
    if (!dataArr.length) return [];
    // Use category as the only key for labels; fallback to platform for legacy records
    return dataArr.map((row: any) => (row.category ?? row.platform ?? "Others"));
  }, [categoryTakedownApiData?.data]);

  const categoryTakedownChartConfig = React.useMemo(() => {
    return categoryTakedownKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      const safeKey = key ?? "Others";
      acc[safeKey] = { label: safeKey, color: categoryTakedownColorPalette[idx % categoryTakedownColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [categoryTakedownKeys]);

  // Map API response to DonutChart expected format (use only percentage for value)
  const categoryTakedownChartData = React.useMemo(() => {
    if (!categoryTakedownApiData?.data) return [];
    return categoryTakedownApiData.data.map((item: any, index: number) => ({
      label: item.category ?? item.platform ?? "Others",
      value: item.percentage,
      percentage: item.percentage,
      fill: categoryTakedownColorPalette[index % categoryTakedownColorPalette.length]
    }));
  }, [categoryTakedownApiData?.data]);

  // Calculate total percentage for center value
  const categoryTakedownTotal = React.useMemo(() => {
    if (!categoryTakedownChartData.length) return 0;
    return categoryTakedownChartData.reduce((sum: number, item: any) => sum + (item.value || 0), 0);
  }, [categoryTakedownChartData]);

  // Format total count in K format for center value
  const formattedCategoryTakedownTotal = React.useMemo(() => {
    const totalCount = categoryTakedownApiData?.total_counts || 0;
    if (!totalCount) return "0";
    if (totalCount >= 1000) {
      return `${(totalCount / 1000).toFixed(1)}K`;
    }
    return totalCount.toString();
  }, [categoryTakedownApiData?.total_counts]);

  // Add API call for Pending Incidents
  const { data: pendingIncidentsApiData, isLoading: isPendingIncidentsLoading } = useQuery(
    ["pendingIncidents", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.PENDING_INCIDENT,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for pending incidents
  const pendingIncidentsColorPalette = [
    "#3B82F6", "#F59E0B", "#10B981", "#EF4444", "#8B5CF6", "#F472B6", "#6366F1", "#FBBF24", "#A78BFA", "#06B6D4"
  ];

  // Dynamically generate config for Pending Incidents
  const pendingIncidentsKeys = React.useMemo(() => {
    const dataArr = pendingIncidentsApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'month'
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "month") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [pendingIncidentsApiData?.data]);

  const pendingIncidentsChartConfig = React.useMemo(() => {
    return pendingIncidentsKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      acc[key] = { label: key, color: pendingIncidentsColorPalette[idx % pendingIncidentsColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [pendingIncidentsKeys]);

  // Add API call for Suspicious Offer Categories
  const { data: suspiciousOffersApiData, isLoading: isSuspiciousOffersLoading } = useQuery(
    ["suspiciousOffers", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.SUSPICIOUS_OFFER_CATEGORY,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for suspicious offers
  const suspiciousOffersColorPalette = [
    "#F59E0B", "#FDBA74", "#FDE68A", "#FBBF24", "#FCD34D", "#FEF3C7", "#FFEDD5", "#FED7AA", "#FECACA", "#FEE2E2"
  ];

  // Dynamically generate config for Suspicious Offer Categories
  const suspiciousOffersKeys = React.useMemo(() => {
    const dataArr = suspiciousOffersApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'month'
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "month") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [suspiciousOffersApiData?.data]);

  const suspiciousOffersChartConfig = React.useMemo(() => {
    return suspiciousOffersKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      acc[key] = { label: key, color: suspiciousOffersColorPalette[idx % suspiciousOffersColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [suspiciousOffersKeys]);

  // Add API call for Customer Care Trend
  const { data: customerCareApiData, isLoading: isCustomerCareLoading } = useQuery(
    ["customerCare", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.CUSTOMER_CARE_TREND,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for customer care
  const customerCareColorPalette = [
    "#274745", "#E8C468", "#147878", "#14B8A6", "#F59E0B", "#FDE68A", "#6366F1", "#F472B6", "#10B981", "#F87171"
  ];

  // Dynamically generate config for Customer Care Trend
  const customerCareKeys = React.useMemo(() => {
    const dataArr = customerCareApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'month'
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "month") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [customerCareApiData?.data]);

  const customerCareChartConfig = React.useMemo(() => {
    return customerCareKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      acc[key] = { label: key, color: customerCareColorPalette[idx % customerCareColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [customerCareKeys]);

  // Map API data to chart format - convert 'month' to 'label' for DynamicBarChart
  const customerCareChartData = React.useMemo(() => {
    if (!customerCareApiData?.data) return [];
    return customerCareApiData.data.map((item: any) => ({ ...item, label: item.month }));
  }, [customerCareApiData?.data]);

  // Add API call for Contact No
  const { data: contactNoApiData, isLoading: isContactNoLoading } = useQuery(
    ["contactNo", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.CONTACT_NUMBER,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to generate a color palette for contact no
  const contactNoColorPalette = [
    "#10B981", "#EF4444", "#3B82F6", "#F59E0B", "#8B5CF6", "#F472B6", "#6366F1", "#FBBF24", "#A78BFA", "#06B6D4"
  ];

  // Dynamically generate config for Contact No
  const contactNoKeys = React.useMemo(() => {
    const dataArr = contactNoApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'date'
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "date") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [contactNoApiData?.data]);

  const contactNoChartConfig = React.useMemo(() => {
    return contactNoKeys.reduce((acc: Record<string, { label: string; color: string }>, key: string, idx: number) => {
      acc[key] = { label: key, color: contactNoColorPalette[idx % contactNoColorPalette.length] };
      return acc;
    }, {} as Record<string, { label: string; color: string }>);
  }, [contactNoKeys]);

  // Calculate totals for stats display
  const contactNoStats = React.useMemo(() => {
    if (!contactNoApiData?.data || contactNoApiData.data.length === 0) {
      return { totalUnique: 0, totalIncidents: 0, incidentPercentage: 0 };
    }
    
    const totalUnique = contactNoApiData.data.reduce((sum: number, item: any) => sum + (item.Unique || 0), 0);
    const totalIncidents = contactNoApiData.data.reduce((sum: number, item: any) => sum + (item.Incidents || 0), 0);
    const incidentPercentage = totalUnique > 0 ? (totalIncidents / totalUnique) * 100 : 0;
    
    return { totalUnique, totalIncidents, incidentPercentage };
  }, [contactNoApiData?.data]);

  // Add API call for Top Platform Trend (Facebook, Telegram, X, Instagram)
  const { data: platformTrendsApiData, isLoading: isPlatformTrendsLoading } = useQuery(
    ["platformTrends", queryParams],
    async () => {
      const idToken = localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_ANALYTICS.PLATFORM_TRENDS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Helper to extract data for each platform
  const getPlatformTrendData = (platformName: string) => {
    if (!platformTrendsApiData?.platformTrends) return [];
    const platformObj = platformTrendsApiData.platformTrends.find((p: any) => p.platform === platformName);
    if (!platformObj) return [];
    // Map API response fields to chart expected format
    return platformObj.data.map((item: any) => ({
      label: item.label,
      incidents: item.incident, // Map 'incident' from API to 'incidents' for chart
      unique: item.unique
    })) || [];
  };

  const facebookTrendData = React.useMemo(() => getPlatformTrendData("Facebook"), [platformTrendsApiData]);
  const telegramTrendData = React.useMemo(() => getPlatformTrendData("Telegram"), [platformTrendsApiData]);
  const xTrendData = React.useMemo(() => getPlatformTrendData("X"), [platformTrendsApiData]);
  const instagramTrendData = React.useMemo(() => getPlatformTrendData("Instagram"), [platformTrendsApiData]);

  // Chart config for all platforms (incidents and unique)
  const platformTrendChartConfig = {
    incidents: { label: "Incidents", color: "#3B82F6" },
    unique: { label: "Unique", color: "#60A5FA" },
  };

  return (
    <div className="min-h-screen w-full bg-[#F3F4F6]">
      {/* Filter Bar */}
      <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)]">
        {/* <div className="text-lg font-semibold text-gray-800">Social Media Analytics</div> */}
        <div className="flex flex-wrap gap-3">
        <FilterPill
          id="brand"
          title="Brand"
          filters={brandFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={!brandFilterData}
          isSearchable={true}
        />
        <FilterPill
          id="priority"
          title="Priority"
          filters={priorityFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={!priorityFilterData}
        />
        <FilterPill
          id="country"
          title="Country"
          filters={countryFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={!countryFilterData}
          isSearchable={true}
        />
      </div>
      </div>

      {/* First Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2">
        {/* Geographical Map Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[3] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title="Incidents By Platform"
            onExpand={handleGeographicalMapExpand}
            handleExport={() => handleCSVDownload("incident_platform", "Incidents By Platform")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent>
            <div className="overflow-hidden h-[250px] flex items-center justify-center">
              {isIncidentsByPlatformLoading ? (
                <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
              ) : !incidentsByPlatformApiData?.data || incidentsByPlatformApiData.data.length === 0 ? (
                <span className="text-sm dark:text-white">No Data Found !</span>
              ) : (
              <DonutChart
                  chartData={incidentsByPlatformChartData}
                  chartConfig={incidentsByPlatformChartConfig}
                dataKey="value"
                nameKey="label"
                isView={true}
                isdonut={true}
                isPercentage={true}
                onExpand={handleGeographicalMapExpand}
                isExpanded={expandedCard === 3}
                innerRadius={65}
                outerRadius={100}
                isSelect={false}
                isRadioButton={false}
                  centerValue={formattedTotalCount}
                centerLabel="Incidents"
                showHeaderRow={false}
                  formatterType="percentage"
                  disableLegendClick={true}
              />
              )}
            </div>
          </CardContent>
        </Card>
 

        {/* Pie Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[4] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title="Incidents By Category"
            onExpand={handlePieChartExpand}
            handleExport={() => handleCSVDownload("incident_category", "Incidents By Category")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent>
            <div className="w-full overflow-hidden h-[250px]">
              <DonutChart
                chartData={incidentsByCategoryChartData}
                chartConfig={incidentsByCategoryChartConfig}
                dataKey="value"
                nameKey="label"
                isView={true}
                isdonut={true}
                isPercentage={true}
                onExpand={handlePieChartExpand}
                innerRadius={65}
                outerRadius={100}
                isSelect={false}
                isRadioButton={false}
                centerValue={formattedCategoryTotalCount}
                centerLabel="Incidents"
                showHeaderRow={false}
                formatterType="percentage"
                isLoading={isIncidentsByCategoryLoading}
                disableLegendClick={true}

              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2">
        {/* Top Domain Providers Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[5] = el;
          }}
          className="shadow-md h-[350px]"
        >
          <ProgressBarChart1
            chartData={productSalesData}
            title="Major Platforms"
            onExpand={handleTopDomainProvidersExpand}
            handleExport={() => handleCSVDownload("major_platforms", "Major Platforms")}
              isLoading={isMajorPlatformsLoading}
            incidentsBarColor="#3B82F6" // blue
            takedownBarColor="#10B981" // green
            incidentsLegendColor="#3B82F6"
            takedownLegendColor="#10B981"
            tooltipPosition="bottom"
            stickyLegend={true}
          />
        </Card>

        {/* Top Domain Provider Table */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[6] = el;
          }}
          className={`shadow-md ${expandedCard === 6 ? 'h-[100vh]' : 'h-[350px]'}`}
        >
          <HeaderRow
            title="Pending Incidents"
            onExpand={handleWebsitesTableExpand}
            handleExport={() => handleCSVDownload("pending_incidents", "Pending Incidents")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
            isExpanded={expandedCard === 6}
          />
          <CardContent className={`flex flex-col ${expandedCard === 6 ? 'h-[calc(100vh-48px)]' : 'h-[300px]'}`}>
            <div className={`flex-1 flex flex-col overflow-hidden ${expandedCard === 6 ? 'h-[calc(100vh-120px)]' : ''}`}>
              <StackedBarWithLine
                chartData={pendingIncidentsApiData?.data || []}
                chartConfig={pendingIncidentsChartConfig}
                onExpand={handleWebsitesTableExpand}
                isLoading={isPendingIncidentsLoading}
                isStacked={true}
                xAxisConfig={{ dataKey: "month", tickLine: false, tickMargin: 5, axisLine: true, angle: 0, textAnchor: "middle", dy: 5, dx: 0, height: 35 }}
                YAxis1={{
                  yAxisId: "left",
                  orientation: "left",
                  tickFormatter: (value) => value
                }}
                barWidth={30}
                isLegend={true}
                chartHeight={expandedCard === 6 ? window.innerHeight - 120 : 275}
                legendMarginTop={1}
                legendHeight={30}
                legendFontSize={12}
                isExpanded={expandedCard === 6}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Platform Trend Section */}
      <Card className="shadow-md m-2 ">
        <CardHeader className="p-3">
          
           <div className="w-full bg-gray-200 text-base rounded-md font-bold grid justify-items-center p-3">
           Top Platform Trend
        </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {/* Facebook Card */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[11] = el;
          }}
              className="shadow-sm border"
        >
              <CardHeader className="p-3">
            <HeaderRow
              title="Facebook"
              onExpand={() => handleExpandBase(11)}
              handleExport={() => handleCSVDownload("top_platform_trend", "Facebook")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
          </CardHeader>
              <CardContent className="w-full min-h-[250px] overflow-hidden">
            <div className="w-full h-full">
              <DynamicBarChart
                data={facebookTrendData}
                config={PlatformChartConfig}
                isHorizontal={true}
                onExpand={() => handleExpandBase(11)}
                position="right"
                isLoading={isPlatformTrendsLoading}
                formatterType="number"
                isRadioButton={false}
                isSelect={false}
                selectoptions={[]}
                showHeader={false}
                isScrollable={false}
                barSize={20}
                yAxisTextGap={-15}
              />
            </div>
          </CardContent>
        </Card>

            {/* Telegram Card */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[2] = el;
          }}
              className="shadow-sm border"
        >
              <CardHeader className="p-3">
            <HeaderRow
                  title="Telegram"
              onExpand={() => handleExpandBase(2)}
              handleExport={() => handleCSVDownload("top_platform_trend", "Telegram")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
          </CardHeader>
              <CardContent className="w-full min-h-[250px] overflow-hidden">
                <div className="w-full h-full">
                  <DynamicBarChart
                    data={telegramTrendData}
                    config={TelegramChartConfig}
                    isHorizontal={true}
                    onExpand={() => handleExpandBase(2)}
                    position="right"
                    isLoading={isPlatformTrendsLoading}
                    formatterType="number"
                    isRadioButton={false}
                    isSelect={false}
                    selectoptions={[]}
                    showHeader={false}
                    isScrollable={false}
                    barSize={20}
                    yAxisTextGap={-15}
                  />
                </div>
              </CardContent>
            </Card>

            {/* X Card */}
            <Card
              ref={(el) => {
                if (el) cardRefs.current[12] = el;
              }}
              className="shadow-sm border"
            >
              <CardHeader className="p-3">
                <HeaderRow
                  title="X"
                  onExpand={() => handleExpandBase(12)}
                  handleExport={() => handleCSVDownload("top_platform_trend", "X")}
                  isRadioButton={false}
                  isSelect={false}
                  titleFontSize="text-base"
                />
              </CardHeader>
              <CardContent className="w-full min-h-[250px] overflow-hidden">
                <div className="w-full h-full">
                  <DynamicBarChart
                    data={xTrendData}
                    config={XChartConfig}
                    isHorizontal={true}
                    onExpand={() => handleExpandBase(12)}
                    position="right"
                    isLoading={isPlatformTrendsLoading}
                    formatterType="number"
                    isRadioButton={false}
                    isSelect={false}
                    selectoptions={[]}
                    showHeader={false}
                    isScrollable={false}
                    barSize={20}
                    yAxisTextGap={-15}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Instagram Card */}
            <Card
              ref={(el) => {
                if (el) cardRefs.current[13] = el;
              }}
              className="shadow-sm border"
            >
              <CardHeader className="p-3">
                <HeaderRow
                  title="Instagram"
                  onExpand={() => handleExpandBase(13)}
                  handleExport={() => handleCSVDownload("top_platform_trend", "Instagram")}
                  isRadioButton={false}
                  isSelect={false}
                  titleFontSize="text-base"
                />
              </CardHeader>
              <CardContent className="w-full min-h-[250px] overflow-hidden">
                <div className="w-full h-full">
                  <DynamicBarChart
                    data={instagramTrendData}
                    config={InstagramChartConfig}
                    isHorizontal={true}
                    onExpand={() => handleExpandBase(13)}
                    position="right"
                    isLoading={isPlatformTrendsLoading}
                    formatterType="number"
                    isRadioButton={false}
                    isSelect={false}
                    selectoptions={[]}
                    showHeader={false}
                    isScrollable={false}
                    barSize={20}
                    yAxisTextGap={-15}
                  
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Category Takedown % Card */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2">
        <Card
          ref={(el) => {
            if (el) cardRefs.current[15] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title="Category Takedown %"
            onExpand={handleSample1Expand}
            handleExport={() => handleCSVDownload("category_takedown", "Category Takedown")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent>
            <div className="overflow-hidden h-[250px]">
              <DonutChart
                chartData={categoryTakedownChartData}
                chartConfig={categoryTakedownChartConfig}
                dataKey="value"
                nameKey="label"
                isView={true}
                isdonut={true}
                isPercentage={true}
                onExpand={handleSample1Expand}
                innerRadius={65}
                outerRadius={100}
                isSelect={false}
                isRadioButton={false}
                centerValue={formattedCategoryTakedownTotal}
                centerLabel="Take Down"
                showHeaderRow={false}
                formatterType="percentage"
                isLoading={isCategoryTakedownLoading}
                disableLegendClick={true}

              />
            </div>
          </CardContent>
        </Card>

        {/* Suspicious Offer Categories Card */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[16] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title="Suspicious Offer Categories"
            onExpand={handleSuspiciousOffersExpand}
            handleExport={() => handleCSVDownload("suspicious_offer_categories", "Suspicious Offer Categories")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent className="h-[350px] flex flex-col">
            <div className="flex-1 flex flex-col overflow-hidden">
              <StackedBarWithLine
                chartData={suspiciousOffersApiData?.data || []}
                chartConfig={suspiciousOffersChartConfig}
                onExpand={handleSuspiciousOffersExpand}
                isLoading={isSuspiciousOffersLoading}
                isStacked={true}
                xAxisConfig={{ dataKey: "month", tickLine: false, tickMargin: 5, axisLine: true, angle: 0, textAnchor: "middle", dy: 5, dx: 0, height: 35 }}
                YAxis1={{
                  yAxisId: "left",
                  orientation: "left",
                  tickFormatter: (value) => value
                }}
                barWidth={30}
                isLegend={true}
                chartHeight={300}
                legendMarginTop={1}
                legendHeight={30}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Care Trend Card */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2">
        <Card
          ref={(el) => {
            if (el) cardRefs.current[17] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title="Customer Care Trend"
            onExpand={handleCustomerCareExpand}
            handleExport={() => handleCSVDownload("customer_care_trend", "Customer Care Trend")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent className="h-[300px]">
            <div className="h-full w-full">
              <DynamicBarChart
                data={customerCareChartData}
                config={customerCareChartConfig}
                isHorizontal={false}
                onExpand={handleCustomerCareExpand}
                isRadioButton={false}
                isSelect={false}
                isLoading={isCustomerCareLoading}
                formatterType="number"
                position="top"
                showHeader={false}
                isScrollable={false}
                barSize={20}
               
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact No Card */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[18] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title="Contact No"
            onExpand={handleContactNoExpand}
            handleExport={() => handleCSVDownload("contact_no", "Contact No")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent>
            {/* Stats text aligned with header */}
            <div className="flex items-center gap-8 mb-4 px-2">
              <div className="text-xs">
                <span style={{ color: '#10B981', fontWeight: 'bold' }}>Unique Count</span>: <span style={{ color: '#10B981', fontWeight: 'bold' }}>{contactNoApiData?.Unique_Count?.toLocaleString?.() ?? '--'}</span>
              </div>
              <div className="text-xs">
                <span style={{ color: '#EF4444', fontWeight: 'bold' }}>Incident Reported</span>: <span style={{ color: '#EF4444', fontWeight: 'bold' }}>{contactNoApiData?.Incident_Reported !== undefined ? `${contactNoApiData.Incident_Reported}` : '--'}</span>
              </div>
            </div>
            
            {/* Chart with horizontal scroll */}
            <div className="w-full h-[200px] overflow-x-auto overflow-y-hidden">
              <div className="min-w-max">
              {isContactNoLoading ? (
                  <div className="h-full flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
              ) : !contactNoApiData?.data || contactNoApiData.data.length === 0 ? (
                  <div className="h-full flex items-center justify-center">
                <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <ResponsiveContainer width={Math.max(600, contactNoApiData.data.length * 80)} height={180}>
                <AreaChart
                    data={contactNoApiData?.data || []}
                      margin={{ top: 15, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                      {contactNoKeys.map((key, index) => (
                    <linearGradient
                          key={key}
                          id={`color${key}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                          <stop offset="5%" stopColor={contactNoColorPalette[index % contactNoColorPalette.length]} stopOpacity={0.8} />
                          <stop offset="95%" stopColor={contactNoColorPalette[index % contactNoColorPalette.length]} stopOpacity={0} />
                    </linearGradient>
                      ))}
                  </defs>

                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => {
                      try {
                        const date = new Date(value);
                        return format(date, "yyyy-MM-dd");
                      } catch {
                        return value;
                      }
                    }}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => formatNumber(value)}
                  />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (!active || !payload?.length) return null;
                      
                      return (
                        <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                          <div className="font-medium bg-gray-100 dark:text-black dark:text-white">
                            {(() => {
                              try {
                                const date = new Date(label);
                                return format(date, "dd MMM yyyy");
                              } catch {
                                return label;
                              }
                            })()}
                          </div>
                          <div className="grid gap-1.5">
                            {payload.map((item, index) => {
                                const color = contactNoColorPalette[index % contactNoColorPalette.length];
                              
                              return (
                                <div
                                  key={item.dataKey}
                                  className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                                >
                                  <div
                                    className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                    style={
                                      {
                                          "--color-bg": color,
                                          "--color-border": color,
                                      } as React.CSSProperties
                                    }
                                  />
                                  <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                    <span className="text-muted-foreground">
                                        {item.name}
                                    </span>
                                    <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                      {typeof item.value === 'number' ? formatNumber(item.value) : item.value}
                                    </span>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    }}
                  />
                    {contactNoKeys.map((key, index) => (
                  <Area
                        key={key}
                    type="linear"
                        dataKey={key}
                        stroke={contactNoColorPalette[index % contactNoColorPalette.length]}
                    fillOpacity={1}
                        fill={`url(#color${key})`}
                    strokeWidth={2}
                  />
                    ))}
                </AreaChart>
              </ResponsiveContainer>
              )}
              </div>
            </div>
            
            {/* Legends below the scroll bar */}
            <div className="mt-3 pt-3">
              <div className="flex space-x-4 justify-center">
                {contactNoKeys.map((key, index) => (
                  <div key={key} className="flex items-center space-x-2">
                    <span 
                      style={{ backgroundColor: contactNoColorPalette[index % contactNoColorPalette.length] }} 
                      className="w-3 h-3 rounded-full"
                    ></span>
                    <span className="text-sm font-medium">{key}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Analytics;



