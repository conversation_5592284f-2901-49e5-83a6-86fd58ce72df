import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { Dispatch, SetStateAction } from 'react';


export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const session = {
  set: (k: string, d: object) => {
    const b = Buffer.from(JSON.stringify(d));
    localStorage.setItem(k, b.toString("base64"));
  },
  get: (k: string) => {
    const b = localStorage.getItem(k);
    if (!b) return {};
    return JSON.parse(Buffer.from(b ?? "", "base64").toString() ?? "{}");
  },
};

export function downloadURI(uri: string, name: string) {
  const link = document.createElement("a");
  link.download = name;
  link.href = uri;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export const getXAxisAngle = (data: string[]): number => {
  if (data.length === 0) {
    return 0; // or handle this case however you prefer
  }
  return data.length > 10 ? -28 : 0;
};

export const formatNumber = (value: number): string => {
  if (value >= 1e9) {
    return (value / 1e9).toFixed(1) + 'B';
  }
  if (value >= 1e6) {
    return (value / 1e6).toFixed(1) + 'M';
  }
  if (value >= 1e3) {
    return (value / 1e3) + 'K';
  }
  return value.toString();
};

// export function onExpand(
//   index: number,
//   cardRefs: React.MutableRefObject<(HTMLElement | null)[]>,
//   expandedCard: number | null,
//   setExpandedCard: (value: number | null) => void
// ) {
//   if (expandedCard === index) {
//     console.log("Collapsing the card");
//     setExpandedCard(null);
//   } else {
//     console.log("Expanding the card to index:", index);
//     setExpandedCard(index);
//   }
// }
export const onExpand = (
  index: number,
  cardRefs: React.MutableRefObject<HTMLElement[]>,
  expandedCard: number | null,
  setExpandedCard: Dispatch<SetStateAction<number | null>>
) => {
  const card = cardRefs.current[index];
  
  if (card) {
    if (!document.fullscreenElement) {
      card.requestFullscreen().catch((err) => {
        console.error('Error attempting to enable fullscreen mode:', err);
      });
    } else {
      document.exitFullscreen().catch((err) => {
        console.error('Error attempting to exit fullscreen mode:', err);
      });
    }

    setExpandedCard(expandedCard === index ? null : index);
  }
};


export const handleExportData = (
  headers: string[],
  rows: string[][],
  fileName: string
) => {
  const csvContent = [
    headers.join(','),
    ...rows.map((row) => row.join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

export const formatValue = (value: number, labelType: string) => {
  if (labelType === "Percentage") {
    return `${Math.round(value * 100 / 1000) * 10}%`; 
  }
  return value.toLocaleString(); 
};

 export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(null, args);
    }, delay);
  };
};

export const parsePercentage = (percentage: string | number): number => {
  // Check if it's a string and has the percentage sign
  if (typeof percentage === 'string') {
    return parseFloat(percentage.replace('%', '').trim());
  }
  // If it's already a number, return it as is
  if (typeof percentage === 'number') {
    return percentage;
  }
  // If it's not a valid type, return NaN or handle as needed
  return NaN;
};

export const handleCSVDownloadFromResponse = async (
  response: any,
  fileName: string
) => {
  try {
    // Check if response contains a download URL
    if (response.data && response.data.url) {
      // Use the provided download URL
      const downloadUrl = response.data.url;
      console.log("Download link received:", downloadUrl);
      
      // Create a download link
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', fileName);
      link.style.display = 'none';
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup after a short delay to ensure download starts
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);
      
      console.log("Download completed successfully");
      return true;
    } else {
      // Fallback to blob download if no URL provided
      console.log("No download link in response, using blob download");
      
      // Create a download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      link.style.display = 'none';
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup after a short delay to ensure download starts
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
      
      console.log("Download completed successfully");
      return true;
    }
  } catch (error) {
    console.error("Download failed:", error);
    return false;
  }
};

