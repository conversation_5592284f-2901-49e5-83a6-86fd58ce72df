'use client'
import React, { useState, useEffect, useMemo } from "react";
import { FilterPill } from "@/components/mf/Filters";
import type { FilterState } from "@/components/mf/Filters/FilterPill";
import { Input } from "@/components/ui/input";
import {  FileUp, FileDown, Link, Send, Camera, FileText, ChevronDown, Loader2, X, CheckCircle, XCircle } from "lucide-react";
import ResizableTable from "@/components/mf/RulesTableComponent";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { useQuery, useQueryClient } from 'react-query';
import axios, { AxiosRequestConfig } from 'axios';
import Endpoint from '@/common/endpoint';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface FilterResponse {
  data: string[];
  status: boolean;
}

interface UserResponse {
  result: string[];
  status: string;
  message: string;
}

interface Filter {
  label: string;
  checked: boolean;
}

interface SelectedFilters {
  priority: string[];
  category: string[];
  platform: string[];
}

interface IncidentData {
  id: string | number;
  date: string;
  brand: string;
  platform: string; // This field contains icon URLs
  category: string;
  description: string;
  handle_details: string;
  likes: number | null;
  followers: string | number;
  url: string | string[] | Array<{
    source_url?: string;
    destination_url?: string;
    screenshot?: string;
    case_reports?: string;
  }>; // Can be single URL, array of URLs, or array of URL objects
  priority?: string;
  "Contact No"?: string; // API response key
  "Priority & Trademark Used"?: string; // API response key
  "Handle Name"?: string; // API response key
  contact_no?: string; // Fallback key
  handle_name?: string; // Fallback key
}

interface ViewIncidentsResponse {
  data: IncidentData[];
  total?: number;
  total_pages?: number;
  success?: boolean;
}

export default function IncidentsPage() {
  const { selectedPackage } = usePackage();
  const { startDate: fromDate, endDate: toDate } = useDateRange();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedItems, setSelectedItems] = useState<IncidentData[]>([]);
  const [tableData, setTableData] = useState<IncidentData[]>([]);
  const [filteredData, setFilteredData] = useState<IncidentData[]>([]);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [tableKey, setTableKey] = useState<number>(0);
  const [forceUpdate, setForceUpdate] = useState<number>(0);
  const [recordLimit, setRecordLimit] = useState<number>(50);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  
  // Search states for dropdowns
  const [assigneeSearch, setAssigneeSearch] = useState<string>('');
  
  // Dialog state for Raise Ticket
  const [isRaiseTicketDialogOpen, setIsRaiseTicketDialogOpen] = useState(false);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [ticketForm, setTicketForm] = useState({
    brand: '',
    estimatedTime: '',
    priority: '',
    status: '',
    assignee: '',
    description: '',
    subject: '',
    watcherList: [] as string[]
  });
  const [isSubmittingTicket, setIsSubmittingTicket] = useState(false);
  const [isUploadingFile, setIsUploadingFile] = useState(false);
  
  // Filter states for UI selection
  const [selectedPriority, setSelectedPriority] = useState<string[]>(['High']); // Initially selected to High
  const [selectedCategory, setSelectedCategory] = useState<string[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>(['Active']); // Initially selected to Active
  
  // Track which specific filters have been actively changed by user
  const [activeFilters, setActiveFilters] = useState({
    priority: true, // Set to true since we have initial selection
    category: false,
    platform: false,
    status: true // Set to true since we have initial selection
  });

  // Debounced query params to prevent rapid API calls
  const [debouncedFilters, setDebouncedFilters] = useState({
    priority: ['High'], // Initially set to High
    category: selectedCategory,
    platform: selectedPlatform,
    status: ['Active'] // Initially set to Active
  });

  // Debounce filter changes to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters({
        priority: selectedPriority,
        category: selectedCategory,
        platform: selectedPlatform,
        status: selectedStatus
      });
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [selectedPriority, selectedCategory, selectedPlatform, selectedStatus]);



  // Helper function to transform filter data with current selections
  const transformFiltersData = (response: FilterResponse | undefined, selectedValues: string[]): Filter[] => {
    if (!response || !response.data) return [];

    const transformedFilters = response.data.map((item) => ({
      label: item,
      checked: selectedValues.includes(item) || selectedValues.length === 0 // Check if selected or if no selections (show all)
    }));

    return transformedFilters;
  };

  // Query parameters for API calls
  const queryParams = useMemo(() => {
    const baseParams = {
      package_name: selectedPackage,
      fromDate: fromDate,
      toDate: toDate,
      page_number: pageNumber,
      record_limit: recordLimit
    };

    // Always include filter fields - use defaults when not actively changed, user selections when changed
    const payload: any = { ...baseParams };

    // Priority: use user selection if actively changed, otherwise use "all"
    if (activeFilters.priority && debouncedFilters.priority.length > 0) {
      payload.priority = debouncedFilters.priority;
    } else {
      payload.priority = ["all"];
    }

    // Category: use user selection if actively changed, otherwise use "all"
    if (activeFilters.category && debouncedFilters.category.length > 0) {
      payload.category = debouncedFilters.category;
    } else {
      payload.category = ["all"];
    }

    // Platform: use user selection if actively changed, otherwise use "all"
    if (activeFilters.platform && debouncedFilters.platform.length > 0) {
      payload.platform = debouncedFilters.platform;
    } else {
      payload.platform = ["all"];
    }

    // Status: use user selection if actively changed, otherwise use "all"
    if (activeFilters.status && debouncedFilters.status.length > 0) {
      payload.status = debouncedFilters.status;
    } else {
      payload.status = ["all"];
    }

    // console.log('Query Params for Incidents API:', payload);
    // console.log('Current Page Number:', pageNumber);
    return payload;
  }, [selectedPackage, fromDate, toDate, debouncedFilters, activeFilters, pageNumber, recordLimit]);



  const { data: priorityFilterData } = useQuery<FilterResponse>({
    queryKey: ['priorityFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'priority'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false
  });

  const { data: categoryFilterData } = useQuery<FilterResponse>({
    queryKey: ['categoryFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'category'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false
  });

  const { data: platformFilterData } = useQuery<FilterResponse>({
    queryKey: ['platformFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'sub_channel_name'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false
  });

  const { data: statusFilterData } = useQuery<FilterResponse>({
    queryKey: ['statusFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'status'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false
  });

  // API call for brand filter data
  const { data: brandFilterData, isLoading: brandFilterLoading } = useQuery<FilterResponse>({
    queryKey: ['brandFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'brand'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    retry: false
  });

  // API call for users data
  const { data: usersData, isLoading: usersLoading, error: usersError } = useQuery<UserResponse>({
    queryKey: ['users', selectedPackage],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        'https://dev-bi-apis.mfilterit.net/api/v1/360_dashboard/website_app/incidents/get_assignee',
        {
          package_name: selectedPackage
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!selectedPackage,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  });

  // // Debug users data
  // useEffect(() => {
  //   if (usersData) {
  //     // console.log('Users Data loaded:', usersData.result?.length || 0, 'users');
  //   }
  // }, [usersData]);

  // Filter assignee users based on search
  const filteredAssigneeUsers = useMemo(() => {
    if (!usersData?.result) return [];
    
    const filtered = usersData.result
      .filter(email => email && typeof email === 'string')
      .filter(email => {
        if (!assigneeSearch.trim()) return true;
        const searchTerm = assigneeSearch.toLowerCase();
        const emailLower = email.toLowerCase();
        return emailLower.includes(searchTerm);
      });
    
    // console.log('Search term:', assigneeSearch);
    // console.log('Total users:', usersData.result.length);
    // console.log('Filtered users:', filtered.length);
    // console.log('Filtered results:', filtered.slice(0, 3));
    
    return filtered;
  }, [usersData?.result, assigneeSearch]);

  // Function to refetch incidents data after actions
  const refetchIncidentsData = () => {
    // Invalidate and refetch the incidents query to get latest data
    queryClient.invalidateQueries(['viewIncidents', queryParams]);
  };

  // API call for incidents data with filter parameters
  const { data: incidentsResponse, isLoading: incidentsLoading, error: incidentsError } = useQuery<ViewIncidentsResponse>(
    ['viewIncidents', queryParams],
    async () => {
      // console.log('API call triggered with queryParams:', queryParams);
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.SM_VIEW_INCIDENTS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      // console.log('API response received:', response.data);
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Update table data when API response changes
  useEffect(() => {
    if (incidentsResponse?.data) {
      // console.log('Incidents API Response:', incidentsResponse.data);
      // console.log('Total Pages:', incidentsResponse.total_pages);
      // Log platform field data for debugging
      incidentsResponse.data.forEach((item, index) => {
        console.log(`Platform ${index + 1}:`, {
          platform: item.platform,
          isIconUrl: item.platform && (item.platform.startsWith('http') || item.platform.startsWith('data:') || item.platform.includes('.png') || item.platform.includes('.jpg') || item.platform.includes('.jpeg') || item.platform.includes('.svg') || item.platform.includes('.ico'))
        });
      });
      setTableData(incidentsResponse.data);
      setFilteredData(incidentsResponse.data);
      // Set total pages from API response
      if (incidentsResponse.total_pages) {
        setTotalPages(incidentsResponse.total_pages);
      }
      // Set total records from API response
      if (incidentsResponse.total !== undefined) {
        setTotalRecords(incidentsResponse.total);
      }
    } else {
      // Clear data when no response or empty data
      // console.log('No incidentsResponse.data, clearing data');
      setTableData([]);
      setFilteredData([]);
      // console.log('useEffect - filteredData cleared');
    }
  }, [incidentsResponse, pageNumber]);

 

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    
    if (!file) {
      toast({
        title: "Error",
        description: "Please select a file to upload.",
        className: "border-l-4 border-l-red-500 bg-red-50 border-red-200",
        action: (
          <XCircle className="h-5 w-5 text-red-600" />
        ),
      });
      return;
    }

    // Check file type - allow CSV and Excel files
    const isAllowedFile = file.type === "text/csv" || 
                          file.type === "application/vnd.ms-excel" ||
                          file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                          file.name.toLowerCase().endsWith('.csv') ||
                          file.name.toLowerCase().endsWith('.xlsx') ||
                          file.name.toLowerCase().endsWith('.xls');

    if (!isAllowedFile) {
      toast({
        title: "Error",
        description: "Please upload a CSV or Excel file (.csv, .xlsx, .xls). Selected file: " + file.name,
        className: "border-l-4 border-l-red-500 bg-red-50 border-red-200",
        action: (
          <XCircle className="h-5 w-5 text-red-600" />
        ),
      });
      return;
    }

    setSelectedFile(file);
    
    try {
      setIsUploadingFile(true);
      
      // Step 1: Upload the file and get a link/token
      const uploadResponse = await uploadBulkTakedownFile(file);
      
      // Step 2: Process the bulk takedown using the link/token
      const uploadSuccess = uploadResponse.success || uploadResponse.status === 'success' || uploadResponse.status === 'Success';
      const uploadUrl = uploadResponse.url || uploadResponse.link || uploadResponse.data?.url || uploadResponse.data?.link;
      
      if (uploadSuccess && uploadUrl) {
        const processResponse = await processBulkTakedown(uploadUrl);
        
        const processSuccess = processResponse.success || processResponse.status === 'success' || processResponse.status === 'Success';
        
        if (processSuccess) {
          toast({
            title: "Success",
            description: "Bulk takedown uploaded and processed successfully!",
            className: "border-l-4 border-l-green-500 bg-green-50 border-green-200",
            action: (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ),
          });
          
          // Reset file input for next upload
          const fileInput = document.getElementById('csv-upload') as HTMLInputElement;
          if (fileInput) {
            fileInput.value = '';
          }
          setSelectedFile(null);
          
          // Refetch incidents data to show latest information after bulk takedown
          refetchIncidentsData();
        } else {
          throw new Error(processResponse.message || processResponse.error || "Failed to process bulk takedown");
        }
      } else {
        throw new Error(uploadResponse.message || uploadResponse.error || "Failed to upload file or get URL");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload file. Please try again.",
        className: "border-l-4 border-l-red-500 bg-red-50 border-red-200",
        action: (
          <XCircle className="h-5 w-5 text-red-600" />
        ),
      });
      
      // Reset file input even on error
      const fileInput = document.getElementById('csv-upload') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }
      setSelectedFile(null);
    } finally {
      setIsUploadingFile(false);
    }
  };

  // Step 1: Upload CSV file and get link
  const uploadBulkTakedownFile = async (file: File) => {
    const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
    if (!idToken) throw new Error('No authorization token found');

    const formData = new FormData();
    formData.append('file', file);
    formData.append('package_name', selectedPackage);

    try {
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.UPLOAD_CSV,
        formData,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'multipart/form-data'
          },
          timeout: 30000
        }
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.message || error.message || "Upload failed");
      }
      throw error;
    }
  };

  // Step 2: Raise bulk ticket using the URL
  const processBulkTakedown = async (url: string) => {
    const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
    if (!idToken) throw new Error('No authorization token found');

    const requestBody = {
      url: url
    };

    try {
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.RAISE_TICKET_BULK,
        requestBody,
        {
          headers: {
            'Authorization': idToken,
            'Content-Type': 'application/json',
            // 'Accept': 'application/json',
            // 'Cache-Control': 'no-cache'
          },
          // timeout: 30000
        }
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || 
                           error.response?.data?.error || 
                           error.response?.data?.detail ||
                           error.message || 
                           "Process failed";
        throw new Error(`Server Error (${error.response?.status}): ${errorMessage}`);
      }
      throw error;
    }
  };

  // Handle filter submit - this will update applied filters and trigger API refetch
  const handleFilterSubmit = (id: string, data: FilterState) => {
    // Mark specific filter as actively changed by user
    switch (id) {
      case 'priority':
        setActiveFilters(prev => ({ ...prev, priority: true }));
        setSelectedPriority(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'category':
        setActiveFilters(prev => ({ ...prev, category: true }));
        setSelectedCategory(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'platform':
        setActiveFilters(prev => ({ ...prev, platform: true }));
        setSelectedPlatform(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'status':
        setActiveFilters(prev => ({ ...prev, status: true }));
        setSelectedStatus(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      default:
        break;
    }
  };

  // Table columns configuration - ordered to match API response structure
  const columns = [
    { 
      key: "id", 
      title: "ID",
      render: (item: Record<string, string | number | undefined>) => {
        const id = item.id;
        const priority = item.priority || item["Priority & Trademark Used"] || "";
        
        // Determine background color based on priority
        const getPriorityColor = (priority: string) => {
          const priorityLower = priority.toLowerCase();
          if (priorityLower.includes('high')) {
            return 'bg-red-100 text-red-900 border-red-200 font-bold'; // Red for high priority
          } else if (priorityLower.includes('medium')) {
            return 'bg-blue-100 text-blue-900 border-blue-200 font-bold'; // Blue for medium priority
          } else if (priorityLower.includes('low')) {
            return 'bg-green-100 text-green-900 border-green-200 font-bold'; // Green for low priority
          } else {
            return 'bg-gray-100 text-gray-900 border-gray-200 font-bold'; // Default gray for unknown priority
          }
        };
        
        return (
          <div className={`px-2 py-1 rounded-md border text-sm font-medium ${getPriorityColor(String(priority))}`}>
            {id}
          </div>
        );
      }
    },
    { key: "date", title: "Date" },
    { key: "brand", title: "Brand" },
    { 
      key: "platform", 
      title: "Platform",
      // Enhanced Platform column - treats platform field as icon URL
      // Features:
      // - Shows actual platform icons from platform field (which contains icon URLs)
      // - Lazy loading for better performance
      // - Automatic fallback to default icon with platform name
      // - Error handling for failed image loads
      // - Improved styling with shadows and gradients
      render: (item: Record<string, string | number | undefined>) => {
        const platform = item.platform as string;
        
        // Check if platform field contains a URL (icon link)
        const isIconUrl = platform && (platform.startsWith('http') || platform.startsWith('data:') || platform.includes('.png') || platform.includes('.jpg') || platform.includes('.jpeg') || platform.includes('.svg') || platform.includes('.ico'));
        
        // Function to extract platform name from URL
        const getPlatformName = (url: string): string => {
          if (!url) return 'Unknown Platform';
          
          // Extract platform name from URL patterns
          if (url.includes('X%20Icon.jpg') || url.includes('X Icon.jpg')) {
            return 'X (Twitter)';
          } else if (url.includes('Facebook') || url.includes('facebook')) {
            return 'Facebook';
          } else if (url.includes('Instagram') || url.includes('instagram')) {
            return 'Instagram';
          } else if (url.includes('LinkedIn') || url.includes('linkedin')) {
            return 'LinkedIn';
          } else if (url.includes('YouTube') || url.includes('youtube')) {
            return 'YouTube';
          } else if (url.includes('TikTok') || url.includes('tiktok')) {
            return 'TikTok';
          } else if (url.includes('Telegram') || url.includes('telegram')) {
            return 'Telegram';
          } else if (url.includes('WhatsApp') || url.includes('whatsapp')) {
            return 'WhatsApp';
          } else if (url.includes('Snapchat') || url.includes('snapchat')) {
            return 'Snapchat';
          } else if (url.includes('Discord') || url.includes('discord')) {
            return 'Discord';
          } else if (url.includes('Reddit') || url.includes('reddit')) {
            return 'Reddit';
          } else if (url.includes('Pinterest') || url.includes('pinterest')) {
            return 'Pinterest';
          } else if (url.includes('Twitter') || url.includes('twitter')) {
            return 'Twitter';
          } else {
            // Try to extract from URL path
            try {
              const urlObj = new URL(url);
              const pathParts = urlObj.pathname.split('/');
              const fileName = pathParts[pathParts.length - 1];
              if (fileName) {
                // Remove file extension and decode URL
                const nameWithoutExt = fileName.replace(/\.(jpg|jpeg|png|svg|ico)$/i, '');
                const decodedName = decodeURIComponent(nameWithoutExt);
                return decodedName.replace(/\s*Icon\s*$/i, '').trim() || 'Unknown Platform';
              }
            } catch (e) {
              // If URL parsing fails, try to extract from the string
              const match = platform.match(/([^\/]+)\.(jpg|jpeg|png|svg|ico)$/i);
              if (match) {
                return match[1].replace(/\s*Icon\s*$/i, '').trim() || 'Unknown Platform';
              }
            }
            return 'Unknown Platform';
          }
        };
        
        const platformName = isIconUrl ? getPlatformName(platform) : (platform || 'Unknown Platform');
        
        return (
          <div className="flex items-center justify-center w-full">
            {/* Platform Icon Container - Centered */}
            <div 
              className="w-8 h-8 rounded-full overflow-hidden flex items-center justify-center bg-gray-100 border border-gray-200 shadow-sm cursor-help"
              title={platformName}
            >
              {isIconUrl ? (
                <div className="w-6 h-6 flex items-center justify-center">
                  <img 
                    src={platform} 
                    alt="Platform icon"
                    className="w-6 h-6 object-contain"
                    loading="lazy"
                    onError={(e) => {
                      // Fallback to default icon if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement?.parentElement;
                      if (parent) {
                        const defaultIcon = parent.querySelector('.default-platform-icon');
                        if (defaultIcon) {
                          defaultIcon.classList.remove('hidden');
                        }
                      }
                    }}
                    onLoad={(e) => {
                      // Hide default icon when image loads successfully
                      const target = e.target as HTMLImageElement;
                      const parent = target.parentElement?.parentElement;
                      if (parent) {
                        const defaultIcon = parent.querySelector('.default-platform-icon');
                        if (defaultIcon) {
                          defaultIcon.classList.add('hidden');
                        }
                      }
                    }}
                  />
                </div>
              ) : (
                <div className="w-6 h-6 flex items-center justify-center">
                  <div className="default-platform-icon w-6 h-6 bg-gradient-to-br from-gray-300 to-gray-400 rounded-full flex items-center justify-center shadow-inner">
                    <span className="text-xs font-bold text-gray-600 flex items-center justify-center w-full h-full">
                      {platform ? platform.charAt(0).toUpperCase() : '?'}
                    </span>
                  </div>
                </div>
              )}
            </div>
            {/* Platform Name - show only if it's not an icon URL */}
            {!isIconUrl && (
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 truncate ml-3">
                {/* {platform || 'Unknown'} */}
              </span>
            )}
          </div>
        );
      }
    },
    { key: "category", title: "Category" },
    { 
      key: "description", 
      title: "Description",
      render: (item: Record<string, string | number | undefined>) => {
        const description = item.description as string;
        const truncatedDescription = description.length > 20 ? description.substring(0, 20) + '...' : description;
        return (
          <div
            className="cursor-help"
            title={description}
          >
            {truncatedDescription}
          </div>
        );
      }
    },
    { 
      key: "handle_details", 
      title: "Handle Details",
      render: (item: Record<string, string | number | undefined>) => {
        const handleDetails = item.handle_details as string;
        const truncatedHandleDetails = handleDetails && handleDetails.length > 12 ? handleDetails.substring(0, 12) + '...' : handleDetails;
        return (
          <div
            className="cursor-help"
            title={handleDetails}
          >
            {truncatedHandleDetails || "N/A"}
          </div>
        );
      }
    },
    { 
      key: "Contact No", 
      title: "Contact Number",
      render: (item: Record<string, string | number | undefined>) => {
        const contactNo = item["Contact No"] || item.contact_no;
        return contactNo || "N/A";
      }
    },
    { 
      key: "Priority & Trademark Used", 
      title: "Trademark Used",
      render: (item: Record<string, string | number | undefined>) => {
        const priority = item["Priority & Trademark Used"] || item.priority;
        return priority || "N/A";
      }
    },
    { 
      key: "Handle Name", 
      title: "Handle Name",
      render: (item: Record<string, string | number | undefined>) => {
        const handleName = item["Handle Name"] || item.handle_name;
        return handleName || "N/A";
      }
    },
    { 
      key: "likes", 
      title: "Likes",
      render: (item: Record<string, string | number | undefined>) => {
        const likes = item.likes;
        return likes !== null && likes !== undefined ? likes : "N/A";
      }
    },
    { 
      key: "followers", 
      title: "Followers",
      render: (item: Record<string, string | number | undefined>) => {
        const followers = item.followers;
        return followers !== null && followers !== undefined ? followers : "N/A";
      }
    },
    {
      key: "url",
      title: "URL",
      render: (item: Record<string, string | number | undefined>) => {
        const urlData = item.url as string | string[] | Array<{
          source_url?: string;
          destination_url?: string;
          screenshot?: string;
          case_reports?: string;
        }>;
        
        // Handle different URL data formats
        let urls: string[] = [];
        if (typeof urlData === 'string') {
          urls = [urlData];
        } else if (Array.isArray(urlData)) {
          // Check if it's the new object format
          if (urlData.length > 0 && typeof urlData[0] === 'object' && urlData[0] !== null) {
            // New format: array of objects with specific keys
            urls = [
              (urlData[0] as any)?.source_url || '',
              (urlData[1] as any)?.destination_url || '',
              (urlData[2] as any)?.screenshot || '',
              (urlData[3] as any)?.case_reports || ''
            ];
          } else {
            // Old format: array of strings
            urls = urlData as string[];
          }
        }
        
        // Ensure we have exactly 4 slots for icons
        const displayUrls = [...urls, '', '', '', ''].slice(0, 4);
        
        return (
          <div className="flex items-center justify-center gap-2 w-full">
            {displayUrls.map((url, index) => {
              const hasUrl = url && typeof url === 'string' && url.trim() !== '';
              
              // Different icons for each slot
              const getIcon = (index: number) => {
                switch (index) {
                  case 0:
                    return <Link className="h-4 w-4 dark:text-white" />;
                  case 1:
                    return <Send className="h-4 w-4 dark:text-white" />;
                  case 2:
                    return <Camera className="h-4 w-4 dark:text-white" />;
                  case 3:
                    return <FileText className="h-4 w-4 dark:text-white" />;
                  default:
                    return <Link className="h-4 w-4 dark:text-white" />;
                }
              };
              
              // Different tooltip labels for each slot
              const getTooltipLabel = (index: number) => {
                switch (index) {
                  case 0:
                    return hasUrl ? 'Source URL' : 'No Source URL available';
                  case 1:
                    return hasUrl ? 'Destination URL' : 'No Destination URL available';
                  case 2:
                    return hasUrl ? 'Screenshot' : 'No Screenshot available';
                  case 3:
                    return hasUrl ? 'Case Report' : 'No Case Report available';
                  default:
                    return hasUrl ? 'URL' : 'No URL available';
                }
              };
              
              return (
                <div
                  key={index}
                  className={`${hasUrl ? 'cursor-pointer text-primary hover:text-[#540094] hover:underline' : 'cursor-not-allowed text-gray-400'}`}
                  title={getTooltipLabel(index)}
                  onClick={hasUrl ? () => window.open(url, '_blank') : undefined}
                >
                  {getIcon(index)}
                </div>
              );
            })}
          </div>
        );
      }
    }
  ];

  // Add a function to check if an item is selected
  const isItemSelected = (item: Record<string, string | number | undefined>) => {
    return selectedItems.some((selectedItem) => selectedItem.id === item.id);
  };

  // Add a function to handle selection
  const handleSelection = (items: Record<string, string | number | undefined>[]) => {
    setSelectedItems(items as unknown as IncidentData[]);
    // Clear selected action when no items are selected
    if (items.length === 0) {
      setSelectedAction('');
    }
  };

  // Add download handler function for individual incidents
  const handleDownload = async (item: Record<string, string | number | undefined>) => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) {
        console.error('No authorization token found');
        return;
      }

      // console.log("Downloading incident for:", item);

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.SM_DOWNLOAD_INCIDENTS,
        {
          package_name: selectedPackage,
          fromDate: fromDate,
          toDate: toDate,
          // incident_id: item.id,
          // Include any other necessary parameters for the specific incident
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );

      // Check if response contains a download link
      if (response.data && response.data.url) {
        // Use the provided download link
        const downloadUrl = response.data.url;
        // console.log("Download link received:", downloadUrl);
        
        // Create a download link
        const link = document.createElement('a');
        link.href = downloadUrl;
        
        // Set filename based on incident data
        const filename = `incident_${item.id}_${item.brand}_${new Date().toISOString().split('T')[0]}.csv`;
        link.setAttribute('download', filename);
        link.style.display = 'none';
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        
        // Cleanup after a short delay to ensure download starts
        setTimeout(() => {
          document.body.removeChild(link);
        }, 100);
        
        // console.log("Download completed successfully");
      } else {
        // Fallback to blob download if no link provided
        // console.log("No download link in response, using blob download");
        
        // Create a download link
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        
        // Set filename based on incident data
        const filename = `incident_${item.id}_${item.brand}_${new Date().toISOString().split('T')[0]}.csv`;
        link.setAttribute('download', filename);
        link.style.display = 'none';
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        
        // Cleanup after a short delay to ensure download starts
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
        
        // console.log("Download completed successfully");
      }
    } catch (error) {
      console.error("Download failed:", error);
      // You might want to show a toast notification here
    }
  };

  // Handle ticket form input changes
  const handleTicketFormChange = (field: string, value: string) => {
    setTicketForm(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear search when assignee is selected
    if (field === 'assignee') {
      setAssigneeSearch('');
    }
  };

  // Handle watcher list changes
  const handleWatcherListChange = (selectedWatchers: string[]) => {
    setTicketForm(prev => ({
      ...prev,
      watcherList: selectedWatchers
    }));
  };

  // Handle raise ticket dialog open
  const handleRaiseTicket = () => {
    // Reset form with empty estimated time (will be filled by user)
    setTicketForm(prev => ({
      ...prev,
      estimatedTime: ''
    }));
    
    setIsRaiseTicketDialogOpen(true);
  };

  // Handle ticket submission
  const handleTicketSubmit = async () => {
    if (!ticketForm.brand.trim() || !ticketForm.description.trim() || !ticketForm.subject.trim()) {
      alert('Please fill in all required fields (Brand, Subject, and Description)');
      return;
    }

    if (!dateRange.from || !dateRange.to) {
      alert('Please select both start and end dates');
      return;
    }

    if (selectedItems.length === 0) {
      alert('Please select at least one incident to raise a ticket for');
      return;
    }

    setIsSubmittingTicket(true);
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) {
        console.error('No authorization token found');
        return;
      }

      // API call to submit ticket
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.RAISE_TICKET,
        {
          tickets: selectedItems.map(item => ({
            id: String(item.id),
            subject: ticketForm.subject,
            start_date: dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : '',
            due_date: dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : '',
            estimate_time: ticketForm.estimatedTime,
            brand: ticketForm.brand,
            priority: ticketForm.priority,
            status: ticketForm.status,
            assignee: ticketForm.assignee,
            email_list: ticketForm.watcherList,
            description: ticketForm.description,
            project: selectedPackage, // Add selected package as project key
            incident_date: item.date // Add incident date to payload
          }))
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );

      // console.log('Ticket created successfully:', response.data);
      
      // Reset form and close dialog
      setTicketForm({
        brand: '',
        estimatedTime: '',
        priority: '',
        status: '',
        assignee: '',
        description: '',
        subject: '',
        watcherList: []
      });
      setDateRange({ from: undefined, to: undefined });
      setIsRaiseTicketDialogOpen(false);
      
      // Show success toast message
      toast({
        title: "Success",
        description: "Tickets Raised Successfully",
        className: "border-l-4 border-l-green-500 bg-green-50 border-green-200",
        action: (
          <CheckCircle className="h-5 w-5 text-green-600" />
        ),
      });
      
      // Refetch incidents data to show latest information
      refetchIncidentsData();
      
    } catch (error) {
      console.error('Failed to Raise Ticket:', error);
      // Show error toast message
      toast({
        title: "Error",
        description: "Failed to Raise Ticket.",
        className: "border-l-4 border-l-red-500 bg-red-50 border-red-200",
        action: (
          <XCircle className="h-5 w-5 text-red-600" />
        ),
      });
    } finally {
      setIsSubmittingTicket(false);
    }
  };

  // Handle ticket dialog close - clears all form data and resets state
  const handleTicketDialogClose = () => {
    setIsRaiseTicketDialogOpen(false);
    // Reset all form fields to initial state
    setTicketForm({
      brand: '',
      estimatedTime: '',
      priority: '',
      status: '',
      assignee: '',
      description: '',
      subject: '',
      watcherList: []
    });
    // Reset date range
    setDateRange({ from: undefined, to: undefined });
    // Clear assignee search
    setAssigneeSearch('');
    // Clear selected items when dialog closes
    setSelectedItems([]);
  };

  // Handle action selection from dropdown
  const handleActionSelect = (action: string) => {
    setSelectedAction(action);
  };

  // Handle page change from table
  const handlePageChange = (page: number) => {
    setPageNumber(page);
    // Clear filtered data when page changes to prevent showing stale data
    setFilteredData([]);
  };

  // Handle limit change from table
  const handleLimitChange = (limit: number) => {
    setRecordLimit(limit);
    setPageNumber(1); // Reset to first page when limit changes
    // Clear filtered data when limit changes
    setFilteredData([]);
  };

  // Handle submit action for selected incidents
  const handleSubmitAction = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "Warning",
        description: "Please select at least one incident to update.",
        className: "border-l-4 border-l-yellow-500 bg-yellow-50 border-yellow-200",
        action: (
          <XCircle className="h-5 w-5 text-yellow-600" />
        ),
      });
      return;
    }

    if (!selectedAction) {
      toast({
        title: "Warning",
        description: "Please select an action from the dropdown.",
        className: "border-l-4 border-l-yellow-500 bg-yellow-50 border-yellow-200",
        action: (
          <XCircle className="h-5 w-5 text-yellow-600" />
        ),
      });
      return;
    }

    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) {
        console.error('No authorization token found');
        return;
      }

      // Map action names to status values
      const actionToStatusMap: { [key: string]: string } = {
        'Whitelist': 'Whitelist',
        'Close : No Action': 'Close:No Action',
        'Take Down': 'Take Down',
        'Hold Case': 'Hold Case'
      };

      const status = actionToStatusMap[selectedAction] || selectedAction;

      // Prepare the payload with selected incident IDs, package, and incident date
      const payload = selectedItems.map(item => ({
        id: String(item.id),
        status: status,
        package_name: selectedPackage,
        incident_date: item.date
      }));

      // console.log("Updating incidents with payload:", payload);

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.SM_UPDATE_INCIDENT,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );

      // console.log('Incidents updated successfully:', response.data);
      
      // Clear selected items and action after successful update
      setSelectedItems([]);
      setSelectedAction('');
      // Force table to re-render and clear its internal selection state
      setTableKey(prev => prev + 1);
      
      // Show success toast message using API response
      const successMessage = response.data?.message || 
                           response.data?.success_message || 
                           response.data?.status_message ||
                           `${selectedItems.length} incident(s) updated successfully with action: ${selectedAction}`;
      
      toast({
        title: "Success",
        description: successMessage,
        className: "border-l-4 border-l-green-500 bg-green-50 border-green-200",
        action: (
          <CheckCircle className="h-5 w-5 text-green-600" />
        ),
      });
      
      // Refetch the incidents data to reflect the changes
      // You might want to invalidate the query here if using React Query
      refetchIncidentsData();
      
    } catch (error) {
      console.error('Failed to update incidents:', error);
      // Show error toast message
      toast({
        title: "Error",
        description: "Failed to update incidents. Please try again.",
        className: "border-l-4 border-l-red-500 bg-red-50 border-red-200",
        action: (
          <XCircle className="h-5 w-5 text-red-600" />
        ),
      });
    }
  };

  // Add download all handler function for the toolbar download button
  const handleDownloadAll = async (items: Record<string, string | number | undefined>[]) => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) {
        console.error('No authorization token found');
        return;
      }

      // console.log("Downloading all incidents:", items.length);

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INCIDENTS.SM_DOWNLOAD_INCIDENTS,
        {
          package_name: selectedPackage,
          fromDate: fromDate,
          toDate: toDate,
          // Download all incidents in the current view/filter
          download_all: true,
          // Include any other necessary parameters
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );

              // Check if response contains a download link
        if (response.data && response.data.url) {
          // Use the provided download link
          const downloadUrl = response.data.url;
          // console.log("Download link received:", downloadUrl);
        
        // Create a download link
        const link = document.createElement('a');
        link.href = downloadUrl;
        
        // Set filename for all incidents
        const filename = `all_incidents_${selectedPackage}_${fromDate}_to_${toDate}_${new Date().toISOString().split('T')[0]}.csv`;
        link.setAttribute('download', filename);
        link.style.display = 'none';
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        
        // Cleanup after a short delay to ensure download starts
        setTimeout(() => {
          document.body.removeChild(link);
        }, 100);
        
        // console.log("Download all completed successfully");
      } else {
        // Fallback to blob download if no link provided
        // console.log("No download link in response, using blob download");
        
        // Create a download link
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        
        // Set filename for all incidents
        const filename = `all_incidents_${selectedPackage}_${fromDate}_to_${toDate}_${new Date().toISOString().split('T')[0]}.csv`;
        link.setAttribute('download', filename);
        link.style.display = 'none';
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        
        // Cleanup after a short delay to ensure download starts
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
        
        // console.log("Download all completed successfully");
      }
    } catch (error) {
      console.error("Download all failed:", error);
      // You might want to show a toast notification here
    }
  };
//  console.log("yaha tk chl rha hai ",filteredData)
  return (
    <div 
      className="flex flex-col w-full h-full overflow-hidden p-2" 
      style={{ 
        height: '100%', 
        maxHeight: '100%',
        overflow: 'hidden'
      }}
    >
      {/* Filters Container */}
      <div className="flex-shrink-0 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 shadow-[0_-1px_2px_rgba(0,0,0,0.1)]">
        {/* <div className="text-lg font-semibold text-gray-800">Website Incidents</div> */}
        <div className="flex flex-wrap gap-2 bg-white dark:bg-gray-700">
          {/* Priority Filter */}
          <FilterPill
            id="priority"
            title="Priority"
            filters={transformFiltersData(priorityFilterData, selectedPriority)}
            onSubmit={handleFilterSubmit}
            onSearch={() => { }}
            loading={false}
            isSearchable={false}
          />

          {/* Category Filter */}
          <FilterPill
            id="category"
            title="Category"
            filters={transformFiltersData(categoryFilterData, selectedCategory)}
            onSubmit={handleFilterSubmit}
            onSearch={() => { }}
            loading={false}
            isSearchable={false}
          />

          {/* Platform Filter */}
          <FilterPill
            id="platform"
            title="Platform"
            filters={transformFiltersData(platformFilterData, selectedPlatform)}
            onSubmit={handleFilterSubmit}
            onSearch={() => { }}
            loading={false}
            isSearchable={false}
          />

          {/* Status Filter */}
          <FilterPill
            id="status"
            title="Status"
            filters={transformFiltersData(statusFilterData, selectedStatus)}
            onSubmit={handleFilterSubmit}
            onSearch={() => { }}
            loading={false}
            isSearchable={false}
          />
        </div>
      </div>

      {/* Search Bar, Actions, and Upload */}
      <div className="flex-shrink-0 bg-white p-2 rounded-md shadow-md dark:bg-gray-700 mt-2">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 dark:bg-gray-700">
          <div className="flex items-center gap-3 dark:bg-gray-700">
            <div className="flex items-center gap-2  text-black dark:text-white">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="lg"
                    className="flex items-center gap-2 bg-white border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900 text-gray-700 text-black dark:text-white dark:bg-gray-800"
                    disabled={selectedItems.length === 0}
                  >
                    <span>{selectedAction || "Select Action"}</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-48">
                  <DropdownMenuItem
                    onClick={() => handleActionSelect('Whitelist')}
                    disabled={selectedItems.length === 0}
                  >
                    Whitelist
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleActionSelect('Close : No Action')}
                    disabled={selectedItems.length === 0}
                  >
                    Close : No Action
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleActionSelect('Take Down')}
                    disabled={selectedItems.length === 0}
                  >
                    Take Down
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleActionSelect('Hold Case')}
                    disabled={selectedItems.length === 0}
                  >
                    Hold Case
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="default"
                size="lg"
                className="dark:text-white dark:bg-gray-900 hover:dark:bg-gray-500 hover:text-gray-200"
                disabled={selectedItems.length === 0 || !selectedAction}
                onClick={handleSubmitAction}
              >
                Submit
              </Button>
            </div>

            <Button
              variant="default"
              size="lg"
              className="dark:text-white dark:bg-gray-900 hover:dark:bg-gray-500 hover:text-gray-200"
              onClick={handleRaiseTicket}
              disabled={selectedItems.length === 0}
            >
              <span>Assign</span>
            </Button>

          </div>

          <Input
            type="file"
            accept=".csv,.xlsx,.xls"
            onChange={handleFileUpload}
            className="hidden"
            id="csv-upload"
            disabled={isUploadingFile}
          />
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-0">
            <Label htmlFor="csv-upload" className={`cursor-pointer ${isUploadingFile ? 'opacity-50 pointer-events-none' : ''}`}>
              <div className="flex items-center gap-3 hover:opacity-80 transition-opacity">
                {isUploadingFile ? (
                  <Loader2 className="w-8 h-8 text-primary dark:text-black animate-spin" />
                ) : (
                  <FileUp className="w-8 h-8 text-primary dark:text-black" />
                )}
                <div className="flex flex-col">
                  <div className="text-base font-medium dark:text-black">
                    {isUploadingFile ? 'Processing...' : 'Bulk Upload'}
                  </div>
                  <div className="text-sm text-primary dark:text-white hover:text-primary/80 dark:hover:text-gray-300">
                    {isUploadingFile ? 'Please wait, this may take a few minutes' : 'Upload CSV/Excel file'}
                  </div>
                </div>
              </div>
            </Label>

            {/* Sample CSV File */}
            <div 
              className="flex items-center gap-3 hover:opacity-80 transition-opacity cursor-pointer sm:ml-4"
              onClick={() => {
                // Create a hidden link element to avoid screen flashing
                const link = document.createElement('a');
                link.href = 'https://d3w2wyejgk3zrv.cloudfront.net/Ticket_sample/Ticket_bulk_upload_Sample.xlsx';
                link.download = 'Ticket_bulk_upload_Sample.csv';
                link.style.display = 'none';
                link.style.position = 'absolute';
                link.style.left = '-9999px';
                document.body.appendChild(link);
                link.click();
                // Remove the link after a short delay to ensure download starts
                setTimeout(() => {
                  if (document.body.contains(link)) {
                    document.body.removeChild(link);
                  }
                }, 100);
              }}
              title="Download Sample CSV Template"
            >
              <FileDown className="w-8 h-8 text-[hsl(306,82%,35%)] dark:text-black" />
              <div className="flex flex-col">
                <div className="text-base font-medium dark:text-black">Sample CSV file</div>
                <div className="text-sm text-primary dark:text-white hover:text-primary/80 dark:hover:text-gray-300">
                  Download template
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 bg-white dark:bg-gray-700 min-h-0 mt-2 flex flex-col">
        <div className="flex-1 overflow-hidden">
          {incidentsLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
            </div>
          ) : incidentsError ? (
            <div className="flex items-center justify-center h-full">
              {/* <div className="text-lg text-red-600">Error loading incidents. Please try again.</div> */}
            </div>
          ) : filteredData.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-lg text-gray-600">No data found</div>
            </div>
          ) : (
            <ResizableTable
              // key={tableKey}
              columns={columns}
              data={(filteredData || []) as any}
              isSearchable={true}
              isPaginated={true}
              isSelectable={true}
              isEdit={false}
              isDelete={false}
              isView={false}
              isDownload={true}
              onSelect={handleSelection}
              isItemSelected={isItemSelected}
              onDownload={handleDownload}
              onDownloadAll={handleDownloadAll}
              initialItemsPerPage={recordLimit}
              onPageChange={handlePageChange}
              onLimitChange={handleLimitChange}
              totalPages={totalPages}
              currentPage={pageNumber}
              totalRecords={totalRecords}
            />
          )}
        </div>
      </div>

      {/* Raise Ticket Dialog */}
      <Dialog open={isRaiseTicketDialogOpen} onOpenChange={(open) => {
        if (!open) {
          // Dialog is being closed (via escape key, outside click, or programmatically)
          // Clear the form and reset all state
          handleTicketDialogClose();
        }
      }}>
        <DialogContent className="sm:max-w-[850px]">
          <DialogHeader>
            <DialogTitle>
              Assign
            </DialogTitle>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            {/* First Row - Subject, Date Range, Estimated Time */}
            <div className="grid grid-cols-6 gap-4">
              <div className="space-y-2 col-span-2">
                <Label htmlFor="ticket-subject">Subject</Label>
                <Input
                  id="ticket-subject"
                  value={ticketForm.subject}
                  onChange={(e) => handleTicketFormChange('subject', e.target.value)}
                  placeholder="Enter subject"
                />
              </div>
              <div className="space-y-2 col-span-2">
                <Label htmlFor="ticket-date-range">Start - End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange.from && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange.from}
                      selected={dateRange}
                      onSelect={(range) => {
                        if (range) {
                          setDateRange({
                            from: range.from,
                            to: range.to
                          });
                        }
                      }}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2 col-span-2">
                <Label htmlFor="ticket-estimated-time">Estimated Time (hours)</Label>
                <Input
                  id="ticket-estimated-time"
                  type="number"
                  min="1"
                  step="1"
                  value={ticketForm.estimatedTime}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (value > 0 || e.target.value === '') {
                      handleTicketFormChange('estimatedTime', e.target.value);
                    }
                  }}
                  placeholder="Enter hours"
                />
              </div>
            </div>

            {/* Second Row - Brand, Priority, Status, Assignee */}
            <div className="grid grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ticket-brand">Brand</Label>
                <Select value={ticketForm.brand} onValueChange={(value) => handleTicketFormChange('brand', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={brandFilterLoading ? "Loading..." : "Select Brand"} />
                  </SelectTrigger>
                  <SelectContent>
                    {brandFilterLoading ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="ml-2">Loading brand...</span>
                      </div>
                    ) : brandFilterData?.data ? (
                      brandFilterData.data.map((brand) => (
                        <SelectItem key={brand} value={brand}>
                          {brand}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="" disabled>
                        No brand available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="ticket-priority">Priority</Label>
                <Select value={ticketForm.priority} onValueChange={(value) => handleTicketFormChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={priorityFilterData?.data ? "Select Priority" : "Loading..."} />
                  </SelectTrigger>
                  <SelectContent>
                    {priorityFilterData?.data ? (
                      priorityFilterData.data.map((priority) => (
                        <SelectItem key={priority} value={priority}>
                          {priority}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="ml-2">Loading priority...</span>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="ticket-status">Status</Label>
                <Select value={ticketForm.status} onValueChange={(value) => handleTicketFormChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Status" />
                  </SelectTrigger>
                  <SelectContent>
                  <SelectItem value="raised">Raised</SelectItem>
                    <SelectItem value="in progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="ticket-assignee">Assignee</Label>
                <Select value={ticketForm.assignee} onValueChange={(value) => handleTicketFormChange('assignee', value)}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={usersLoading ? "Loading..." : "Select Assignee"} />
                  </SelectTrigger>
                  <SelectContent className="w-full min-w-[200px]">
                    {usersLoading ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="ml-2">Loading users...</span>
                      </div>
                    ) : usersData?.result && usersData.result.length > 0 ? (
                      <>
                        {/* Search Input */}
                        <div className="flex items-center px-3 py-2 border-b">
                          <Input
                            placeholder="Search users..."
                            value={assigneeSearch}
                            onChange={(e) => setAssigneeSearch(e.target.value)}
                            onKeyDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            className="h-8 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                        {/* Filtered Users */}
                        {filteredAssigneeUsers.length > 0 ? (
                          filteredAssigneeUsers.map((email) => {
                            return (
                              <SelectItem key={email} value={email} title={email}>
                                {email}
                              </SelectItem>
                            );
                          })
                        ) : (
                          <div className="px-3 py-2 text-sm text-gray-500">
                            No users found
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="px-3 py-2 text-sm text-gray-500">
                        No users available
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Third Row - Description and WatcherList */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2 col-span-2">
                <Label htmlFor="ticket-description">Description</Label>
                <textarea
                  id="ticket-description"
                  value={ticketForm.description}
                  onChange={(e) => handleTicketFormChange('description', e.target.value)}
                  placeholder="Enter description"
                  className="min-h-[100px] p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent w-full"
                />
              </div>
              <div className="space-y-2 col-span-1">
                <Label htmlFor="ticket-watcher-list">Watcher List</Label>
                {usersLoading ? (
                  <div className="flex items-center justify-center p-4 border border-gray-300 rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="ml-2">Loading users...</span>
                  </div>
                ) : usersData?.result ? (
                  <MultiSelect
                    options={usersData.result
                      .filter(email => email && typeof email === 'string') // Filter out invalid emails
                      .map((email) => {
                        return {
                          label: email, // Display email in UI
                          value: email, // Send email in payload
                          title: email // Email for tooltip
                        };
                      })}
                    onValueChange={handleWatcherListChange}
                    defaultValue={ticketForm.watcherList}
                    placeholder="Select Watchers"
                    maxCount={2}
                    className="w-full [&_[cmdk-list]]:overflow-y-auto [&_[cmdk-list]]:scrollbar-thin [&_[cmdk-list]]:scrollbar-thumb-gray-300 [&_[cmdk-list]]:scrollbar-track-gray-100"
                  />
                ) : (
                  <div className="flex items-center justify-center p-4 border border-gray-300 rounded-md text-gray-500">
                    No users available
                  </div>
                )}
              </div>
            </div>

            {/* Selected Incidents Info */}
            {selectedItems.length > 0 && (
              <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                <strong>Related Incidents:</strong> {selectedItems.length} incident(s) selected
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              size="lg"
              onClick={handleTicketDialogClose}
              disabled={isSubmittingTicket}
              className="dark:text-white dark:bg-gray-900 hover:dark:bg-gray-500"
            >
              Close
            </Button>
            <Button
              variant="default"
              size="lg"
              onClick={handleTicketSubmit}
              disabled={isSubmittingTicket || !ticketForm.brand.trim() || !ticketForm.description.trim() || !ticketForm.subject.trim() || !dateRange.from || !dateRange.to || selectedItems.length === 0}
              className="dark:text-white dark:bg-gray-900 hover:dark:bg-gray-500"
            >
              {isSubmittingTicket ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}