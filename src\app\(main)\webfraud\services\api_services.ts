"use client"
import { QueryFunctionContext } from "@tanstack/react-query";
import axios, { AxiosRequestConfig, AxiosError } from "axios";

// TYPES
type Method = "GET" | "POST" | "PUT" | "DELETE";
type APICallType = {
  method: Method;
  url: string;
  headers?: Record<string, string>;
  params?: object;
};

// Default headers
const headers = { "Content-Type": "application/json" };
const API_Instance = axios.create({ headers });

// Generic API call function
export async function APICall<T = unknown>({
  method,
  url,
  headers,
  params,
}: APICallType): Promise<T> {
  try {
    const config: AxiosRequestConfig = {
      method,
      url,
      headers,
      params: method === "GET" ? params : undefined,
      data: method !== "GET" ? params : undefined,
    };

    const response = await API_Instance(config);
    return response.data as T;
  } catch (error) {
    console.error("API Error:", error);
    if (error instanceof AxiosError) {
      switch (error.status) {
        case 401:
          // Handle 401 Unauthorized (e.g., redirect to login)
          // localStorage.clear();
          // window.location.href = "/";
          break;
        default:
          throw new Error(
            error.response?.data?.error ?? "Something went wrong",
          );
      }
    }
    throw new Error("Something went wrong");
  }
}

// Query function for React Query
export async function queryFunction<T = unknown>(
  context: QueryFunctionContext,
): Promise<T> {
  const { queryKey, signal } = context;
  const [_, options] = queryKey;

  return APICall<T>({
    ...options,
    signal, // Pass the AbortSignal to the API call
  });
}

// Mutation function for React Query
export async function mutationFunction<T = unknown>(
  options: AxiosRequestConfig,
): Promise<T> {
  return APICall<T>(options);
}