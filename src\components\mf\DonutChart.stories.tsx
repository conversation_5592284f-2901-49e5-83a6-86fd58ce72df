import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import Donut<PERSON>hart from './DonutChart';

const meta: Meta<typeof DonutChart> = {
  title: 'Components/DonutChart',
  component: DonutChart,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    legendPosition: {
      control: { type: 'select' },
      options: ['right', 'bottom', 'left', 'top'],
    },
    isPercentage: {
      control: { type: 'boolean' },
    },
    isLabelist: {
      control: { type: 'boolean' },
    },
    showRefresh: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for testing
const sampleChartData = [
  { label: 'Desktop', value: 45.2, fill: '#8884d8' },
  { label: 'Mobile', value: 32.1, fill: '#82ca9d' },
  { label: 'Tablet', value: 15.7, fill: '#ffc658' },
  { label: 'Other', value: 7.0, fill: '#ff7c7c' },
];

const sampleChartConfig = {
  Desktop: { label: 'Desktop', color: '#8884d8' },
  Mobile: { label: 'Mobile', color: '#82ca9d' },
  Tablet: { label: 'Tablet', color: '#ffc658' },
  Other: { label: 'Other', color: '#ff7c7c' },
};

export const Default: Story = {
  args: {
    chartData: sampleChartData,
    chartConfig: sampleChartConfig,
    dataKey: 'value',
    nameKey: 'label',
    title: 'Device Usage Distribution',
    isView: true,
    isPercentage: true,
    legendPosition: 'right',
    onExpand: () => console.log('Expand clicked'),
  },
};

export const WithBottomLegend: Story = {
  args: {
    ...Default.args,
    legendPosition: 'bottom',
    title: 'Traffic Sources (Bottom Legend)',
  },
};

export const WithCenterValue: Story = {
  args: {
    ...Default.args,
    centerValue: '1,234',
    centerLabel: 'Total Users',
    title: 'User Analytics',
  },
};

export const SmallSize: Story = {
  args: {
    ...Default.args,
    title: 'Small Chart',
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

export const LargeDataset: Story = {
  args: {
    chartData: [
      { label: 'Chrome', value: 35.2, fill: '#4285f4' },
      { label: 'Safari', value: 28.1, fill: '#34a853' },
      { label: 'Firefox', value: 15.7, fill: '#ea4335' },
      { label: 'Edge', value: 12.0, fill: '#fbbc05' },
      { label: 'Opera', value: 5.5, fill: '#9aa0a6' },
      { label: 'Internet Explorer', value: 2.1, fill: '#ff6d01' },
      { label: 'Other', value: 1.4, fill: '#673ab7' },
    ],
    chartConfig: {
      Chrome: { label: 'Chrome', color: '#4285f4' },
      Safari: { label: 'Safari', color: '#34a853' },
      Firefox: { label: 'Firefox', color: '#ea4335' },
      Edge: { label: 'Edge', color: '#fbbc05' },
      Opera: { label: 'Opera', color: '#9aa0a6' },
      'Internet Explorer': { label: 'Internet Explorer', color: '#ff6d01' },
      Other: { label: 'Other', color: '#673ab7' },
    },
    dataKey: 'value',
    nameKey: 'label',
    title: 'Browser Market Share',
    isView: true,
    isPercentage: true,
    legendPosition: 'right',
    onExpand: () => console.log('Expand clicked'),
  },
};

export const WithRefreshButton: Story = {
  args: {
    ...Default.args,
    showRefresh: true,
    onRefresh: () => console.log('Refresh clicked'),
    title: 'Refreshable Chart',
  },
};

export const Loading: Story = {
  args: {
    ...Default.args,
    isLoading: true,
    title: 'Loading Chart',
  },
};

export const NoData: Story = {
  args: {
    chartData: [],
    chartConfig: {},
    dataKey: 'value',
    nameKey: 'label',
    title: 'No Data Chart',
    isView: true,
    onExpand: () => console.log('Expand clicked'),
  },
};

export const ResponsiveTest: Story = {
  args: {
    ...Default.args,
    title: 'Responsive Chart Test',
    legendPosition: 'bottom',
    enableHorizontalScroll: true,
  },
  parameters: {
    layout: 'fullscreen',
  },
};
