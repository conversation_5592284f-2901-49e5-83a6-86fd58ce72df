"use client";

import React from 'react';
import DonutChart from '@/components/mf/DonutChart';

const TestDonutPage = () => {
  // Sample data for testing
  const sampleChartData = [
    { label: 'Desktop', value: 45.2, fill: '#8884d8' },
    { label: 'Mobile', value: 32.1, fill: '#82ca9d' },
    { label: 'Tablet', value: 15.7, fill: '#ffc658' },
    { label: 'Other', value: 7.0, fill: '#ff7c7c' },
  ];

  const sampleChartConfig = {
    Desktop: { label: 'Desktop', color: '#8884d8' },
    Mobile: { label: 'Mobile', color: '#82ca9d' },
    Tablet: { label: 'Tablet', color: '#ffc658' },
    Other: { label: 'Other', color: '#ff7c7c' },
  };

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">DonutChart Responsive Test</h1>
      
      {/* Test 1: Default size */}
      <div className="border p-4">
        <h2 className="text-lg font-semibold mb-4">Test 1: Default Size</h2>
        <div className="w-full h-[300px]">
          <DonutChart
            chartData={sampleChartData}
            chartConfig={sampleChartConfig}
            dataKey="value"
            nameKey="label"
            title="Device Usage Distribution"
            isView={true}
            isPercentage={true}
            legendPosition="right"
            onExpand={() => console.log('Expand clicked')}
          />
        </div>
      </div>

      {/* Test 2: Small container */}
      <div className="border p-4">
        <h2 className="text-lg font-semibold mb-4">Test 2: Small Container (200px height)</h2>
        <div className="w-full h-[200px]">
          <DonutChart
            chartData={sampleChartData}
            chartConfig={sampleChartConfig}
            dataKey="value"
            nameKey="label"
            title="Small Chart"
            isView={true}
            isPercentage={true}
            legendPosition="bottom"
            onExpand={() => console.log('Expand clicked')}
          />
        </div>
      </div>

      {/* Test 3: Large container */}
      <div className="border p-4">
        <h2 className="text-lg font-semibold mb-4">Test 3: Large Container (500px height)</h2>
        <div className="w-full h-[500px]">
          <DonutChart
            chartData={sampleChartData}
            chartConfig={sampleChartConfig}
            dataKey="value"
            nameKey="label"
            title="Large Chart"
            isView={true}
            isPercentage={true}
            legendPosition="right"
            centerValue="1,234"
            centerLabel="Total Users"
            onExpand={() => console.log('Expand clicked')}
          />
        </div>
      </div>

      {/* Test 4: Mobile simulation */}
      <div className="border p-4">
        <h2 className="text-lg font-semibold mb-4">Test 4: Mobile Simulation (320px width)</h2>
        <div className="w-[320px] h-[250px] mx-auto border-2 border-gray-300">
          <DonutChart
            chartData={sampleChartData}
            chartConfig={sampleChartConfig}
            dataKey="value"
            nameKey="label"
            title="Mobile Chart"
            isView={true}
            isPercentage={true}
            legendPosition="bottom"
            enableHorizontalScroll={true}
            onExpand={() => console.log('Expand clicked')}
          />
        </div>
      </div>

      {/* Test 5: With many data points */}
      <div className="border p-4">
        <h2 className="text-lg font-semibold mb-4">Test 5: Many Data Points</h2>
        <div className="w-full h-[350px]">
          <DonutChart
            chartData={[
              { label: 'Chrome', value: 35.2, fill: '#4285f4' },
              { label: 'Safari', value: 28.1, fill: '#34a853' },
              { label: 'Firefox', value: 15.7, fill: '#ea4335' },
              { label: 'Edge', value: 12.0, fill: '#fbbc05' },
              { label: 'Opera', value: 5.5, fill: '#9aa0a6' },
              { label: 'Internet Explorer', value: 2.1, fill: '#ff6d01' },
              { label: 'Other Browsers', value: 1.4, fill: '#673ab7' },
            ]}
            chartConfig={{
              Chrome: { label: 'Chrome', color: '#4285f4' },
              Safari: { label: 'Safari', color: '#34a853' },
              Firefox: { label: 'Firefox', color: '#ea4335' },
              Edge: { label: 'Edge', color: '#fbbc05' },
              Opera: { label: 'Opera', color: '#9aa0a6' },
              'Internet Explorer': { label: 'Internet Explorer', color: '#ff6d01' },
              'Other Browsers': { label: 'Other Browsers', color: '#673ab7' },
            }}
            dataKey="value"
            nameKey="label"
            title="Browser Market Share"
            isView={true}
            isPercentage={true}
            legendPosition="right"
            onExpand={() => console.log('Expand clicked')}
          />
        </div>
      </div>
    </div>
  );
};

export default TestDonutPage;
