"use client";
import React, { useState, useMemo, useRef, use<PERSON><PERSON>back } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Users, FileText, ThumbsUp, Loader2 } from "lucide-react";
import { FilterPill } from "@/components/mf/Filters";
import { useQuery } from "react-query";
import axios from "axios";
import Endpoint from "@/common/endpoint";

import { usePackage } from "@/components/mf/PackageContext";
import { FilterState } from "@/components/mf/Filters/FilterPill";
import { StackedBarWithLine } from "@/components/mf/charts/StackedBarwithLine";
import HeaderRow from "@/components/mf/HeaderRow";
import EllipsisTooltip from "@/components/mf/EllipsisTooltip";
import { onExpand, downloadURI, handleCSVDownloadFromResponse } from "@/lib/utils";
import domToImage from "dom-to-image";
import Dynamic<PERSON>ar<PERSON>hart from "@/components/mf/DynamicBarChart";
import { MFPieChartInteractive } from "@/components/mf/charts/MFPieChartInteractive";
import { Link } from "lucide-react";
import ResizableTable from "@/components/mf/TableComponent";


interface FilterResponse {
  data: string[];
  status: boolean;
}

interface IncidentData {
  category: string;
  "Last Month": number;
  "Last Quarter": number;
  "Last Year": number;
  label: string;
  value: number;
}

// Add interface and data transformation
interface CategoryDataResponse {
  data: {
    similar_domains: {
      count: number;
      percentage: number;
    };
    blogging_sites: {
      count: number;
      percentage: number;
    };
  };
  status: boolean;
}

// Add new interface for website status data
interface WebsiteStatusDataResponse {
  data: {
    active_sites: {
      count: number;
      percentage: number;
    };
    inactive_sites: {
      count: number;
      percentage: number;
    };
    pending_sites: {
      count: number;
      percentage: number;
    };
  };
  status: boolean;
}

interface TransformedCategoryData {
  title: string;
  count: string;
  percentage: string;
  bgColor: string;
  link: string;
  customBgColor: string;
}



// Transform category data
const transformCategoryData = (
  response: CategoryDataResponse
): TransformedCategoryData[] => {
  const categoryMapping = {
    similar_domains: {
      title: "Telegram",
      bgColor: "bg-gradient-to-r from-purple-100 to-purple-200",
      link: "",
      customBgColor: "#274745",
    },
    blogging_sites: {
      title: "Facebook",
      bgColor: "bg-gradient-to-r from-red-100 to-red-200",
      link: "",
      customBgColor: "#E8C468",
    },
  };

  try {
    const data = response.data;
    return Object.entries(data)
      .map(([key, value]) => {
        const category = categoryMapping[key as keyof typeof categoryMapping];
        if (!category) return null;

        return {
          title: category.title,
          count: new Intl.NumberFormat("en-US").format(value.count),
          percentage: value.percentage.toString(),
          bgColor: category.bgColor,
          link: category.link,
          customBgColor: category.customBgColor,
        };
      })
      .filter((item): item is TransformedCategoryData => item !== null);
  } catch (error) {
    console.error("Error transforming category data:", error);
    return [];
  }
};

// Update the transform function signature
const transformStatusData = (
  response: WebsiteStatusDataResponse
): TransformedCategoryData[] => {
  const statusMapping = {
    active_sites: {
      title: "Active Sites",
      bgColor: "bg-gradient-to-r from-green-100 to-green-200",
      link: "",
      customBgColor: "#22C55E",
    },
    inactive_sites: {
      title: "Inactive Sites",
      bgColor: "bg-gradient-to-r from-red-100 to-red-200",
      link: "",
      customBgColor: "#EF4444",
    },
    pending_sites: {
      title: "Pending Sites",
      bgColor: "bg-gradient-to-r from-orange-100 to-orange-200",
      link: "",
      customBgColor: "#F97316",
    },
  };

  try {
    const data = response.data;
    return Object.entries(data)
      .map(([key, value]) => {
        const category = statusMapping[key as keyof typeof statusMapping];
        if (!category) return null;

        return {
          title: category.title,
          count: new Intl.NumberFormat("en-US").format(value.count),
          percentage: `${value.percentage}%`,
          bgColor: category.bgColor,
          link: category.link,
          customBgColor: category.customBgColor,
        };
      })
      .filter((item): item is TransformedCategoryData => item !== null);
  } catch (error) {
    console.error("Error transforming status data:", error);
    return [];
  }
};

// Add new data configuration for the second chart
// Generate last 3 months and MTD for Top 3 Categories chart
const getLast3MonthsAndMTDLabels = () => {
  return ["Apr", "May", "Jun", "MTD"];
};

const websiteTypeLegendLabels = getLast3MonthsAndMTDLabels();
const websiteTypeColors = ["#274745", "#E8C468", "#147878", "#14B8A6"];
const websiteTypeConfig = websiteTypeLegendLabels.reduce(
  (acc, label, idx) => {
    acc[label] = {
      label,
      color: websiteTypeColors[idx % websiteTypeColors.length],
    };
  return acc;
  },
  {} as Record<string, { label: string; color: string }>
);

// Top 3 Platforms chart config: use same color palette and dynamic labels as Top 3 Categories
const socialMediaTypeLegendLabels = getLast3MonthsAndMTDLabels();
const socialMediaTypeColors = ["#274745", "#E8C468", "#147878", "#14B8A6"];
type LegendConfig = Record<string, { label: string; color: string }>;
const SocialMediaTypeConfig = socialMediaTypeLegendLabels.reduce<LegendConfig>(
  (acc, label, idx) => {
    acc[label] = {
      label,
      color: socialMediaTypeColors[idx % socialMediaTypeColors.length],
    };
  return acc;
  },
  {}
);


const Insights = () => {
  // const { startDate: fromDate, endDate: toDate } = useDateRange();
  const { selectedPackage } = usePackage();
  const [loading, setLoading] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<string[]>(["all"]);
  const [selectedPriority, setSelectedPriority] = useState<string[]>([
    "all",
  ]);
  const [selectedCountry, setSelectedCountry] = useState<string[]>(["all"]);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const cardRefs = useRef<HTMLElement[]>([]);
  const [takedownSamplesSearchTerm, setTakedownSamplesSearchTerm] =
    useState("");

  // Handle card expansion
  const handleExpand = (index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  };

  // Add CSV download function
  const handleCSVDownload = useCallback(async (apiDetail: string, cardTitle: string) => {
    try {
      setLoading(true);
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const getFilterArray = (selected: string[]) => {
        if (selected.length === 0 || (selected.length === 1 && selected.includes("All"))) {
          return ["All"];
        }
        return selected.filter(item => item !== "All");
      };

      const basePayload: any = {
        package_name: selectedPackage,
        // fromDate: fromDate,
        // toDate: toDate,
        country: getFilterArray(selectedCountry),
        priority: getFilterArray(selectedPriority),
        brand: getFilterArray(selectedBrand),
        export_type: "csv"
      };

      let endpoint = '';
      let payload: any = { ...basePayload };

      switch (apiDetail) {
        case "platforms_observed":
          endpoint = Endpoint.BI.SM_INSIGHTS.PLATFORMS_OBSERVED;
          break;
        case "incident_industry":
          endpoint = Endpoint.BI.SM_INSIGHTS.INCIDENT_INDUSTRY;
          break;
        case "takedown_trend":
          endpoint = Endpoint.BI.SM_INSIGHTS.TAKEDOWN_TREND;
          break;
        case "platform_trend":
          endpoint = Endpoint.BI.SM_INSIGHTS.PLATFORM_TREND;
          break;
        case "top_categories":
          endpoint = Endpoint.BI.SM_INSIGHTS.TOP_CATEGORIES;
          break;
        case "takedown":
          endpoint = Endpoint.BI.SM_INSIGHTS.TAKEDOWN;
          break;
        case "top_platforms":
          endpoint = Endpoint.BI.SM_INSIGHTS.TOP_PLATFORMS;
          break;
        case "top_handles":
          endpoint = Endpoint.BI.SM_INSIGHTS.TOP_HANDLES;
          break;
        // Add more cases as needed
        default:
          throw new Error(`Unknown API detail: ${apiDetail}`);
      }

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + endpoint,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const fileName = `${cardTitle}.csv`;
      const success = await handleCSVDownloadFromResponse(response, fileName);
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    } finally {
      setLoading(false);
    }
  }, [selectedPackage, selectedBrand, selectedPriority, selectedCountry]);

  // Add onExport function
  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];

      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs]
  );

  // Fetch filter data
  const { data: brandFilterData } = useQuery(
    ["brandFilter", selectedPackage],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
        Endpoint.BI.BI_FILTERS.replace(":col", "brand"),
        {
          package_name: selectedPackage,
          // fromDate,
          // toDate,
          menu:"social_media",
          insights:true
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  const { data: priorityFilterData } = useQuery(
    ["priorityFilter", selectedPackage],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
        Endpoint.BI.BI_FILTERS.replace(":col", "priority"),
        {
          package_name: selectedPackage,
          // fromDate,
          // toDate,
          menu:"social_media",
          insights:true
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  const { data: countryFilterData } = useQuery(
    ["countryFilter", selectedPackage],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
        Endpoint.BI.BI_FILTERS.replace(":col", "country"),
        {
          package_name: selectedPackage,
          // fromDate,
          // toDate,
          menu:"social_media",
          insights:true
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  // Transform filter data
  const brandFilters = useMemo(
    () =>
      (brandFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedBrand.includes(label) || selectedBrand.includes("all"),
      })),
    [brandFilterData?.data, selectedBrand]
  );

  const priorityFilters = useMemo(
    () =>
      (priorityFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedPriority.includes(label) ||
          selectedPriority.includes("all"),
      })),
    [priorityFilterData?.data, selectedPriority]
  );

  const countryFilters = useMemo(
    () =>
      (countryFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedCountry.includes(label) ||
          selectedCountry.includes("all"),
      })),
    [countryFilterData?.data, selectedCountry]
  );

  // Handle filter changes
  const handleFilterSubmit = (id: string, data: FilterState) => {
    switch (id) {
      case "brand":
        setSelectedBrand(
          data.is_select_all
            ? ["all"]
            : data.filters.filter((f) => f.checked).map((f) => f.label)
        );
        break;
      case "priority":
        setSelectedPriority(
          data.is_select_all
            ? ["all"]
            : data.filters.filter((f) => f.checked).map((f) => f.label)
        );
        break;
      case "country":
        setSelectedCountry(
          data.is_select_all
            ? ["all"]
            : data.filters.filter((f) => f.checked).map((f) => f.label)
        );
        break;
    }
  };

  // Handle filter search
  const handleFilterSearch = (id: string, query: string) => {
    console.log(`Searching ${id} with query: ${query}`);
  };

  const queryParams = useMemo(() => {
    const formatFilterValue = (filters: string[]) => {
      if (filters.length === 0 || (filters.length === 1 && filters.includes("all"))) {
        return ["all"];
      }
      if (filters.includes("all")) {
        return ["all"];
      }
      return filters.filter(item => item !== "all");
    };

    return {
      package_name: selectedPackage,
      brand: formatFilterValue(selectedBrand),
      priority: formatFilterValue(selectedPriority),
      country: formatFilterValue(selectedCountry),
    };
  }, [selectedBrand, selectedPriority, selectedCountry, selectedPackage]);

 
  const { data: takedownTrendApiData, isLoading: isTakedownTrendLoading } =
    useQuery(
      ["takedownTrend", queryParams],
      async () => {
        const idToken =
          localStorage.getItem("IdToken") ||
          localStorage.getItem("IDToken");
        if (!idToken) throw new Error("No authorization token found");
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST +
            Endpoint.BI.SM_INSIGHTS.TAKEDOWN_TREND,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              "Content-Type": "application/json",
            },
          }
        );
        return response.data;
      },
      {
        enabled: !!selectedPackage,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
      }
    );

  // Add API call for Platform Trend
  const { data: platformTrendApiData, isLoading: isPlatformTrendLoading } =
    useQuery(
      ["platformTrend", queryParams],
      async () => {
        const idToken =
          localStorage.getItem("IdToken") ||
          localStorage.getItem("IDToken");
        if (!idToken) throw new Error("No authorization token found");
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST +
            Endpoint.BI.SM_INSIGHTS.PLATFORM_TREND,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              "Content-Type": "application/json",
            },
          }
        );
        return response.data;
      },
      {
        enabled: !!selectedPackage,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
      }
    );

  

  // Add API call for Top Categories
  const { data: topCategoriesApiData, isLoading: isTopCategoriesLoading } =
    useQuery(
      ["topCategories", queryParams],
      async () => {
        const idToken =
          localStorage.getItem("IdToken") ||
          localStorage.getItem("IDToken");
        if (!idToken) throw new Error("No authorization token found");
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST +
            Endpoint.BI.SM_INSIGHTS.TOP_CATEGORIES,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              "Content-Type": "application/json",
            },
          }
        );
        return response.data;
      },
      {
        enabled: !!selectedPackage,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
      
      }
    );

  // Helper to generate a color palette for categories
  const categoryColorPalette = [
    "#E8C468",
    "#14B8A6",
    "#F59E0B",
    "#FDE68A",
    "#6366F1",
    "#F472B6",
    "#10B981",
    "#F87171",
    "#A78BFA",
    "#FBBF24",
  ];

  // Dynamically generate config for Top Categories
  // The API response structure has month values as legends (e.g., "MTD", "Jul", "Jun", "May")
  // Each category (like "Fake BS Handles", "Fake Job Promotions") becomes a stacked bar segment
  const topCategoriesKeys = React.useMemo(() => {
    const dataArr = topCategoriesApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'month' (which is the x-axis key)
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "month") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [topCategoriesApiData?.data]);

  const topCategoriesChartConfig = React.useMemo(() => {
    return topCategoriesKeys.reduce(
      (acc, key, idx) => {
        acc[key] = {
          label: key,
          color: categoryColorPalette[idx % categoryColorPalette.length],
        };
        return acc;
      },
      {} as Record<string, { label: string; color: string }>
    );
  }, [topCategoriesKeys]);

  // Add API call for Incident Industry Data
  const { data: incidentIndustryData, isLoading: isIncidentIndustryLoading } =
    useQuery(
      ["incidentIndustry", queryParams],
      async () => {
        const idToken =
          localStorage.getItem("IdToken") ||
          localStorage.getItem("IDToken");
        if (!idToken) throw new Error("No authorization token found");
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST +
            Endpoint.BI.SM_INSIGHTS.INCIDENT_INDUSTRY,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              "Content-Type": "application/json",
            },
          }
        );
        return response.data;
      },
      {
        enabled: !!selectedPackage,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
      }
    );

  // Helper to generate a color palette for incident industry
  const incidentIndustryColorPalette = [
    "#E8C468",
    "#14B8A6",
    "#F59E0B",
    "#FDE68A",
    "#6366F1",
    "#F472B6",
    "#10B981",
    "#F87171",
    "#A78BFA",
    "#FBBF24",
  ];

  // Transform API data for donut chart
  const donutLegends = React.useMemo(() => {
    if (!incidentIndustryData?.data) return [];
    
    // Sort data by value (count) in descending order
    const sortedData = [...incidentIndustryData.data].sort((a, b) => b.value - a.value);
    
    return sortedData.map((item: any, index: number) => ({
      label: item.label,
      color:
        incidentIndustryColorPalette[
          index % incidentIndustryColorPalette.length
        ],
    }));
  }, [incidentIndustryData?.data]);

  const donutData = React.useMemo(() => {
    if (!incidentIndustryData?.data) return [];
    
    // Sort data by value (count) in descending order
    const sortedData = [...incidentIndustryData.data].sort((a, b) => b.value - a.value);
    
    return sortedData.map((item: any, index: number) => ({
      label: item.label,
      value: item.value,
      fill: incidentIndustryColorPalette[
        index % incidentIndustryColorPalette.length
      ],
    }));
  }, [incidentIndustryData?.data]);

  // Add API call for Top Platforms Observed
  const { data: platformsObservedData, isLoading: isPlatformsObservedLoading } =
    useQuery(
      ["platformsObserved", queryParams],
      async () => {
        const idToken =
          localStorage.getItem("IdToken") ||
          localStorage.getItem("IDToken");
        if (!idToken) throw new Error("No authorization token found");
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST +
            Endpoint.BI.SM_INSIGHTS.PLATFORMS_OBSERVED,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              "Content-Type": "application/json",
            },
          }
        );
        return response.data;
      },
      {
        enabled: !!selectedPackage,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
      }
    );

  // Helper to generate a color palette
  const colorPalette = [
    "#E8C468",
    "#14B8A6",
    "#F59E0B",
    "#FDE68A",
    "#6366F1",
    "#F472B6",
    "#10B981",
    "#F87171",
    "#A78BFA",
    "#FBBF24",
  ];

  // Dynamically generate chartConfig for Platform Trend
  const platformTrendKeys = React.useMemo(() => {
    const dataArr = platformTrendApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'month'
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "month") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [platformTrendApiData?.data]);

  const platformTrendChartConfig = React.useMemo(() => {
    return platformTrendKeys.reduce(
      (acc, key, idx) => {
        acc[key] = {
          label: key,
          color: colorPalette[idx % colorPalette.length],
        };
        return acc;
      },
      {} as Record<string, { label: string; color: string }>
    );
  }, [platformTrendKeys]);



  // Add API call for Top Platforms
  const { data: topPlatformsApiData, isLoading: isTopPlatformsLoading } =
    useQuery(
      ["topPlatforms", queryParams],
      async () => {
        const idToken =
          localStorage.getItem("IdToken") ||
          localStorage.getItem("IDToken");
        if (!idToken) throw new Error("No authorization token found");
        const response = await axios.post(
          process.env.NEXT_PUBLIC_BI_TEST +
            Endpoint.BI.SM_INSIGHTS.TOP_PLATFORMS,
          queryParams,
          {
            headers: {
              Authorization: idToken,
              "Content-Type": "application/json",
            },
          }
        );
        return response.data;
      },
      {
        enabled: !!selectedPackage,
      }
    );

  // Helper to generate a color palette for platforms
  const topPlatformsColorPalette = [
    "#E8C468",
    "#14B8A6",
    "#F59E0B",
    "#FDE68A",
    "#6366F1",
    "#F472B6",
    "#10B981",
    "#F87171",
    "#A78BFA",
    "#FBBF24",
  ];

  // Dynamically generate config for Top Platforms
  const topPlatformsKeys = React.useMemo(() => {
    const dataArr = topPlatformsApiData?.data || [];
    if (!dataArr.length) return [];
    // Get all unique keys except 'label' (or 'platform', or whatever is the x-axis key)
    const keys = new Set<string>();
    dataArr.forEach((row: any) => {
      Object.keys(row).forEach((k) => {
        if (k !== "label" && k !== "platform") keys.add(k);
      });
    });
    return Array.from(keys);
  }, [topPlatformsApiData?.data]);

  const topPlatformsChartConfig = React.useMemo(() => {
    return topPlatformsKeys.reduce(
      (acc, key, idx) => {
        acc[key] = {
          label: key,
          color:
            topPlatformsColorPalette[idx % topPlatformsColorPalette.length],
        };
        return acc;
      },
      {} as Record<string, { label: string; color: string }>
    );
  }, [topPlatformsKeys]);

  // Map 'platform' to 'label' for the chart
  const topPlatformsChartData = React.useMemo(() => {
    if (!topPlatformsApiData?.data) return [];
    return topPlatformsApiData.data.map((item: any) => ({
      ...item,
      label: item.platform,
    }));
  }, [topPlatformsApiData?.data]);

  // Remove dummy topHandlesData and add API integration for Top Handles
  const { data: topHandlesApiData, isLoading: isTopHandlesLoading } = useQuery(
    ["topHandles", queryParams],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INSIGHTS.TOP_HANDLES,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
    }
  );

  const topHandlesData = React.useMemo(() => {
    if (!topHandlesApiData?.data) return [];
    
    // Always show exactly 3 cards, regardless of data quality
    return topHandlesApiData.data.slice(0, 3).map((item: any, index: number) => ({
      id: `${item.metric}-${index}`,
      label: item.metric,
      handle: item.handle && item.handle !== 0 ? item.handle : "-",
      platform: item.platform && item.platform !== 0 ? item.platform : "-",
      likes: item.likes && item.likes !== 0 ? item.likes : "0",
      followers: item.followers && item.followers !== 0 ? item.followers : "0",
    }));
  }, [topHandlesApiData?.data]);

  // API call for Takedown Samples
  const { data: takedownApiData, isLoading: isTakedownLoading } = useQuery(
    ["takedown", queryParams],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");
      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_INSIGHTS.TAKEDOWN,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
    }
  );

  // Transform takedown API data
  const takedownSamplesData = React.useMemo(() => {
    if (!takedownApiData?.data) return [];
    return takedownApiData.data.map((item: any) => ({
      category: item.category,
      details: item.details,
      date: item.date,
      threatType: item.threat_type,
      screenshotUrl: item.screenshot_url,
    }));
  }, [takedownApiData?.data]);

  // Sample data for Takedown Samples table
  const takedownSamplesColumns = [
    { title: "Category", key: "category" },
    { 
      title: "Details", 
      key: "details",
      render: (data: any) => {
        const truncateText = (text: string, maxLength: number = 12) => {
          if (text.length <= maxLength) return text;
          return text.substring(0, maxLength) + '...';
        };
        
        return (
          <span
            className="text-gray-700 block"
            title={data.details}
          >
            {truncateText(data.details, 12)}
          </span>
        );
      },
    },
    { title: "Date", key: "date" },
    { title: "Threat Type", key: "threatType" },
    {
      title: "Screenshot",
      key: "screenshot",
      render: (data: any) => (
        <div className="flex items-center justify-center w-full h-full">
          <button
            className="flex items-center justify-center w-8 h-8 transition-colors"
            onClick={() => window.open(data.screenshotUrl, "_blank")}
          >
            <Link className="w-4 h-4 text-purple-800 dark:text-white" />
          </button>
        </div>
      ),
    },
  ];



  return (
    <div className="min-h-screen w-full bg-[#F3F4F6] ">
      {/* Filters */}
      
      <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)]">
        {/* <div className="text-lg font-semibold text-gray-800 ">Social Media Insights</div> */}
        <div className="flex flex-wrap gap-3">
          <FilterPill
            id="brand"
            title="Brand"
            filters={brandFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={loading}
            isSearchable={true}
          />
          <FilterPill
            id="priority"
            title="Priority"
            filters={priorityFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={loading}
            isSearchable={true}
          />
          <FilterPill
            id="country"
            title="Country"
            filters={countryFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={loading}
            isSearchable={true}
          />
        </div>
      </div>

      <div className="flex flex-col m-2">
        {/* First Row - Categories and Categories Chart */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <Card
            ref={(el) => {
              if (el) cardRefs.current[0] = el;
            }}
            className="shadow-md h-full min-h-[180px] flex flex-col justify-between"
          >
            <HeaderRow
              title="Top Platforms Observed"
              onExpand={() => handleExpand(0)}
              onExport={() => onExport("png", "Top Platforms Observed", 0)}
              handleExport={() => handleCSVDownload("platforms_observed", "Top Platforms Observed")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="p-3 flex-1 flex flex-col justify-center">
              {isPlatformsObservedLoading ? (
                <div className="flex items-center justify-center w-full h-full">
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !platformsObservedData?.data ||
                platformsObservedData.data.length === 0 ? (
                <div className="flex items-center justify-center w-full h-full">
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full h-full">
                  {Array.isArray(platformsObservedData?.data) 
                    ? platformsObservedData.data
                        .sort((a: any, b: any) => b.percentage - a.percentage)
                        .slice(0, 2)
                        .map((platform: any, idx: number) => (
                      <div
                        key={platform.title || idx}
                        className={`flex flex-col w-full rounded-lg shadow-md border border-gray-100 p-3 items-center justify-center bg-purple-50/50 border-purple-200/70`}
                    >
                      <div className="flex justify-between items-center mb-2 w-full">
                        <span className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                            {platform.logo_url && (
                              <img
                                src={platform.logo_url}
                                alt={platform.title}
                                className="w-5 h-5 rounded-full"
                              />
                            )}
                            {platform.title}
                        </span>
                          {platform.percentage !== undefined && (
                        <span className="text-lg font-bold">
                              {platform.percentage}%
                        </span>
                          )}
                      </div>
                        <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden mb-1">
                          <div
                            className="bg-[#8B5CF6]"
                          style={{
                              width: `${platform.percentage}%`,
                            height: "100%",
                          }}
                        ></div>
                      </div>
                    </div>
                  ))
                    : []
                  }
              </div>
              )}
            </CardContent>
          </Card>

          <Card
            ref={(el) => {
              if (el) cardRefs.current[1] = el;
            }}
            className="shadow-md h-full min-h-[180px]"
          >
            <HeaderRow
              title="Top Handles"
              onExpand={() => handleExpand(1)}
              onExport={() => onExport("png", "Top Handles", 1)}
              handleExport={() => handleCSVDownload("top_handles", "Top Handles")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="h-[calc(100%-50px)] flex flex-col items-center p-3">
              {isTopHandlesLoading ? (
                <div className="flex items-center justify-center w-full h-full">
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-1 w-full px-1">
                  {topHandlesData.map((data: any) => {
                    let label = data.label;
                  let Icon = null;
                    if (label === "By Likes") {
                    Icon = ThumbsUp;
                    } else if (label === "By Followers") {
                    Icon = Users;
                  } else {
                    Icon = FileText;
                  }
                  return (
                    <div
                      key={data.id}
                      className="flex flex-col items-start justify-center p-3 bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 shadow-md w-full"
                    >
                      <div className="flex items-center gap-2 mb-2 w-full">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                          {Icon && <Icon className="w-4 h-4 text-primary" />}
                        </div>
                        <span className="text-sm font-semibold text-gray-800">
                          {label}
                        </span>
                      </div>

                      <div className="space-y-1.5 w-full">
                        <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500 font-medium">
                              Handle
                            </span>
                            <EllipsisTooltip content={data.handle || "-"} maxChars={10} className="text-xs text-gray-800 font-semibold max-w-[140px] text-right" />
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500 font-medium">
                              Platform
                            </span>
                            <span className="text-xs text-gray-800 font-semibold">
                              {data.platform}
                            </span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500 font-medium">
                              Likes
                            </span>
                            <span className="text-xs text-blue-600 font-bold">
                              {data.likes}
                            </span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500 font-medium">
                              Followers
                            </span>
                            <span className="text-xs text-green-600 font-bold">
                              {data.followers}
                            </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              )}
            </CardContent>
          </Card>
        </div>
        {/* Second Row - Bar Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2 shadow-md ">
          <Card
            ref={(el) => {
              if (el) cardRefs.current[2] = el;
            }}
            className="shadow-md min-h-[250px] max-h-[400px] h-full flex flex-col"
          >
            <HeaderRow
              title="Incidents In Industry Data (mFilterIt Tracked)"
              onExpand={() => handleExpand(2)}
              onExport={() => onExport("png", "Incidents In Industry Data", 2)}
              handleExport={() => handleCSVDownload("incident_industry", "Incidents In Industry Data")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="h-[calc(100%-50px)] w-full p-0">
              <div className="h-full w-full flex items-center justify-center">
                {isIncidentIndustryLoading ? (
                  <div className="flex items-center justify-center w-full h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !incidentIndustryData?.data ||
                  incidentIndustryData.data.length === 0 ? (
                  <div className="flex items-center justify-center w-full h-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <MFPieChartInteractive
                    legends={donutLegends}
                    data={donutData}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Platform Chart */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[3] = el;
            }}
            className="shadow-md min-h-[250px] max-h-[400px] h-full flex flex-col"
          >
            <HeaderRow
              title="Takedown Trend"
              onExpand={() => handleExpand(3)}
              onExport={() => onExport("png", "Takedown Trend", 3)}
              handleExport={() => handleCSVDownload("takedown_trend", "Takedown Trend")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="flex-1 flex flex-col p-0">
              <div className="flex-1 flex items-center justify-center w-full">
                <StackedBarWithLine
                  chartData={takedownTrendApiData?.data || []}
                  chartConfig={{
                    incident: { label: "Incidents", color: "#E8C468" },
                    takedown: { label: "Takedown", color: "#14B8A6" },
                  }}
                  onExpand={() => {}}
                  isLoading={isTakedownTrendLoading}
                  showTrendline={false}
                  barRadius={0}
                  isLegend={true}
                  chartHeight={350}
                  legendMarginTop={2}
                  legendHeight={40}
                  legendFontSize={12}
                  legendMarginBottom={10}
                  // chartHeight={300}
                  xAxisConfig={{
                    dataKey: "month",
                    tickLine: false,
                    tickMargin: 5,
                    axisLine: true,
                    angle: 0,
                    textAnchor: "middle",
                    dy: 5,
                    dx: 0,
                    height: 35,
                  }}
                  isStacked={true}
                  YAxis1={{
                    yAxisId: "left",
                    orientation: "left",
                    tickFormatter: (value) => value,
                  }}
                  barWidth={30}
                  
                />
              </div>
            </CardContent>
          </Card>

          {/* Replica of Incidents Overview Chart */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[4] = el;
            }}
            className="shadow-md min-h-[250px] max-h-[420px] h-full flex flex-col"
          >
            <HeaderRow
              title="Platform Trend"
              onExpand={() => handleExpand(4)}
              onExport={() => onExport("png", "Platform Trend", 4)}
              handleExport={() => handleCSVDownload("platform_trend", "Platform Trend")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
           <CardContent className="flex-1 flex flex-col p-0">
           <div className="flex-1 flex items-center justify-center w-full">
                <StackedBarWithLine
                  chartData={platformTrendApiData?.data || []}
                  chartConfig={platformTrendChartConfig}
                  onExpand={() => {}}
                  isLoading={isPlatformTrendLoading}
                  isStacked={true}
                  showTrendline={false}
                  barRadius={0}
                  barWidth={30}
                  // chartHeight={350}
                  legendFontSize={12}
                  isLegend={true}
                  chartHeight={300}
                  legendMarginTop={2}
                  legendHeight={40}
                  
                  
                  xAxisConfig={{
                    dataKey: "month",
                    tickLine: false,
                    tickMargin: 2,
                    axisLine: true,
                    angle: 0,
                    textAnchor: "middle",
                    dy: 5,
                    dx: 0,
                    height: 30,
                  }}
                  YAxis1={{
                    yAxisId: "left",
                    orientation: "left",
                    tickFormatter: (value) => `${value}`,
                  }}
                />
              </div>
            </CardContent>
          </Card>

          <Card
            ref={(el) => {
              if (el) cardRefs.current[5] = el;
            }}
            className="shadow-md min-h-[250px] max-h-[400px] h-full flex flex-col"
          >
            <HeaderRow
              title="Top Categories"
              onExpand={() => handleExpand(5)}
              onExport={() => onExport("png", "Top Categories", 5)}
              handleExport={() => handleCSVDownload("top_categories", "Top Categories")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="flex-1 flex flex-col p-0">
              <div className="flex-1 flex items-center justify-center w-full">
                {isTopCategoriesLoading ? (
                  <div className="flex items-center justify-center w-full h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !topCategoriesApiData?.data ||
                  topCategoriesApiData.data.length === 0 ? (
                  <div className="flex items-center justify-center w-full h-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <StackedBarWithLine
                    chartData={topCategoriesApiData.data}
                    chartConfig={topCategoriesChartConfig}
                    onExpand={() => {}}
                    isLoading={false}
                    isStacked={true}
                    showTrendline={false}
                    barRadius={4}
                    legendFontSize={12}
                    isLegend={true}
                    xAxisConfig={{
                      dataKey: "month",
                      tickLine: false,
                      tickMargin: 5,
                      axisLine: true,
                      angle: 0,
                      textAnchor: "middle",
                      dy: 5,
                      dx: 0,
                      height: 35,
                    }}
                    YAxis1={{
                      yAxisId: "left",
                      orientation: "left",
                      tickFormatter: (value) => value,
                    }}
                    barWidth={30}
                    chartHeight={350}
                    legendRows={1}
                    legendItemsPerRow={0}
                    legendGap={50}
                    legendMarginTop={10}
                    legendHeight={50}
                    legendMarginBottom={20}
                  />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Third Row Starts Here*/}
        <div className="grid grid-cols-1 md:grid-cols-2 mt-2 gap-2">
          <Card
            ref={(el) => {
              if (el) cardRefs.current[6] = el;
            }}
            className="shadow-md min-h-[400px] h-full flex flex-col"
          >
            <HeaderRow
              title="Takedown Samples"
              onExpand={() => handleExpand(6)}
              onExport={() => onExport("png", "Takedown Samples", 6)}
              handleExport={() => handleCSVDownload("takedown", "Takedown Samples")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="flex-1 p-0">
              <div className="h-full">
                {isTakedownLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin text-primary dark:text-white" />
                  </div>
                ) : takedownSamplesData.length === 0 ? (
                  <div className="text-sm dark:text-white flex items-center justify-center h-full ">
                    No Data Found !
                  </div>
                ) : (
                  <ResizableTable
                    columns={takedownSamplesColumns}
                    data={takedownSamplesData}
                    headerColor="#DCDCDC"
                    buttonTextName=""
                    isEdit={false}
                    isDelete={false}
                    isView={false}
                    isDownload={false}
                    isSearchable={true}
                    isSelectable={false}
                    isPaginated={true}
                    SearchTerm={takedownSamplesSearchTerm}
                    setSearchTerm={setTakedownSamplesSearchTerm}
                    height={250}
                  />
                )}
              </div>
            </CardContent>
          </Card>
          {/* Top Platforms */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[7] = el;
            }}
            className="shadow-md"
          >
            <HeaderRow
              title="Top Platforms"
              onExpand={() => handleExpand(7)}
              onExport={() => onExport("png", "Top Platforms", 7)}
              handleExport={() => handleCSVDownload("top_platforms", "Top Platforms")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="h-[350px] w-full p-0">
              <div className="h-full w-full">
                <DynamicBarChart
                  data={topPlatformsChartData}
                  config={topPlatformsChartConfig}
                  isHorizontal={false}
                  onExpand={() => {}}
                  isRadioButton={false}
                  isSelect={false}
                  isLoading={isTopPlatformsLoading}
                  formatterType="number"
                  position="top"
                  showHeader={false}
                  isScrollable={false}
                  barSize={20}
                  height={320}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Insights;
