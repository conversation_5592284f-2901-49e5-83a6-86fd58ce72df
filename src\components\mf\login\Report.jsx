import React, { useState } from 'react'
import "./report.css"

const Report = ({ onClose }) => {
  const [selected, setSelected] = useState('')

  return (
    <div className='report-popup'>
      <div id='ace'>
        <h1>Report an issue</h1>
        <h3>What area are you having problems with?</h3>
        <div id='a1'>
          <div id='o'>
            <h2>Area</h2>
            <select value={selected} onChange={(e) => setSelected(e.target.value)} className='drop1'>
              <option value="" disabled>Select an option</option>
              <option value="Option A">Option A</option>
              <option value="Option B">Option B</option>
              <option value="Option C">Option C</option>
            </select>
          </div>
          <div>
            <h2>Security Level</h2>
            <select value={selected} onChange={(e) => setSelected(e.target.value)} className='drop1'>
              <option value="" disabled>Select an option</option>
              <option value="Option A">Option A</option>
              <option value="Option B">Option B</option>
              <option value="Option C">Option C</option>
            </select>
          </div>
        </div>
        <div id='a2'>
          <h2>Subject</h2>
          <input type="text" placeholder='I need help with...' id='text1' />
        </div>
        <div id='a3'>
          <h2>Description</h2>
          <textarea
            id="text2"
            placeholder="Please include all information relevant to your issue"
            rows={2}
            onInput={(e) => {
              e.target.style.height = 'auto'
              e.target.style.height = e.target.scrollHeight + 'px'
            }}
          />
        </div>
        <div style={{textAlign: 'left'}}>
          <button onClick={onClose} id="close-btn">Cancel</button>
        </div>
      </div>
    </div>
  )
}

export default Report 