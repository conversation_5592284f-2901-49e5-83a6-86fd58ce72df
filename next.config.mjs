// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   output: "standalone",
//   reactStrictMode: false,
//   eslint: { ignoreDuringBuilds: true },
//   typescript: { ignoreBuildErrors: true },
//   images: {
//     remotePatterns: [
//       {
//         protocol: "https",
//         hostname: "infringementportalcontent.mfilterit.com",
//         pathname: "/**",
//       },
//     ],
//   },
//   async rewrites() {
//     return [
//       {
//         source: '/api/:path*',
//         destination: 'https://ri52x3pnnf.execute-api.us-west-2.amazonaws.com/dev/api/:path*',
//       },
//     ]
//   },
// };

// export default nextConfig;
// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   output: "standalone",
//   reactStrictMode: false,
//   eslint: { ignoreDuringBuilds: true },
//   typescript: { ignoreBuildErrors: true },
 
//   // 👇 Add basePath and assetPrefix for App Fraud
  // basePath: '/brand-infringement',
  // assetPrefix: '/brand-infringement',
 
//   images: {
//     remotePatterns: [
//       {    
//         protocol: "https",
//         hostname: "infringementportalcontent.mfilterit.com",
//         pathname: "/**",
//       },
//     ],
//   },  
// }
 
// export default nextConfig;
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
 
    // 👇 Add basePath and assetPrefix for App Fraud
    // basePath: '/brand-infringement',
    assetPrefix: '/brand-infringement',
 
  images: {
    remotePatterns: [
      {    
        protocol: "https",
        hostname: "infringementportalcontent.mfilterit.com",
        pathname: "/**",
      },
    ],
  },  
}
 
export default nextConfig;
 
