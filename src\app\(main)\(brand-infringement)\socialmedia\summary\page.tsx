"use client";
import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { FilterPill } from "@/components/mf/Filters/FilterPill";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { usePackage } from "@/components/mf/PackageContext";
import { StackedBarWithLine } from "@/components/mf/charts/StackedBarwithLine";
import HeaderRow from "@/components/mf/HeaderRow";
import { format } from "date-fns";
import { onExpand, handleCSVDownloadFromResponse } from "@/lib/utils";
import DonutChart from "@/components/mf/DonutChart";
import { XAxis, YAxis, Tooltip, ResponsiveContainer, Legend, Line, LineChart } from "recharts";
import { ScrollableLineChart } from "@/components/mf/charts/ScrollableLineChart";
import domToImage from "dom-to-image";
import { downloadURI } from "@/lib/utils";
import AttractiveCard from "@/components/ui/AttractiveCard";
import { useQuery } from 'react-query';
import axios, { AxiosRequestConfig } from 'axios';
import Endpoint from '@/common/endpoint';
import { Loader2 } from "lucide-react";
// Inline type for recharts tick props
type RechartsTickProps = {
  x?: number;
  y?: number;
  payload?: { value: string };
};
 
// Add type definitions
interface Filter {
  label: string;
  checked: boolean;
}
 
interface FilterState {
  is_select_all: boolean;
  selected_count: number;
  filters: Filter[];
}
 
interface IncidentStatsResponse {
  status: string;
  message: string;
  data: {
    total: number;
    active: {
      count: number;
      percentage: number;
    };
    in_progress: {
      count: number;
      percentage: number;
    };
    closed: {
      count: number;
      percentage: number;
    };
    closed_takedown_completed: {
      count: number;
      percentage: number;
    };
    closed_takedown_sticky_incident: {
      count: number;
      percentage: number;
    };
    closed_recommend_to_legal: {
      count: number;
      percentage: number;
    };
  };
}

interface SocialMediaStatusResponse {
  status: string;
  message: string;
  data: {
    website_type: string;
    reported_volume: number;
    unique: number;
    closure_percentage: number;
  }[];
}

interface CategoryShareResponse {
  status: string;
  message: string;
  data: {
    channel: string;
    count: number;
    percentage: number;
  }[];
}

interface CategoryTrendResponse {
  status: string;
  message: string;
  data: {
    date: string;
    unique: number;
    actual_incidents: number;
  }[];
}
 
 
interface TrafficTrendData {
  month: string;
  "Reported Volume": number;
  "Unique": number;
  "Closed": number; // This is now closure_percentage from API
}
 
interface TransformedCategoryData {
  [key: string]: string | number | undefined;
  label: string;
  visit: number;
  fill: string;
}
 
// Add these type definitions before the data declarations
interface CategoryConfig {
  label: string;
  color: string;
  fillOpacity: number;
}
 
 
interface SingleCategoryConfig {
  unique: CategoryConfig;
  incidents: CategoryConfig;
}
 
interface SingleCategoryData {
  date: string;
  unique: number;
  incidents: number;
}
 
const transformStatsData = (response: IncidentStatsResponse | undefined) => {
  // Use API response data only
  if (!response?.data) {
    return {
      Total: { count: 0, title: "Incidents Reported", color: "#540094" },
      Active: { count: 0, title: "Under Brand Review", color: "#FF0000" },
      InProgress: { count: 0, title: "Takedown Initiated", color: "#FFDB58" },
      Closed: { 
        count: 0, 
        title: "Closed Incidents", 
        color: "#00A86B",
        breakdown: [
          { label: "Taken Down", value: 0 },
          { label: "No Action Requested", value: 0 },
          { label: "Recommend to Legal", value: 0 }
        ]
      }
    };
  }

  const data = response.data;
 
  return {
    Total: {
      count: data?.total || 0,
      title: "Incidents Reported",
      color: "#540094" // Purple
    },
    Active: {
      count: data?.active?.count || 0,
      title: "Under Brand Review",
      color: "#FF0000"
    },
    InProgress: {
      count: data?.in_progress?.count || 0,
      title: "Takedown Initiated",
      color: "#FFDB58"
    },
    Closed: {
      count: data?.closed?.count || 0,
      title: "Closed Incidents",
      color: "#00A86B",
      breakdown: [
        {
          label: "Taken Down",
          value: data?.closed_takedown_completed?.count || 0
        },
        {
          label: "No Action Requested",
          value: data?.closed_takedown_sticky_incident?.count || 0
        },
        {
          label: "Recommend to Legal",
          value: data?.closed_recommend_to_legal?.count || 0
        }
      ]
    }
  };
};
 
// Function to transform Social Media Status API response to chart format
const transformSocialMediaStatusData = (response: SocialMediaStatusResponse | undefined): TrafficTrendData[] => {
  if (!response?.data) return [];

  return response.data.map(item => ({
    month: item.website_type,
    "Reported Volume": item.reported_volume,
    "Unique": item.unique,
    "Closed": item.closure_percentage
  }));
};

// Function to transform Category Share API response to chart format
const transformCategoryShareData = (response: CategoryShareResponse | undefined): TransformedCategoryData[] => {
  if (!response?.data) return [];

  return response.data.map(item => ({
    label: item.channel,
    visit: item.percentage, // Using only percentage from API
    value: item.percentage, // Adding value field for legend percentage display
    fill: getCategoryColor(item.channel)
  }));
};

// Color mapping for categories
const getCategoryColor = (category: string): string => {
  const colorMap: { [key: string]: string } = {
    "Suspicious Handles": "#540094",
    "Suspicious Offers": "#00C49F",
    "Job Promotions": "#FFBB28",
    "Customer Care Number": "#FF8042",
    "Sponsored Ad": "#8884d8",
    "Misleading Information": "#E54030"
  };

  return colorMap[category] || "#8884d8"; // Default color if not found
};

// Function to transform Category Trend API response to chart format
const transformCategoryTrendData = (response: CategoryTrendResponse | undefined): SingleCategoryData[] => {
  if (!response?.data) return [];

  return response.data.map(item => ({
    date: item.date,
    unique: item.unique,
    incidents: item.actual_incidents
  }));
};
 
const websiteChartConfig = {
  "Reported Volume": { label: "Reported Volume", color: "#4CAF50" },
  "Unique": { label: "Unique", color: "#E54030" },
  "Closed": { label: "Closed", color: "#540094" }
};

// Config for single category view
const singleCategoryConfig: SingleCategoryConfig = {
  unique: {
    label: "Unique",
    color: "#6366F1",
    fillOpacity: 0.4
  },
  incidents: {
    label: "Incidents",
    color: "#F59E0B",
    fillOpacity: 0.4
  }
};
 
const Summary = () => {
  const { selectedPackage } = usePackage();
  const { startDate: fromDate, endDate: toDate } = useDateRange();
  const [selectedBrand, setSelectedBrand] = useState<string[]>(["all"]);
  const [selectedPriority, setSelectedPriority] = useState<string[]>(["all"]);
  const [selectedCountry, setSelectedCountry] = useState<string[]>(["all"]);
  const [loading, setLoading] = useState(false);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  // Handle fullscreen exit to reset expandedCard state
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && !document.webkitFullscreenElement && 
          !document.mozFullScreenElement && !document.msFullscreenElement) {
        if (expandedCard !== null) {
          setExpandedCard(null);
        }
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [expandedCard]);
  const cardRefs = React.useRef<HTMLElement[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 1023);
    };
    
    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch filter data
  const { data: brandFilterData } = useQuery(
    ["brandFilter", selectedPackage],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
        Endpoint.BI.BI_FILTERS.replace(":col", "brand"),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  const { data: priorityFilterData } = useQuery(
    ["priorityFilter", selectedPackage],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
        Endpoint.BI.BI_FILTERS.replace(":col", "priority"),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  const { data: countryFilterData } = useQuery(
    ["countryFilter", selectedPackage],
    async () => {
      const idToken =
        localStorage.getItem("IdToken") || localStorage.getItem("IDToken");
      if (!idToken) throw new Error("No authorization token found");

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST +
        Endpoint.BI.BI_FILTERS.replace(":col", "country"),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu:"social_media"
        },
        {
          headers: {
            Authorization: idToken,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate,
    }
  );

  // Transform filter data
  const brandFilters = useMemo(
    () =>
      (brandFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedBrand.includes(label) || selectedBrand.includes("all"),
      })),
    [brandFilterData?.data, selectedBrand]
  );

  const priorityFilters = useMemo(
    () =>
      (priorityFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedPriority.includes(label) ||
          selectedPriority.includes("all"),
      })),
    [priorityFilterData?.data, selectedPriority]
  );

  const countryFilters = useMemo(
    () =>
      (countryFilterData?.data || []).map((label: string) => ({
        label,
        checked:
          selectedCountry.includes(label) ||
          selectedCountry.includes("all"),
      })),
    [countryFilterData?.data, selectedCountry]
  );
 
  // Helper function to format filter values
  // Always send as array - both "all" and selected filters
  const formatFilterValue = (filters: string[]) => {
    // If no filters are selected or only "all" is selected, return "all" as array
    if (filters.length === 0 || (filters.length === 1 && filters.includes("all"))) {
      return ["all"];
    }
    // If "all" is included along with other selections, send "all" as array
    if (filters.includes("all")) {
      return ["all"];
    }
    // Otherwise send selected filters as array
    return filters.filter(item => item !== "all");
  };

  // Query params for API calls (without category for CATEGORY_SHARE)
  const queryParams = useMemo(() => ({
    package_name: selectedPackage,
    fromDate: fromDate,
    toDate: toDate,
    brand: formatFilterValue(selectedBrand),
    priority: formatFilterValue(selectedPriority),
    country: formatFilterValue(selectedCountry)
    // No category field for CATEGORY_SHARE
  }), [selectedPackage, fromDate, toDate, selectedBrand, selectedPriority, selectedCountry]);

  // Query params for CATEGORY_TREND (with category based on selection)
  const categoryTrendParams = useMemo(() => ({
    package_name: selectedPackage,
    fromDate: fromDate,
    toDate: toDate,
    brand: formatFilterValue(selectedBrand),
    priority: formatFilterValue(selectedPriority),
    country: formatFilterValue(selectedCountry),
    category: selectedCategory || "all" // Use selected category or "all" if none selected
  }), [selectedPackage, fromDate, toDate, selectedBrand, selectedPriority, selectedCountry, selectedCategory]);

  // API call for incident stats
  const { data: incidentStatsResponse, isLoading: incidentStatsLoading } = useQuery<IncidentStatsResponse>(
    ['socialMediaStats', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_SUMMARY.STAT_CARDS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate
    }
  );

  // Use API data only
  const incidentStatsData = incidentStatsResponse;

  // API call for social media status
  const { data: socialMediaStatusResponse, isLoading: socialMediaStatusLoading } = useQuery<SocialMediaStatusResponse>(
    ['socialMediaStatus', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_SUMMARY.SM_STATUS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate
    }
  );

  // Transform the social media status data for the chart
  const socialMediaStatusData = transformSocialMediaStatusData(socialMediaStatusResponse);

  // API call for category share
  const { data: categoryShareResponse, isLoading: categoryShareLoading } = useQuery<CategoryShareResponse>(
    ['categoryShare', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_SUMMARY.CATEGORY_SHARE,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'], 
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate
    }
  );

  // Transform the category share data for the chart
  const categoryShareData = transformCategoryShareData(categoryShareResponse);

  // Calculate total count from API response for center display
  const totalCount = categoryShareResponse?.data?.reduce((sum, item) => sum + item.count, 0) || 0;

  // Format total count in full number format for display
  const formattedTotalCount = React.useMemo(() => {
    if (!totalCount) return "0";
    return totalCount.toLocaleString('en-US');
  }, [totalCount]);

  // API call for category trend (without category)
  const { data: categoryTrendResponse, isLoading: categoryTrendLoading } = useQuery<CategoryTrendResponse>(
    ['categoryTrend', categoryTrendParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.SM_SUMMARY.CATEGORY_TREND,
        categoryTrendParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers']
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage && !!fromDate && !!toDate
    }
  );

  // Transform the category trend data for the chart
  const categoryTrendData = transformCategoryTrendData(categoryTrendResponse);

  // CSV download function
  const handleCSVDownload = useCallback(async (apiDetail: string, cardTitle: string) => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      // Helper function to format filter values for CSV download
      const formatFilterValueForCSV = (filters: string[]) => {
        if (filters.length === 0 || (filters.length === 1 && filters.includes("all"))) {
          return ["all"];
        }
        if (filters.includes("all")) {
          return ["all"];
        }
        return filters.filter(item => item !== "all");
      };

      // Base payload for all API calls
      const basePayload: any = {
        package_name: selectedPackage,
        fromDate: fromDate,
        toDate: toDate,
        brand: formatFilterValueForCSV(selectedBrand),
        priority: formatFilterValueForCSV(selectedPriority),
        country: formatFilterValueForCSV(selectedCountry),
        export_type: "csv"
      };

      let endpoint = '';
      let payload: any = { ...basePayload };

      // Determine endpoint and payload based on apiDetail
      switch (apiDetail) {
        case "stat_cards":
          endpoint = Endpoint.BI.SM_SUMMARY.STAT_CARDS;
          break;
        case "social_media_status":
          endpoint = Endpoint.BI.SM_SUMMARY.SM_STATUS;
          break;
        case "category_share":
          endpoint = Endpoint.BI.SM_SUMMARY.CATEGORY_SHARE;
          break;
        case "category_trend":
          endpoint = Endpoint.BI.SM_SUMMARY.CATEGORY_TREND;
          payload = {
            ...basePayload,
            category: selectedCategory || "all"
          };
          break;
        default:
          throw new Error(`Unknown API detail: ${apiDetail}`);
      }

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + endpoint,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );

      // Use the utility function to handle CSV download
      const fileName = `${cardTitle}_${fromDate}_to_${toDate}.csv`;
      const success = await handleCSVDownloadFromResponse(response, fileName);
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    }
  }, [selectedPackage, fromDate, toDate, selectedBrand, selectedPriority, selectedCountry, selectedCategory]);
 
  // Handle filter changes
  const handleFilterSubmit = (id: string, data: FilterState) => {
    switch (id) {
      case "brand":
        setSelectedBrand(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case "priority":
        setSelectedPriority(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case "country":
        setSelectedCountry(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      default:
        break;
    }
  };
 
  // Handle filter search
  const handleFilterSearch = (id: string, query: string) => {
    console.log(`Searching ${id} with query: ${query}`);
  };
 
  // Handle card expansion
  const handleExpand = (index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  };
 
  // Channel click handler
  const handleCategorySelect = (segment: { label: string; value: number }) => {
    setSelectedCategory(segment.label);
  };
 
  // Add onExport function
  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];
 
      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs]
  );

 
  return (
    <div className=" w-full bg-[#F3F4F6] overflow-x-hidden">
 
      {/* Filters */}
      <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)]">
      {/* <div className="text-lg font-semibold text-gray-800">Social Media Summary</div> */}
      <div className="flex flex-wrap gap-3">
        <FilterPill
          id="brand"
          title="Brand"
          filters={brandFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={loading}
          isSearchable={true}
        />
        <FilterPill
          id="priority"
          title="Priority"
          filters={priorityFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={loading}
          isSearchable={true}
        />
        <FilterPill
          id="country"
          title="Country"
          filters={countryFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={loading}
          isSearchable={true}
        />
      </div>
      </div>
 
      <div className="flex flex-col overflow-x-hidden m-2">
        {/* Stats Cards and Social Media Status Row */}
        <div className="grid grid-cols-1 lg:grid-cols-10 gap-2 mb-2">
          {/* Stats Cards Column (30%) */}
          <div className="lg:col-span-3 space-y-2 h-full">
            {/* First Row - First Two Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {/* Total Incidents */}
              <AttractiveCard className="shadow-md"
                title="Incidents Reported"
                value={incidentStatsLoading ? <Loader2 className="h-6 w-6 animate-spin text-primary dark:text-white" /> : (() => {
                  try {
                    const transformed = transformStatsData(incidentStatsData);
                    return transformed?.Total?.count || 0;
                  } catch (error) {
                    return 0;
                  }
                })()}
                borderColor="#540094"
                valueColor="#540094"
                width="w-full"
                height="h-[120px]"
                titlePadding="mb-3"
                valueFontSize="text-2xl" // Increased size
                titleFontSize="text-base"
              />
              
              {/* Active Incidents */}
              <AttractiveCard className="shadow-md"
                title="Under Brand Review"
                value={incidentStatsLoading ? <Loader2 className="h-6 w-6 animate-spin text-primary dark:text-white" /> : (() => {
                  try {
                    const transformed = transformStatsData(incidentStatsData);
                    return transformed?.Active?.count || 0;
                  } catch (error) {
                    return 0;
                  }
                })()}
                borderColor="#FF0000"
                valueColor="#FF0000"
                width="w-full"
                height="h-[120px]"
                titlePadding="mb-3"
                valueFontSize="text-2xl" // Increased size
                titleFontSize="text-base"
              />
            </div>
            
            {/* Second Row - Takedown Initiated Card */}
            <div className="w-full">
              <AttractiveCard className="shadow-md"
                title="Takedown Initiated"
                value={incidentStatsLoading ? <Loader2 className="h-6 w-6 animate-spin text-primary dark:text-white" /> : (() => {
                  try {
                    const transformed = transformStatsData(incidentStatsData);
                    return transformed?.InProgress?.count || 0;
                  } catch (error) {
                    return 0;
                  }
                })()}
                borderColor="#FFDB58"
                valueColor="#FFDB58"
                width="w-full"
                height="h-[120px]"
                titlePadding="mb-3"
                valueFontSize="text-2xl" // Increased size
                titleFontSize="text-base"
              />
        </div>
 
            {/* Third Row - Closed Incidents Card */}
            <div className="w-full">
              <AttractiveCard className="shadow-md"
                title="Closed Incidents"
                value={incidentStatsLoading ? <Loader2 className="h-6 w-6 animate-spin text-primary dark:text-white" /> : (() => {
                  try {
                    const transformed = transformStatsData(incidentStatsData);
                    return transformed?.Closed?.count || 0;
                  } catch (error) {
                    return 0;
                  }
                })()}
                borderColor="#00A86B"
                valueColor="#00A86B"
                width="w-full"
                height="h-[180px]"
                titlePadding="mb-3"
                valueFontSize="text-2xl" // Increased size
                titleFontSize="text-base"
              >
                {incidentStatsData && transformStatsData(incidentStatsData)?.Closed?.breakdown && (
                  <div className="flex items-stretch justify-center w-full gap-1 mt-1">
                    <div className="flex flex-col items-center flex-1">
                      <span className="text-xs font-semibold text-black dark:text-white">{transformStatsData(incidentStatsData)?.Closed?.breakdown[0]?.label || "Taken Down"}</span>
                      <span className="text-sm text-orange-500 font-semibold">
                        {(Number(transformStatsData(incidentStatsData)?.Closed?.breakdown[0]?.value) || 0).toLocaleString('en-US')}
                      </span>
                    </div>
                    <div className="w-[1px] bg-gray-300 h-6 self-center"></div>
                    <div className="flex flex-col items-center flex-1">
                      <span className="text-xs font-semibold text-black dark:text-white">{transformStatsData(incidentStatsData)?.Closed?.breakdown[1]?.label || "No Action Requested"}</span>
                      <span className="text-sm text-green-600 font-semibold">
                        {(Number(transformStatsData(incidentStatsData)?.Closed?.breakdown[1]?.value) || 0).toLocaleString('en-US')}
                      </span>
                    </div>
                    <div className="w-[1px] bg-gray-300 h-6 self-center"></div>
                    <div className="flex flex-col items-center flex-1">
                      <span className="text-xs font-semibold text-black dark:text-white">{transformStatsData(incidentStatsData)?.Closed?.breakdown[2]?.label || "Recommend to Legal"}</span>
                      <span className="text-sm text-blue-600 font-semibold">
                        {(Number(transformStatsData(incidentStatsData)?.Closed?.breakdown[2]?.value) || 0).toLocaleString('en-US')}
                      </span>
                    </div>
                  </div>
                )}
              </AttractiveCard>
            </div>
          </div>
 
          {/* Social Media Status Chart Column (70%) */}
          <div className="lg:col-span-7 h-full shadow-md">
        <Card
          ref={(el) => {
            if (el) cardRefs.current[2] = el;
          }}
              className={`w-full h-full shadow-md rounded-lg bg-white gap-2 dark:bg-card dark:text-white text-header ${expandedCard === 2 ? 'h-[100vh]' : ''}`}
        >
          <CardHeader className="p-2">
            <HeaderRow
              title="Social Media Status"
              onExpand={() => handleExpand(2)}
              onExport={() => onExport("png", "Social Media Status", 2)}
              handleExport={() => handleCSVDownload("social_media_status", "Social Media Status")}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
              isExpanded={expandedCard === 2}
            />
          </CardHeader>
          <CardContent className={`p-1 ${expandedCard === 2 ? 'h-[calc(100vh-48px)]' : ''}`}>
            <div className={`w-full overflow-hidden ${expandedCard === 2 ? 'h-[calc(100vh-120px)]' : ''}`}>
              <StackedBarWithLine
                chartData={socialMediaStatusData}
                chartConfig={websiteChartConfig}
                isHorizontal={false}
                isLegend={true}
                isLoading={socialMediaStatusLoading}
                isStacked={false}
                barRadius={0}
                barWidth={isMobile ? 30 : 45}
                chartHeight={isMobile ? 250 : 350}
                xAxisConfig={{
                  dataKey: "month",
                  angle: -45,
                  textAnchor: "end",
                  tickMargin: 8,
                  dy: 5,
                  // Custom tick renderer for tooltip on truncated/wrapped text
                  tick: (props: RechartsTickProps) => {
                    const { x, y, payload } = props;
                    const value = payload?.value || '';
                    const displayValue = value.length > 12 ? value.slice(0, 12) + '...' : value;
                    return (
                      <g transform={`translate(${x},${y})`}>
                        <text
                          x={0}
                          y={0}
                          dy={16}
                          textAnchor="end"
                          fill="#666"
                          fontSize={10}
                          style={{ cursor: 'pointer' }}
                        >
                          <title>{value}</title>
                          {displayValue}
                        </text>
                      </g>
                    );
                  },
                  tickFormatter: (value) => value.length > 12 ? value.slice(0, 12) + '...' : value
                }}
                YAxis1={{
                  yAxisId: "left",
                  orientation: "left",
                  tickFormatter: (value: string) => Number(value).toLocaleString('en-US')
                }}
                YAxis2={{
                  yAxisId: "right",
                  orientation: "right",
                  tickFormatter: (value: string) => `${Number(value)}%`
                }}
                onExpand={() => handleExpand(2)}
                isExpanded={expandedCard === 2}
              />
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
 
        {/* Category Distribution Donut Chart and Area Chart/Placeholder */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 m-1 shadow-md">
          {/* Category Share Donut Chart */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[1] = el;
            }}
            className="w-full shadow-md rounded-lg bg-white dark:bg-card dark:text-white text-header"
          >
            <CardContent className="w-full min-h-[300px] pl-5">
              <DonutChart
                chartData={categoryShareData}
                dataKey="visit"
                nameKey="label"
                chartConfig={categoryShareData.reduce(
                  (acc: any, cur: any) => ({ ...acc, [cur.label]: { label: cur.label, color: cur.fill } }),
                  {}
                )}
                isView={true}
                isLoading={categoryShareLoading}
                isPercentage={true}
                direction="flex-col"
                isdonut={expandedCard === 1}
                marginTop="mt-0"
                position="items-start"
                onSegmentClick={handleCategorySelect}
                selectedSegment={selectedCategory}
                onExpand={() => handleExpand(1)}
                isExpanded={expandedCard === 1}
                title="Category Share"
                hideScrollbar={true}
                legendsTitle="Select Categories For Trendline"
                legendsTitleFontSize="0.75rem"
                legendFontSize={11}
                innerRadius={45}
                outerRadius={80}
                totalV={-1}
                centerValue={formattedTotalCount}
                centerLabel="Total Count"
                showRefresh={true}
                titleFontSize="text-base"
                formatterType="percentage"
                onRefresh={() => {
                  setSelectedCategory(null);
                  // Here you would typically fetch fresh data
                  console.log("Refreshing category data...");
                  // Example: refetchCategoryData();
                }}
                onExport={() => onExport("png", "Category Share", 1)}
                handleExport={() => handleCSVDownload("category_share", "Category Share")}
              />
            </CardContent>
          </Card>
 
          {/* Area Chart */}
          <Card
            ref={(el) => {
              if (el) cardRefs.current[3] = el;
            }}
            className={`w-full shadow-md rounded-lg bg-white dark:bg-card dark:text-white text-header ${expandedCard === 3 ? 'h-[100vh]' : ''}`}
          >
            <CardHeader className="p-2">
              <HeaderRow
                title={selectedCategory ? `${selectedCategory} Trend` : "Categories Trend"}
                onExpand={() => handleExpand(3)}
                onExport={() => onExport("png", selectedCategory ? `${selectedCategory} Trend` : "Categories Trend", 3)}
                handleExport={() => handleCSVDownload("category_trend", "Categories Trend")}
                isRadioButton={false}
                isSelect={false}
                titleFontSize="text-base"
                isExpanded={expandedCard === 3}
              />
            </CardHeader>
            <CardContent className={`w-full min-h-[250px] ${expandedCard === 3 ? 'h-[calc(100vh-48px)]' : ''}`}>
              {categoryTrendLoading ? (
                <div className={`flex items-center justify-center w-full ${expandedCard === 3 ? 'h-[calc(100vh-120px)]' : 'h-[250px]'}`}>
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : categoryTrendData.length > 0 ? (
                                <ScrollableLineChart
                  data={categoryTrendData}
                  lines={(() => {
                    const selectedColor = selectedCategory && categoryShareData.find(item => item.label === selectedCategory)?.fill
                      ? categoryShareData.find(item => item.label === selectedCategory)?.fill || singleCategoryConfig.unique.color
                      : singleCategoryConfig.unique.color;
                    
                    // Create lighter and darker shades of the selected color
                    const getColorShades = (hex: string) => ({
                      light: hex + "80", // ~50% opacity (lighter)
                      dark: hex + "FF"    // 100% opacity (brightest)
                    });
                    
                    const shades = getColorShades(selectedColor);
                    
                    return [
                      {
                        dataKey: "unique",
                        name: "Unique",
                        stroke: shades.dark,
                        strokeWidth: 3,
                        fill: shades.dark,
                        fillOpacity: 0.3,
                        dot: false,
                        activeDot: { r: 6, stroke: shades.dark, strokeWidth: 2, fill: shades.dark }
                      },
                      {
                        dataKey: "incidents",
                        name: "Incidents",
                        stroke: shades.light,
                        strokeWidth: 3,
                        fill: shades.light,
                        fillOpacity: 0.3,
                        dot: false,
                        activeDot: { r: 6, stroke: shades.light, strokeWidth: 2, fill: shades.light }
                      }
                    ];
                  })()}
                  height={expandedCard === 3 ? window.innerHeight - 120 : 250}
                  enableHorizontalScroll={categoryTrendData.length > 20}
                  margin={{ top: 10, right: 30, left: 0, bottom: 20 }}
                  isAreaChart={true}
                />
              ) : (
                <div className={`flex items-center justify-center w-full ${expandedCard === 3 ? 'h-[calc(100vh-120px)]' : 'h-[250px]'}`}>
                  <span className="text-sm">No Data Found !</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
 
export default Summary;