"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>Axis, <PERSON>Axis, Label, ResponsiveContainer} from "recharts"
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import HeaderRow from "./HeaderRow";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatValue } from "@/lib/utils";
import { InformationCard } from "./InformationCard";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useFullscreen } from "@/hooks/fullscreen";


interface ChartBarStackedProps {
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  selectedType?: string;
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  heading?:string
  isInformCard?:boolean;
  layoutDirection?:string;
  isLegend?:boolean;
  ischangeLegend?:boolean;
  placeholder?:string;
  isLoading?:boolean;
  selectoptions?:string[];
  selectedFrequency?:string;
  handleFrequencyChange?: (value: string) => void; 
  isCartesian?:boolean;
  //sub_heading?:string
 InformCard?:{title:string,desc:string}[];
  chartData?: 
    {label: string;
    [key: string]: string | number;}[]
    chartConfig?: {
      [key: string]: {
        label: string;
        color: string;
      };
    };
  xAxis?: {
    dataKey: string
    title: string
    tickFormatter?: (value: string | number) => string  

  }
  yAxis?: {
    dataKey: string
    title?: string
    tickFormatter?: (value: string ) => string
  }
  isHorizontal?: boolean;
  AxisLabel?:string;
  barSize?: number;
  barGap?: number;
  tickMargin?: number;
  margin?: {
    left?: number;
    right?: number;
    top?: number;
    bottom?: number;
  };
  titleFontSize?: string;
  showMenuDots?: boolean;
  legendFontSize?: string;
  labelTruncateLength?: number;
  yAxisFontSize?: string;
  isExpanded?: boolean; // New prop for fullscreen state
}

interface CustomTickProps {
  x: number;
  y: number;
  payload: {
    value: string;
  };
  chartConfig: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
}

 const ChartBarStacked:React.FC<ChartBarStackedProps> = ({ 
  heading ="heading",
  //sub_heading,
    handleTypeChange,
    visitEventOptions,
    isCartesian,
    selectedType,
    handleExport,
    onExport,
    selectoptions =[],
    onExpand,
    handleFrequencyChange,
    title ,
    isSelect= false,
    isRadioButton =false,
    chartData=[],
    chartConfig,
    xAxis,
    yAxis,
    selectedFrequency,
    placeholder,
    isHorizontal,
    AxisLabel= "Value",
    InformCard=[],
    isInformCard=false,
    layoutDirection ="flex-col",
    isLegend=true,
    ischangeLegend=false,
    isLoading,
    barSize = 20,
    barGap = 30,
    tickMargin = 12,
    margin = { left: 20, right: 25, top: 10, bottom: 20 },
    titleFontSize,
    showMenuDots = true,
    legendFontSize = "12px",
    labelTruncateLength,
    yAxisFontSize = "10px",
    isExpanded = false, // New prop for fullscreen state
  }) => {
    const isFullscreen = useFullscreen();
    
    // Function to get chart height based on fullscreen state
    const getFullscreenChartHeight = () => {
      if (isExpanded || isFullscreen) {
        return Math.max(window.innerHeight - 120, 400);
      }
      return Math.min(chartData.length > 0 ? Math.min(chartData.length * 20, 500) : 300, 500);
    };

    // Function to get bar size based on fullscreen state
    const getFullscreenBarSize = () => {
      if (isExpanded || isFullscreen) {
        return Math.max(barSize * 1.5, 30);
      }
      return barSize;
    };

    // Function to get bar gap based on fullscreen state
    const getFullscreenBarGap = () => {
      if (isExpanded || isFullscreen) {
        return Math.max(barGap * 1.2, 40);
      }
      return barGap;
    };

    const chartHeight = getFullscreenChartHeight();
    const currentBarSize = getFullscreenBarSize();
    const currentBarGap = getFullscreenBarGap();

    const CustomTick = ({ x, y, payload, chartConfig }: CustomTickProps) => {
      const label = chartConfig[payload.value]?.label || payload.value;
      const truncateLength = labelTruncateLength ?? 8;
    
      return (
        <g transform={`translate(${x},${y})`}>
          <title>{label}</title>
          <text
            x={0}
            y={0}
            dy={4}
            textAnchor="end"
            fontSize={yAxisFontSize}
            className="truncate w-24"
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              width: "60px",
            }}
          >
            {label.length > truncateLength ? `${label.slice(0, truncateLength)}...` : label}
          </text>
        </g>
      );
    };
    
   
   
  return (
   <Card className="border-none">
   <HeaderRow
      visitEventOptions={visitEventOptions}
      handleTypeChange={handleTypeChange}
      selectoptions={selectoptions}
      selectedType={selectedType}
      title={title}
      handleFrequencyChange={handleFrequencyChange}
      selectedFrequency={selectedFrequency}
      onExpand={onExpand}
      handleExport={handleExport}
      isRadioButton={isRadioButton}
      isSelect={isSelect}
      onExport={onExport}
      heading={heading}
      placeholder={placeholder}
      titleFontSize={titleFontSize}
      showMenuDots={showMenuDots}
/>
{isInformCard &&(
  <div className="flex-1 px-4 flex flex-row">
  {InformCard?.map((item, index) => (
        <InformationCard
          key={index}
          InformTitle={item.title}
          informDescription={item.desc}
        />
      ))}
  </div>
    )}
{/* <CardHeader>
  <CardTitle>
  </CardTitle>
</CardHeader> */}
 {isLoading ? (
      <div className="flex items-center justify-center h-[300px] sm:h-[300px] lg:h-[4
      00px]">
            <Loader2 className=" h-8 w-8 animate-spin text-primary dark:text-white" />
       </div>
     ):(
<CardContent className="w-full h-[300px] overflow-hidden scrollbar p-0">
  {chartData.length>0 ?(
  <div className="flex flex-col w-full ">
    {/* Chart Container */}
    <div className="flex flex-col w-full ">
      <ChartContainer config={chartConfig} style={{ height: "300px",width: "90%" }}>
         <ResponsiveContainer height={150} width="100%"> 
          <BarChart
            data={chartData}
            layout={isHorizontal ? 'horizontal' : 'vertical'}
            margin={margin}
            barSize={currentBarSize}
            barGap={currentBarGap}
            height={chartHeight} 
          >
            {isCartesian &&(
             
            <CartesianGrid strokeDasharray="2 2" stroke="#555" strokeWidth={0.5} horizontal={isHorizontal} vertical={!isHorizontal} />
            )}
            <XAxis className="text-small-font"
              dataKey={isHorizontal ? xAxis?.dataKey : undefined}
              type={isHorizontal ? 'category' : 'number'}
              tickLine={false} 
              axisLine={true}
              tickFormatter={isHorizontal 
                ? undefined 
                : xAxis?.tickFormatter || ((value) => {
                    return `${value}%`;
                  })
              }
              interval={0}
              angle={isHorizontal ? -45 : 0}
              textAnchor={isHorizontal ? 'end' : 'start'}
              height={isHorizontal ? 80 : 30}
              style={{ fontSize: '10px' }}
            >
              {isHorizontal && <Label style={{fontSize:'10px'}} value={xAxis?.title} offset={-20} position="insideBottom" />}
            </XAxis>
            {yAxis && (
              <YAxis
                className="text-body"
                dataKey={isHorizontal ? undefined : yAxis.dataKey}
                type={isHorizontal ? 'number' : 'category'}
                tickLine={false}
                axisLine={true}
                tickFormatter={isHorizontal 
                  ? (value: number) => `${(value * 1).toFixed(0)}%`
                  : (value: string) => {
                      return value.length > 10 ? value.substring(0, 5) + "..." : value;
                    }
                }  
                width={isHorizontal ? 50 : 80}
                tickMargin={tickMargin}
                interval={0}
                height={isHorizontal ? 80 : 500}
                tick={<CustomTick chartConfig={chartConfig || {}} />}
                style={{ fontSize: '10px' }}
              >
                {!isHorizontal && <Label style={{fontSize:'10px'}} value={yAxis.title} angle={-90} position="left" offset={-10} />}
              </YAxis>
            )}
            <ChartTooltip content={<ChartTooltipContent />} />
            {isLegend && (
              <ChartLegend
                content={({ payload, verticalAlign }) => (
                  <div
                    className={cn(
                      "flex items-center justify-center gap-4",
                      verticalAlign === "top" ? "pb-3" : "pt-3",
                    )}
                    style={{ fontSize: legendFontSize }}
                  >
                    {payload?.map((item) => {
                      const key = item.dataKey || "value";
                      const itemConfig = chartConfig?.[key];

                      return (
                        <div
                          key={item.value}
                          className={cn(
                            "flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground",
                          )}
                        >
                          <div
                            className="h-3 w-3 shrink-0 rounded-full"
                            style={{
                              backgroundColor: item.color,
                            }}
                          />
                          {itemConfig?.label || item.value}
                        </div>
                      );
                    })}
                  </div>
                )}
              />
            )}
            {/* Chart Bars */}
            {chartConfig &&
              Object.keys(chartConfig).map((key) => (
                <Bar key={key} dataKey={key} stackId="a" fill={chartConfig[key].color} />
              ))}
              
          </BarChart>
         </ResponsiveContainer> 
      </ChartContainer>
    </div>

    {/* Legend Container Below or Side Based on layoutDirection */}
    {ischangeLegend && (
     <div className="grid grid-cols-5 w-full p-0"> {/* 5 columns with a gap */}
     {chartConfig &&
       Object.keys(chartConfig).map((key) => (
         <div key={key} className="flex items-center">
           <div
             className="w-3 h-3 mr-2 rounded-full"
             style={{ backgroundColor: chartConfig[key].color }}
           ></div>
           <span style={{ fontSize: legendFontSize }}>{chartConfig[key].label}</span>
         </div>
       ))}
   </div>
    )}
  </div>
 
  ):( <div className="flex items-center justify-center w-full h-full min-h-[300px]">
    <span className="text-sm dark:text-white">No Data Found !</span>
  </div>)}
</CardContent>
     )}
    </Card>
  );
}
export default ChartBarStacked
