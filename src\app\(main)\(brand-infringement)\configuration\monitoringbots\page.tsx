"use client";

import React, { useEffect, useState } from "react";
import CardwithSwitch from "@/components/mf/CardwithSwitch";
import { Card, CardContent } from "@/components/ui/card";
import DeleteDialog from "@/components/ui/DeleteDialog";
import ToastContent from "@/components/mf/ToastContent";

// Platform data for each category
const platformData = {
  "Social Media ": [
    "Facebook", "X", "Instagram", "Pinterest", "YouTube", "LinkedIn", "Aguea", "Dumpor", 
    "Lightbrd", "Linktree", "Nitter", "Pinterest", "ShareChat", "Snapchat", "Threads", "TikTok"
  ],
  "Instant Messaging": [
    "Telegram", "WhatsApp"
  ],
  "Search Engines": [
    "Google", "Bing", "Yandex", "Yahoo", "Excavator", "Forms", "Light Search", 
    "Onion Centre", "Onion Search", "Torch"
  ],
  "Blogging Sites": [
    "About.me", "Blogspot", "Consumercompla", "intboard", "Is<PERSON>u", "Medium", "Mouthshut", 
    "<PERSON>jo<PERSON>", "<PERSON>uku", "Quora", "Reddit", "Redlib Tiekoetter", "Trustpilot", "Tumblr", 
    "Wattpad", "XCancel"
  ],
  "Job Sites": [
    "Naukri", "Indeed", "Shine", "Apna", "BeBee", "Behance", "CareerJet", "Expertini", 
    "FoundIt", "Freshersworld", "Glassdoor", "IIMJobs", "Instahyre", "Jobhai", "Jobsora", "Kitjob"
  ],
  "Market Places": [
    "IndiaMart", "Justdial", "OLX", "Quickr", "Sulekha"
  ],
  "App Store": [
    "Apkmonk", "Apkpure", "Apkvenus", "Appchive", "Appteka", "Filehippo", "Popsilla", 
    "Programy", "Softonic", "Updatestar", "9App Store"
  ]
};

// Platforms that should be underlined (reddish-brown color)
const underlinedPlatforms = [
  "Aguea", "Linktree", "About.me", "Issuu", "Mouthshut", "Nojoto", "Nuku", 
  "Redlib Tiekoetter", "Shine", "Freshersworld", "IIMJobs", "Instahyre", 
  "Filehippo", "Popsilla", "Programy", "Updatestar"
];

const getInitialDummyData = (category: string) => {
  const platforms = platformData[category as keyof typeof platformData] || [];
  return platforms.map((platform: string) => ({
    rule_name: platform,
    enabled: Math.random() > 0.7, // Most platforms enabled by default
    isUnderlined: underlinedPlatforms.includes(platform)
  }));
};

// Define types for better TypeScript support
interface RuleData {
  rule_name: string;
  enabled: boolean;
  isUnderlined?: boolean;
}

interface ToastData {
  type: "success" | "error" | "info" | "warning";
  title: string;
  description: string;
  variant: "default" | "destructive" | null;
}

interface PendingToggle {
  index: number;
  newStatus: boolean;
  type: string;
}

function MonitoringBots() {
  const [whiteListData, setWhiteListData] = useState<RuleData[] | null>(null);
  const [currentRuleName, setCurrentRuleName] = useState("");
  const [loadingIndex, setLoadingIndex] = useState<number | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [toastData, setToastData] = useState<ToastData | null>(null);
  const [pendingToggle, setPendingToggle] = useState<PendingToggle | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("Social Media ");

  const handleSwitchChange = (index: number, checked: boolean) => {
    const currentRule = whiteListData?.[index];
    if (!currentRule) return;
    setCurrentRuleName(currentRule.rule_name);
    setPendingToggle({ index, newStatus: checked, type: "whitelist" });
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (!pendingToggle || !whiteListData) return;

    const { index, newStatus } = pendingToggle;
    const updatedRules = [...whiteListData];
    const currentRule = updatedRules[index];

    setCurrentRuleName(currentRule.rule_name);
    updatedRules[index].enabled = newStatus;
    setWhiteListData(updatedRules);
    setLoadingIndex(index);
    setIsSubmitting(true);

    setTimeout(() => {
      setToastData({
        type: "success",
        title: "Success",
        description: `${currentRule.rule_name} successfully ${newStatus ? "enabled" : "disabled"}`,
        variant: "default",
      });
      setLoadingIndex(null);
      setIsSubmitting(false);
      setDeleteDialogOpen(false);
      setPendingToggle(null);
    }, 500);
  };

  useEffect(() => {
    setWhiteListData(getInitialDummyData("Social Media "));
  }, []);

  // Update data when tab changes
  useEffect(() => {
    if (activeTab) {
      setWhiteListData(getInitialDummyData(activeTab));
    }
  }, [activeTab]);

  // Render different content based on active tab
  const renderTabContent = () => {
    return (
      <CardwithSwitch
        Title={activeTab}
        Sub_title={whiteListData?.map(rule => rule.rule_name)}
        enabledStates={whiteListData?.map(rule => rule.enabled)}
        onSwitchChange={handleSwitchChange}
        loadingIndex={loadingIndex ?? undefined}
        isSelect={false}
        isLoading={!whiteListData}
      />
    );
  };

  const tabs = ["Social Media ", "Instant Messaging", "Search Engines", "Blogging Sites", "Job Sites", "Market Places", "App Store"];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Responsive Tab Navigation */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
          {/* Mobile: Scrollable horizontal tabs */}
          <div className="flex overflow-x-auto scrollbar-hide sm:hidden">
            <div className="flex space-x-1 p-2 min-w-max">
              {tabs.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-4 py-3 rounded-lg text-sm font-semibold transition-colors whitespace-nowrap flex-shrink-0 ${
                    activeTab === tab
                      ? "bg-primary dark:bg-gray-300 text-white dark:text-black shadow-md"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {/* Desktop: Grid layout for tabs */}
          <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-1 p-2">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-3 py-3 rounded-lg text-sm font-semibold transition-colors ${
                  activeTab === tab
                    ? "bg-primary dark:bg-gray-300 text-white dark:text-black shadow-md"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                }`}
              >
                <span className="block truncate">{tab}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Main Content Card */}
        <Card className="shadow-lg rounded-xl overflow-hidden bg-white dark:bg-gray-800">
          <CardContent className="p-4 sm:p-6 lg:p-8">
            {toastData && (
              <div className="mb-4">
                <ToastContent
                  type={toastData.type}
                  title={toastData.title}
                  description={toastData.description}
                  variant={toastData.variant}
                />
              </div>
            )}
            
            <div className="min-h-[400px] sm:min-h-[500px] lg:min-h-[600px]">
              {renderTabContent()}
            </div>

            <DeleteDialog
              open={deleteDialogOpen}
              onOpenChange={setDeleteDialogOpen}
              onConfirm={confirmDelete}
              title={`Confirm ${pendingToggle?.newStatus ? "Activate" : "Deactivate"}?`}
              description={`Are you sure you want to ${pendingToggle?.newStatus ? "activate" : "deactivate"} ${currentRuleName}?`}
              button_no="No"
              button_yes="Yes"
              isSubmitting={isSubmitting}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default MonitoringBots;
 