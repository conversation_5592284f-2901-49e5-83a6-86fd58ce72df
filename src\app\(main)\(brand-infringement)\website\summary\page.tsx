"use client";
import React, { useState, useMemo, use<PERSON><PERSON>back, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { FilterPill } from "@/components/mf/Filters/FilterPill";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { usePackage } from "@/components/mf/PackageContext";
import Endpoint from "@/common/endpoint";
import { StackedBarWithLine } from "@/components/mf/charts/StackedBarwithLine";
import HeaderRow from "@/components/mf/HeaderRow";
import { format } from "date-fns";
import { formatNumber, onExpand, handleCSVDownloadFromResponse } from "@/lib/utils";
import DonutChart from "@/components/mf/DonutChart";
import { Area, AreaChart, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";
import DynamicBar<PERSON>hart from "@/components/mf/DynamicBarChart";
import ChartBarStacked from "@/components/mf/stackedBarChart";
import ScrollableChartContainer from "@/components/mf/ScrollableChartContainer";
import KeyValueCard from "@/components/mf/keyvalueCard";
import domToImage from "dom-to-image";
import { downloadURI } from "@/lib/utils";
import { useQuery } from "react-query";
import axios, { AxiosRequestConfig } from "axios";
import { Loader2 } from "lucide-react";

// Add type definitions
interface Filter {
  label: string;
  checked: boolean;
}

interface FilterState {
  is_select_all: boolean;
  selected_count: number;
  filters: Filter[];
}

interface IncidentStatsResponse {
  data: {
    total: number;
    active: {
      count: number;
      percentage: number;
    };
    in_progress: {
      count: number;
      percentage: number;
    };
    closed: {
      count: number;
      percentage: number;
    };
    takedown: {
      count: number;
      percentage: number;
    };
    no_action_required: {
      count: number;
      percentage: number;
    };
  };
  status: boolean;
}

interface TrafficTrendData {
  month: string;
  "Reported Volume": number;
  "Unique": number;
  "Closed": number;
}

// API response interface for Web App Status
interface WebAppStatusApiResponse {
  status: string;
  message: string;
  data: {
    website_type: string;
    reported_volume: number;
    unique: number;
    closure_percentage: number;
  }[];
}

// API response interface for Category Share
interface CategoryShareApiResponse {
  status: string;
  message: string;
  data: {
    channel: string;
    count: number;
    percentage: number;
  }[];
  total_count: number; // Total count from API
}

// API response interface for Category Trend
interface CategoryTrendApiResponse {
  status: string;
  message: string;
  data: {
    date: string;
    unique: number;
    actual_incidents: number;
  }[];
}

// API response interface for Customer Care
interface CustomerCareApiResponse {
  status: string;
  message: string;
  data: any; // Chart data array
  total_unique_count: number; // Total unique count is at root level
  total_incidents_count: number; // Total incidents count is at root level
}

// API response interface for Job Promotion
interface JobPromotionApiResponse {
  status: string;
  message: string;
  data: {
    channel: string;
    count: number;
    percentage: number;
  }[];
  total_count: number; // Total count from API
}

// API response interface for Offers
interface OffersApiResponse {
  status: string;
  message: string;
  data: {
    website_type: string;
    distribution: {
      [key: string]: number;
    };
  }[];
}

// API response interface for Sponsors Ads
interface SponsorsAdsApiResponse {
  status: string;
  message: string;
  data: {
    name: string;
    label: string;
    value: number;
  }[];
}

// API response interface for Filters
interface FiltersApiResponse {
  status: string;
  message: string;
  data: string[];
}

interface TransformedCategoryData {
  [key: string]: string | number | undefined;
  label: string;
  visit: number;
  fill: string;
}

// Add these type definitions before the data declarations
interface CategoryConfig {
  label: string;
  color: string;
  fillOpacity: number;
}


interface SingleCategoryConfig {
  unique: CategoryConfig;
  incidents: CategoryConfig;
}

interface SingleCategoryData {
  date: string;
  unique: number;
  incidents: number;
}

// Function to transform Web App Status API response to chart format
const transformWebAppStatusData = (response: WebAppStatusApiResponse | undefined): TrafficTrendData[] => {
  if (!response || !response.data) return [];

  return response.data.map(item => ({
    month: item.website_type,
    "Reported Volume": item.reported_volume,
    "Unique": item.unique,
    "Closed": item.closure_percentage // This will now be a bar instead of a line
  }));
};

// Color mapping for categories
const getCategoryColor = (category: string): string => {
  const colorMap: { [key: string]: string } = {
    "Suspicious/Similar Domains": "#540094",
    "Parked Domains": "#00C49F",
    "Job Websites": "#FFBB28",
    // "Job Websites": "#FFBB28", // Same as Job Websites
    "Blogging Websites": "#FF8042",
    "Apps/Apks": "#8884d8",
    "Market Places": "#E54030",
    "Google Ads": "#4CAF50",
    "Google Business Listings": "#1DA1F2"
  };

  // Use a deterministic fallback color based on category name instead of random
  if (colorMap[category]) {
    return colorMap[category];
  }

  // Generate a consistent color based on the category name hash
  let hash = 0;
  for (let i = 0; i < category.length; i++) {
    hash = category.charCodeAt(i) + ((hash << 5) - hash);
  }
  const color = Math.abs(hash).toString(16).substring(0, 6);
  return `#${color.padStart(6, '0')}`;
};

// Function to transform Category Share API response to chart format
const transformCategoryShareData = (response: CategoryShareApiResponse | undefined): TransformedCategoryData[] => {
  if (!response || !response.data) return [];

  return response.data.map(item => ({
    label: item.channel,
    visit: item.percentage, // Use percentage instead of count
    value: item.percentage, // Add value field for percentage display in legend
    fill: getCategoryColor(item.channel)
  }));
};

// Function to transform Category Trend API response to chart format
// Transform category trend data - only used for daily frequency
// For weekly and monthly frequency, data is rendered directly from API without transformation
const transformCategoryTrendData = (response: CategoryTrendApiResponse | undefined, frequency: string = 'daily'): SingleCategoryData[] => {
  if (!response || !response.data) return [];

  // Note: This function is only used for daily frequency
  // For weekly and monthly frequency, data comes pre-aggregated from the backend API
  if (frequency.toLowerCase() === 'monthly') {
    // For monthly, aggregate data by month (only used for daily frequency data)
    const monthlyData: { [key: string]: { unique: number; incidents: number } } = {};
    
    response.data.forEach(item => {
      const d = new Date(item.date);
      const monthKey = format(d, 'yyyy-MM'); // Use year-month as key
      const monthLabel = format(d, 'MMM');
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { unique: 0, incidents: 0 };
      }
      
      monthlyData[monthKey].unique += item.unique || 0;
      monthlyData[monthKey].incidents += item.actual_incidents || 0;
    });

    // Convert aggregated data to array format
    return Object.entries(monthlyData).map(([monthKey, data]) => {
      const d = new Date(monthKey + '-01'); // Create date from year-month
      return {
        date: format(d, 'MMM'),
        unique: data.unique,
        incidents: data.incidents
      };
    });
  } else if (frequency.toLowerCase() === 'weekly') {
    // For weekly, aggregate data by week (only used for daily frequency data)
    const weeklyData: { [key: string]: { unique: number; incidents: number } } = {};
    
    response.data.forEach(item => {
      const d = new Date(item.date);
      const firstDayOfMonth = new Date(d.getFullYear(), d.getMonth(), 1);
      const dayOfWeek = firstDayOfMonth.getDay();
      
      // Calculate week number starting from the beginning of the month
      let weekNumber;
      if (d.getDate() <= 7) {
        weekNumber = 1;
      } else if (d.getDate() <= 14) {
        weekNumber = 2;
      } else if (d.getDate() <= 21) {
        weekNumber = 3;
      } else if (d.getDate() <= 28) {
        weekNumber = 4;
      } else {
        weekNumber = 5;
      }
      
      const monthName = format(d, 'MMM');
      const weekKey = `${d.getFullYear()}-${d.getMonth()}-${weekNumber}`;
      
      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = { unique: 0, incidents: 0 };
      }
      
      weeklyData[weekKey].unique += item.unique || 0;
      weeklyData[weekKey].incidents += item.actual_incidents || 0;
    });

    // Convert aggregated data to array format
    return Object.entries(weeklyData).map(([weekKey, data]) => {
      const [year, month, weekNum] = weekKey.split('-');
      const d = new Date(parseInt(year), parseInt(month), 1);
      const monthName = format(d, 'MMM');
      return {
        date: `Week-${weekNum} ${monthName}`,
        unique: data.unique,
        incidents: data.incidents
      };
    });
  } else {
    // For daily frequency, keep original format with date formatting
    return response.data.map(item => {
      const d = new Date(item.date);
      return {
        date: format(d, 'yyyy-MM-dd'),
        unique: item.unique,
        incidents: item.actual_incidents
      };
    });
  }
};

// Function to transform Customer Care API response to chart format
const transformCustomerCareData = (response: CustomerCareApiResponse | undefined) => {
  if (!response || !response.data) return [];

  // Handle different possible API response structures
  let chartData;

  if (Array.isArray(response.data)) {
    // If data is directly an array
    chartData = response.data;
  } else if (response.data.chart_data && Array.isArray(response.data.chart_data)) {
    // If data has chart_data property
    chartData = response.data.chart_data;
  } else {
    return [];
  }

  return chartData.map((item: any) => {
    // Use website_type for y-axis label, fallback to label if not available
    const labelText = item.website_type || item.label || 'Unknown';
    const truncatedLabel = labelText.length > 10 ? `${labelText.substring(0, 10)}...` : labelText;

    return {
      label: truncatedLabel,
      fullLabel: labelText, // Store the complete label for tooltip
      value: item.unique || 0,
      Unique: item.unique || 0,
      Incidents: item.incidents || 0
    };
  });
};

// Function to transform Job Promotion API response to chart format
const transformJobPromotionData = (response: JobPromotionApiResponse | undefined) => {
  if (!response || !response.data) return [];

  return response.data.map((item, index) => ({
    number: item.channel, // Channel name for tooltip
    value: item.percentage, // Use percentage for tooltip display
    visit: item.percentage, // Show percentage in legend
    label: item.channel, // Channel name for tooltip
    fill: getCategoryColor(item.channel),
  }));
};

// Function to transform Offers API response to chart format
const transformOffersData = (response: OffersApiResponse | undefined) => {
  if (!response || !response.data) return [];

  return response.data.map((item) => {
    const transformedItem: any = {
      label: item.website_type, // Use website_type for y-axis
    };

    // Map distribution keys to chart data - ensure values are treated as counts, not percentages
    Object.entries(item.distribution).forEach(([key, value]) => {
      // Ensure the value is treated as a count (number)
      let countValue = Number(value) || 0;
      
      // Force the value to be a count - if it's a decimal, treat it as a count directly
      // Don't multiply by 100 as that might be causing the percentage display
      transformedItem[key] = countValue;
    });

    // Add a total value field for the x-axis to use
    const totalCount = Object.values(item.distribution).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0);
    transformedItem.value = totalCount;

    return transformedItem;
  });
};

// Function to generate dynamic chart configuration for Offers from API response
const generateOffersChartConfig = (response: OffersApiResponse | undefined) => {
  if (!response || !response.data) return {};

  const chartConfig: { [key: string]: { label: string; color: string } } = {};
  const colors = [
    "#1e3799", // Dark Blue
    "#ff6b01", // Orange
    "#27ae60", // Green
    "#3498db", // Light Blue
    "#8e44ad", // Purple
    "#e74c3c", // Red
    "#f39c12", // Yellow
    "#16a085", // Teal
    "#d35400", // Dark Orange
    "#c0392b"  // Dark Red
  ];

  // Extract all unique offer types from the distribution data
  const offerTypes = new Set<string>();
  response.data.forEach(item => {
    Object.keys(item.distribution).forEach(key => {
      offerTypes.add(key);
    });
  });

  // Generate configuration for each offer type
  Array.from(offerTypes).forEach((offerType, index) => {
    chartConfig[offerType] = {
      label: offerType,
      color: colors[index % colors.length] // Cycle through colors if more than 10 offer types
    };
  });

  return chartConfig;
};

// Function to transform Sponsors Ads API response to chart format
const transformSponsorsAdsData = (response: SponsorsAdsApiResponse | undefined) => {
  if (!response || !response.data) return [];

  return response.data.map((item) => ({
    name: item.name,
    label: item.label,
    value: item.value
  }));
};

// Function to transform package name to brand name
const getDefaultBrandName = (packageName: string): string => {
  const brandMap: { [key: string]: string } = {
    'com.icicibank': 'ICICI Bank',
    'com.rblbank': 'RBL Bank'
  };

  // Return mapped brand name or fallback conversion
  return brandMap[packageName] || packageName
    .replace('com.', '')
    .replace('bank', '')
    .toUpperCase() + ' Bank';
};

// Stats configuration for KeyValueCard
const statsConfig = [
  { key: 'total', label: 'Incidents Reported', color: '#540094', showPercentage: true },
  { key: 'active', label: 'Under Brand Review', color: '#FF0000', showPercentage: true },
  { key: 'in_progress', label: 'Takedown Initiated', color: '#FFDB58', showPercentage: true },
  {
    key: 'closed',
    label: 'Closed Incidents',
    color: '#00A86B',
    showPercentage: true,
    subCategories: [
      { key: 'closed_takedown_completed', label: 'Taken Down', color: '#2E8B57', showPercentage: true },
      { key: 'closed_takedown_sticky_incident', label: 'No Action Required', color: '#1E40AF', showPercentage: true },
      { key: 'closed_recommend_to_legal', label: 'Recommend to Legal', color: '#FF6B35', showPercentage: true }
    ]
  }
];

// Function to transform Filters API response to filter format
const transformFiltersData = (response: FiltersApiResponse | undefined): Filter[] => {
  if (!response || !response.data) return [];

  const transformedFilters = response.data.map((item) => ({
    label: item,
    checked: true // Initially all filters are checked visually
  }));

  // Return filters without "all" option
  return transformedFilters;
};

// Function to transform API response to the required format for Website Summary
const transformStatsData = (response: IncidentStatsResponse | undefined) => {
  // Add default values in case response is undefined
  const defaultData = {
    total: 0,
    active: { count: 0, percentage: 0 },
    in_progress: { count: 0, percentage: 0 },
    closed: { count: 0, percentage: 0 },
    takedown: { count: 0, percentage: 0 },
    no_action_required: { count: 0, percentage: 0 }
  };
  const data = response?.data || defaultData;

  return {
    Total: {
      count: data.total,
      title: "Incidents Reported",
      color: "#540094" // Purple
    },
    Active: {
      count: data.active.count,
      title: "Under Brand Review",
      color: "#FF0000"
    },
    InProgress: {
      count: data.in_progress.count,
      title: "Takedown Initiated",
      color: "#FFDB58"
    },
    Closed: {
      count: data.closed.count,
      title: "Closed Incidents",
      color: "#00A86B",
      breakdown: [
        {
          label: "Taken Down",
          value: data?.takedown?.count || 0
        },
        {
          label: "No Action Required",
          value: data?.no_action_required?.count
        }
      ]
    }
  };
};

// Sample category data replaced with API data from CATEGORY_SHARE endpoint


// Channel colors mapping
const channelColors: { [key: string]: string } = {
  "Suspicious/Similar Domains": "#540094",
  "Parked Domains": "#00C49F",
  "Job Related Websites": "#FFBB28",
  "Blogging Websites": "#FF8042",
  "Apps/Apks": "#8884d8",
  "Market Places": "#E54030",
  "Google Ads": "#4CAF50",
  "Google Business Listings": "#1DA1F2"
};


const websiteChartConfig = {
  "Reported Volume": { label: "Reported Volume", color: "#4CAF50" },
  "Unique": { label: "Unique", color: "#E54030" },
  "Closed": { label: "Closed", color: "#540094" } // This will now be a bar instead of a line
};

// Config for single category view
const singleCategoryConfig: SingleCategoryConfig = {
  unique: {
    label: "Unique",
    color: "#10B981",
    fillOpacity: 0.4
  },
  incidents: {
    label: "Incidents",
    color: "#EF4444",
    fillOpacity: 0.4
  }
};

// Add chart configuration for the bars
const barChartConfig = {
  Unique: {
    label: "Unique",
    color: "#274745"
  },
  Incidents: {
    label: "Incidents",
    color: "#E8C468"
  }
};

// Dynamic chart configuration for Offers will be generated from API response


// Add chart configuration for Google Ads
const googleAdsConfig = {
  Date1: {
    label: "Date1",
    color: "#708090"
  },
  Date2: {
    label: "Date2",
    color: "#708090"
  },
  Date3: {
    label: "Date3",
    color: "#708090"
  },
  Date4: {
    label: "Date4",
    color: "#708090"
  },
  Date5: {
    label: "Date5",
    color: "#708090"
  }
};

const getColorShades = (hex: string) => ({
  light: hex + "66", // ~40% opacity
  dark: hex + "CC"    // ~80% opacity
});


const Summary = () => {

  const { selectedPackage } = usePackage();
  const { startDate: fromDate, endDate: toDate } = useDateRange();
  const [selectedBrand, setSelectedBrand] = useState<string[]>([]);
  const [selectedPriority, setSelectedPriority] = useState<string[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string[]>([]);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const cardRefs = React.useRef<HTMLElement[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedFrequencyCategories, setSelectedFrequencyCategories] = useState("Daily");

  // Track which specific filters have been actively changed by user
  const [activeFilters, setActiveFilters] = useState({
    brand: false,
    priority: false,
    country: false
  });

  // Debounced query params to prevent rapid API calls
  const [debouncedFilters, setDebouncedFilters] = useState({
    brand: selectedBrand,
    priority: selectedPriority,
    country: selectedCountry
  });

  // Debounce filter changes to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters({
        brand: selectedBrand,
        priority: selectedPriority,
        country: selectedCountry
      });
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [selectedBrand, selectedPriority, selectedCountry]);

  // Frequency options for Categories Trend chart
  const frequencyOptions = ["Daily", "Weekly", "Monthly"];

  // Handler for frequency change in Categories Trend chart
  const handleCategoriesFrequencyChange = async (value: string) => {
    console.log('Changing categories frequency to:', value);
    setSelectedFrequencyCategories(value);
  };

  // Query params for API calls
  const queryParams = useMemo(() => {
    const baseParams = {
      package_name: selectedPackage,
      fromDate: fromDate,
      toDate: toDate
      // limit: 11,
      // page: 1
    };

    // Always include filter fields - use defaults when not actively changed, user selections when changed
    const payload: any = { ...baseParams };

    // Helper function to format filter values
    // Always send as array - both "all" and selected filters
    const formatFilterValue = (filters: string[], isActive: boolean) => {
      if (!isActive || filters.length === 0) {
        return ["all"]; // Send "All" as array when not actively changed or empty
      }

      // If all filters are selected or "All" is included, send "All" as array
      if (filters.includes("all") || filters.length === 0) {
        return ["all"];
      }

      // Otherwise send selected filters as array
      return filters.filter(item => item !== "all");
    };

    // Brand: use user selection if actively changed, otherwise use "All"
    if (activeFilters.brand && debouncedFilters.brand.length > 0) {
      payload.brand = formatFilterValue(debouncedFilters.brand, activeFilters.brand);
    } else {
      payload.brand = ["all"]; // Send "All" as array for default
    }

    // Priority: use user selection if actively changed, otherwise use "All"
    if (activeFilters.priority && debouncedFilters.priority.length > 0) {
      payload.priority = formatFilterValue(debouncedFilters.priority, activeFilters.priority);
    } else {
      payload.priority = ["all"]; // Send "All" as array for default
    }

    // Country: use user selection if actively changed, otherwise use "All"
    if (activeFilters.country && debouncedFilters.country.length > 0) {
      payload.country = formatFilterValue(debouncedFilters.country, activeFilters.country);
    } else {
      payload.country = ["all"]; // Send "All" as array for default
    }

    return payload;
  }, [selectedPackage, fromDate, toDate, debouncedFilters, activeFilters]);

  // Base params for filter APIs (separate from main queryParams to prevent refetching)
  const baseFilterParams = useMemo(() => ({
    package_name: selectedPackage,
    fromDate: fromDate,
    toDate: toDate,
    menu: "website_app"
    // limit: 11,
    // page: 1
  }), [selectedPackage, fromDate, toDate]);

  // API call for incident stats
  const { data: incidentStatsData, isLoading: incidentStatsLoading } = useQuery<IncidentStatsResponse>(
    ['incidentStats', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.INCIDENTS_STATS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // API call for web app status
  const { data: webAppStatusResponse, isLoading: webAppStatusLoading } = useQuery<WebAppStatusApiResponse>(
    ['webAppStatus', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.WEB_APP_STATUS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the web app status data for the chart
  const webAppStatusData = transformWebAppStatusData(webAppStatusResponse);

  // API call for category share
  const { data: categoryShareResponse, isLoading: categoryShareLoading } = useQuery<CategoryShareApiResponse>(
    ['categoryShare', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.CATEGORY_SHARE,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the category share data for the chart
  const categoryShareData = transformCategoryShareData(categoryShareResponse);

  // Generate chart config from API data
  const categoryDonutChartConfig = categoryShareData.reduce(
    (acc, cur) => ({ ...acc, [cur.label]: { label: cur.label, color: cur.fill } }),
    {} as Record<string, { label: string; color: string }>
  );

  // API call for category trend (day-wise)
  const { data: categoryTrendResponse, isLoading: categoryTrendLoading } = useQuery<CategoryTrendApiResponse>(
    ['categoryTrend', queryParams, selectedCategory, selectedFrequencyCategories],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const trendPayload = {
        ...queryParams,
        category: selectedCategory || "all",
        frequency: selectedFrequencyCategories.toLowerCase()
      };

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.CATEGORY_TREND,
        trendPayload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the category trend data for the chart
  // For weekly and monthly frequency, render data directly from API without any client-side calculations
  // The backend API handles the aggregation and returns the appropriate data structure
  // For daily frequency, apply client-side transformation for date formatting and aggregation
  const categoryTrendData = selectedFrequencyCategories.toLowerCase() === 'weekly' || selectedFrequencyCategories.toLowerCase() === 'monthly'
    ? (categoryTrendResponse?.data || []).map(item => ({
        date: item.date,
        unique: item.unique,
        incidents: item.actual_incidents // Map actual_incidents to incidents for chart compatibility
      }))
    : transformCategoryTrendData(categoryTrendResponse, selectedFrequencyCategories);

  // API call for customer care data
  const { data: customerCareResponse, isLoading: customerCareLoading } = useQuery<CustomerCareApiResponse>(
    ['customerCare', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.CUSTOMER_CARE,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the customer care data for the chart
  const customerCareData = transformCustomerCareData(customerCareResponse);

  // API call for job promotion data
  const { data: jobPromotionResponse, isLoading: jobPromotionLoading } = useQuery<JobPromotionApiResponse>(
    ['jobPromotion', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.JOB_PROMOTION,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the job promotion data for the chart
  const jobPromotionData = transformJobPromotionData(jobPromotionResponse);

  // Generate chart config from Job Promotion API data
  const jobPromotionChartConfig = jobPromotionData.reduce(
    (acc, cur) => ({ ...acc, [cur.label]: { label: cur.label, color: cur.fill } }),
    {} as Record<string, { label: string; color: string }>
  );

  // API call for offers data
  const { data: offersResponse, isLoading: offersLoading } = useQuery<OffersApiResponse>(
    ['offers', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.OFFERS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the offers data for the chart
  const offersData = transformOffersData(offersResponse);

  // API call for sponsors ads data
  const { data: sponsorsAdsResponse, isLoading: sponsorsAdsLoading } = useQuery<SponsorsAdsApiResponse>(
    ['sponsorsAds', queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB.SPONSORS_ADS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,  
      refetchOnMount: true,
      enabled: !!queryParams.fromDate && !!queryParams.toDate && !!selectedPackage
    }
  );

  // Transform the sponsors ads data for the chart
  const sponsorsAdsData = transformSponsorsAdsData(sponsorsAdsResponse);

  // API call for brand filter options
  const { data: brandFilterResponse, isLoading: brandFilterLoading } = useQuery<FiltersApiResponse>(
    ['brandFilter', selectedPackage, fromDate, toDate, baseFilterParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'brand'),
        baseFilterParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage
    }
  );

  // API call for priority filter options
  const { data: priorityFilterResponse, isLoading: priorityFilterLoading } = useQuery<FiltersApiResponse>(
    ['priorityFilter',  selectedPackage, fromDate, toDate, baseFilterParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'priority'),
        baseFilterParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage
    }
  );

  // API call for country filter options
  const { data: countryFilterResponse, isLoading: countryFilterLoading } = useQuery<FiltersApiResponse>(
    ['countryFilter', selectedPackage, fromDate, toDate, baseFilterParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'country'),
        baseFilterParams,
        
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          } as AxiosRequestConfig['headers'],
          timeout: 30000
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage
    }
  );

  // Transform filter data for each filter type
  const brandFilterData = transformFiltersData(brandFilterResponse);
  const priorityFilterData = transformFiltersData(priorityFilterResponse);
  const countryFilterData = transformFiltersData(countryFilterResponse);

  // Auto-select all filters for visual display (but don't send in API payload until user changes them)
  useEffect(() => {
    if (brandFilterResponse?.data && brandFilterResponse.data.length > 0) {
      setSelectedBrand(brandFilterResponse.data);
    }
  }, [brandFilterResponse]);

  useEffect(() => {
    if (priorityFilterResponse?.data && priorityFilterResponse.data.length > 0) {
      setSelectedPriority(priorityFilterResponse.data);
    }
  }, [priorityFilterResponse]);

  useEffect(() => {
    if (countryFilterResponse?.data && countryFilterResponse.data.length > 0) {
      setSelectedCountry(countryFilterResponse.data);
    }
  }, [countryFilterResponse]);

  // Handle filter changes
  const handleFilterSubmit = (id: string, data: FilterState) => {
    // Mark specific filter as actively changed by user
    switch (id) {
      case "brand":
        setActiveFilters(prev => ({ ...prev, brand: true }));
        setSelectedBrand(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case "priority":
        setActiveFilters(prev => ({ ...prev, priority: true }));
        setSelectedPriority(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case "country":
        setActiveFilters(prev => ({ ...prev, country: true }));
        setSelectedCountry(data.filters.filter(f => f.checked).map(f => f.label));
        break;
      default:
        break;
    }
  };

  // Handle filter search
  const handleFilterSearch = (id: string, query: string) => {
    console.log(`Searching ${id} with query: ${query}`);
  };

  // Handle card expansion
  const handleExpand = useCallback((index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  }, [expandedCard]);

  // Add effect to handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && !(document as any).webkitFullscreenElement && expandedCard !== null) {
        // User exited fullscreen, reset the expanded card state
        setExpandedCard(null);
      }
    };

    // Add listeners for different browser implementations
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [expandedCard]);

  // Channel click handler
  const handleCategorySelect = (segment: { label: string; value: number }) => {
    setSelectedCategory(segment.label);
  };

  // Add CSV download function
  const handleCSVDownload = async (apiDetail: string, cardTitle: string) => {
    try {
      setLoading(true);
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      // Helper function to format filter values for CSV download
      const formatFilterValueForCSV = (filters: string[], isActive: boolean) => {
        if (!isActive || filters.length === 0) {
          return ["all"];
        }
        if (filters.includes("all") || filters.length === 0) {
          return ["all"];
        }
        return filters.filter(item => item !== "all");
      };

      // Base payload for all API calls
      const basePayload: any = {
        package_name: selectedPackage,
        fromDate: fromDate,
        toDate: toDate,
        country: formatFilterValueForCSV(selectedCountry, activeFilters.country),
        priority: formatFilterValueForCSV(selectedPriority, activeFilters.priority),
        brand: formatFilterValueForCSV(selectedBrand, activeFilters.brand),
        export_type: "csv"
      };

      let endpoint = '';
      let payload: any = { ...basePayload };

      // Determine endpoint and payload based on apiDetail
      switch (apiDetail) {
        case "incident_stats":
          endpoint = Endpoint.BI.WEB.INCIDENTS_STATS;
          break;
        case "web_app_status":
        case "website_activity":
          endpoint = Endpoint.BI.WEB.WEB_APP_STATUS;
          break;
        case "category_share_chart":
          endpoint = Endpoint.BI.WEB.CATEGORY_SHARE;
          break;
        case "category_trend":
          endpoint = Endpoint.BI.WEB.CATEGORY_TREND;
          payload = {
            ...basePayload,
            category: selectedCategory || "all",
            frequency: selectedFrequencyCategories.toLowerCase()
          };
          break;
        case "customer_care_bar_chart":
          endpoint = Endpoint.BI.WEB.CUSTOMER_CARE;
          break;
        case "job_promotion":
          endpoint = Endpoint.BI.WEB.JOB_PROMOTION;
          break;
        case "get_offers":
          endpoint = Endpoint.BI.WEB.OFFERS;
          break;
       
        default:
          throw new Error(`Unknown API detail: ${apiDetail}`);
      }

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + endpoint,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );

      // Use the utility function to handle CSV download
      const fileName = `${cardTitle}_${fromDate}_to_${toDate}.csv`;
      const success = await handleCSVDownloadFromResponse(response, fileName);
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    } finally {
      setLoading(false);
    }
  };

  // Add onExport function
  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];

      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs]
  );



  return (
    <div className="min-h-screen w-full dark:bg-gray-800 bg-[#F3F4F6]">
    {/* Filter Bar - Made Sticky */}
    <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)] border-b border-gray-200 dark:border-gray-700">
      <div className="flex flex-wrap gap-3">
          <FilterPill
            id="brand"
            title="Brand"
            filters={brandFilterData}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={brandFilterLoading}
            isSearchable={true}
          />
          <FilterPill
            id="priority"
            title="Priority"
            filters={priorityFilterData}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={priorityFilterLoading}
            isSearchable={true}
          />
          <FilterPill
            id="country"
            title="Country"
            filters={countryFilterData}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={countryFilterLoading}
            isSearchable={true}
          />
        </div>
      </div>

      
      <div className="grid grid-cols-1 lg:grid-cols-10 gap-2 m-2">
        {/* Stats Card - 20% width */}
        <div className="lg:col-span-2">
          <Card className="shadow-md rounded-md bg-white dark:bg-card dark:text-white text-header h-full">
            <CardContent className="p-2 mt-2">
              {incidentStatsLoading ? (
                <div className="flex items-center justify-center h-[290px]">
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : incidentStatsData && incidentStatsData.data ? (
                <div className="space-y-0">
                  {statsConfig.map((config, index) => {
                    const data = (incidentStatsData.data as any)[config.key];
                    const value = typeof data === 'object' ? data?.count || 0 : data || 0;
                    const percentage = config.showPercentage && typeof data === 'object' ? `(${data?.percentage || 0}%)` : '';

                    return (
                      <div key={index}>
                        <div className="overflow-x-auto scrollbar">
                          <KeyValueCard
                            title=""
                            leftKey={config.label}
                            leftValue={value}
                            percentage={percentage}
                            colors={config.color}
                            labelFontSize="text-base"
                            valueFontSize="text-base"
                          />
                          {config.subCategories && (
                            <div className="space-y-0 ml-4">
                              {config.subCategories.map((subConfig, subIndex) => {
                                const subData = (incidentStatsData.data as any)[subConfig.key];
                                const subValue = typeof subData === 'object' ? subData?.count || 0 : subData || 0;
                                const subPercentage = subConfig.showPercentage && typeof subData === 'object' ? `(${subData?.percentage || 0}%)` : '';

                                return (
                                  <div key={subIndex} className="overflow-x-auto scrollbar">
                                    <KeyValueCard
                                      title=""
                                      leftKey={subConfig.label}
                                      leftValue={subValue}
                                      percentage={subPercentage}
                                      colors={subConfig.color}
                                      showDivider={false}
                                      compact={true}
                                      bold={false}
                                    />
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>
                        {/* Divider between main categories */}
                        {index < statsConfig.length - 1 && (
                          <div className="border-b border-gray-200 my-2"></div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-[280px] text-center">
                  <div>
                    <div className="text-sm dark:text-white">No Data Found !</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Web/App Status Chart - 50% width */}
        <div className="lg:col-span-5">
          <Card
            ref={(el) => {
              if (el) cardRefs.current[3] = el;
            }}
            className="shadow-md rounded-md bg-white gap-2 dark:bg-card dark:bg-card dark:text-white text-header h-full"
          >
            <CardHeader className="p-2">
              <HeaderRow
                title="Web / App Status"
                onExpand={() => handleExpand(3)}
                onExport={() => onExport("png", "Web / App Status", 3)}
                handleExport={() => handleCSVDownload("website_activity", "Web / App Status")}
                isRadioButton={false}
                isSelect={false}
                titleFontSize="text-base"
                isExpanded={expandedCard === 3}
              />
            </CardHeader>
            <CardContent className="p-2">
              <div className="w-full h-[290px]">
                <StackedBarWithLine
                  chartData={webAppStatusData}
                  chartConfig={websiteChartConfig}
                  showTrendline={false}
                  isHorizontal={false}
                  isLegend={true}
                  isLoading={webAppStatusLoading}
                  isStacked={false}
                  barRadius={0}
                  barWidth={35}
                  chartHeight={300}
                  isExpanded={expandedCard === 3}
                  
                  xAxisConfig={{
                    dataKey: "month",
                    angle: 0,
                    textAnchor: "middle",
                    tickMargin: 8,
                    dy: 5,
                    height: 60,
                    tickFormatter: (value: string) => value.length > 12 ? value.slice(0, 12) + '...' : value,
                    tick: ({ x, y, payload }: any) => (
                      <g transform={`translate(${x},${y})`}>
                        <title>{payload.value}</title>
                        <text
                          x={0}
                          y={0}
                          dy={16}
                          textAnchor="middle"
                          fill="#666"
                          fontSize={12}
                        >
                          {payload.value.length > 12 ? payload.value.slice(0, 12) + '...' : payload.value}
                        </text>
                      </g>
                    )
                  }}
                  YAxis1={{
                    yAxisId: "left",
                    orientation: "left",
                    tickFormatter: (value: string) => formatNumber(Number(value))
                  }}
                  onExpand={() => handleExpand(3)}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Job Promotion Donut Chart - 30% width */}
        <div className="lg:col-span-3">
          <Card
            ref={(el) => {
              if (el) cardRefs.current[12] = el;
            }}
            className="w-full shadow-md rounded-md bg-white dark:bg-card dark:text-white text-header h-full overflow-hidden"
          >
            <CardContent className="pr-4 pl-6 h-full pt-2 pb-7">
              <div className="h-full min-h-[300px]">
                <DonutChart
                  chartData={jobPromotionData}
                  chartConfig={jobPromotionChartConfig}
                  dataKey="value"
                  nameKey="label"
                  isView={true}
                  isLoading={jobPromotionLoading}
                  isPercentage={true}
                  direction="flex-col"
                  isdonut={true}
                  marginTop="mt-0"
                  position="items-start"
                  onExpand={() => handleExpand(12)}
                  innerRadius={50}
                  outerRadius={90}
                  title="Job Promotions"
                  legendFontSize={12}
                  titleFontSize="text-base"
                  isSelect={false}
                  isRadioButton={false}
                  visitEventOptions={[]}
                  handleTypeChange={() => { }}
                  selectedType=""
                  selectoptions={[]}
                  placeholder=""
                  centerValue={jobPromotionResponse?.total_count?.toString() || ""}
                  centerLabel="Total Count"
                  onExport={() => onExport("png", "Job Promotions", 12)}
                  handleExport={() => handleCSVDownload("job_promotion", "Job Promotions")}
                  formatterType="percentage"
                  legendPosition="bottom"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>


      <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-10 gap-2 m-2">
        {/* Customer Share Donut Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[1] = el;
          }}
          className="w-full shadow-md rounded-md bg-white dark:bg-card dark:text-white text-header md:col-span-3 overflow-hidden"
        >
          <CardContent className="w-full min-h-[280px] pt-2 pb-7">
            <div className="h-full min-h-[300px]">
              <DonutChart
                chartData={categoryShareData}
                dataKey="visit"
                nameKey="label"
                chartConfig={categoryDonutChartConfig}
                isView={true}
                isLoading={categoryShareLoading}
                isPercentage={true}
                direction="flex-col"
                isdonut={expandedCard === 1}
                marginTop="mt-4"
                position="items-start"
                innerRadius={50}
                  outerRadius={90}
                onSegmentClick={handleCategorySelect}
                selectedSegment={selectedCategory}
                onExpand={() => handleExpand(1)}
                title="Category Share"
                hideScrollbar={true}
                titleFontSize="text-base"
                legendsTitle="Select Categories For Trendline"
                legendsTitleFontSize="0.7rem"
                showRefresh={true}
                legendFontSize={12}                centerValue={categoryShareResponse?.total_count?.toString() || ""}
                centerLabel="Total Count"
                onRefresh={() => {
                  setSelectedCategory(null);
                  console.log("Refreshing category data...");
                }}
                onExport={() => onExport("png", "Customer Share", 1)}
                handleExport={() => handleCSVDownload("category_share_chart", "Customer Share")}
                formatterType="percentage"
                legendPosition="bottom"
              />
            </div>
          </CardContent>
        </Card>

        {/* Area Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[2] = el;
          }}
          className="w-full shadow-md rounded-md bg-white dark:bg-card dark:text-white text-header md:col-span-7"
        >
          <CardHeader className="p-2">
            <HeaderRow
              title={selectedCategory ? `${selectedCategory} Trend` : "Categories Trend"}
              onExpand={() => handleExpand(2)}
              onExport={() => onExport("png", selectedCategory ? `${selectedCategory} Trend` : "Day-Wise Categories Trend", 2)}
              handleExport={() => handleCSVDownload("category_trend", "Categories Trend")}
              isRadioButton={false}
              isSelect={true}
              selectoptions={frequencyOptions}
              handleFrequencyChange={handleCategoriesFrequencyChange}
              selectedFrequency={selectedFrequencyCategories}
              placeholder="Daily"
              titleFontSize="text-base"
              isExpanded={expandedCard === 2}
            />
          </CardHeader>
          <CardContent className="w-full">
             <div className={`w-full ${expandedCard === 2 ? 'h-[100vh]' : 'h-full'}`}>
              {categoryTrendLoading ? (
                 <div className={`flex items-center justify-center ${expandedCard === 2 ? 'h-[calc(100vh-120px)]' : 'h-[300px]'}`}>
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !categoryTrendData || categoryTrendData.length === 0 ? (
                 <div className={`flex items-center justify-center ${expandedCard === 2 ? 'h-[calc(100vh-120px)]' : 'h-[300px]'}`}>
                  <span className="text-small-font">No Data Found.!</span>
                </div>
              ) : (
                <div className="w-full">
                  <ScrollableChartContainer
                    dataLength={categoryTrendData.length}
                    itemWidth={80}
                     height={expandedCard === 2 ? window.innerHeight - 120 : 260}
                    showScrollbar={true}
                    scrollbarHeight={6}
                    scrollbarColor="rgba(156, 163, 175, 0.3)"
                    scrollbarHoverColor="rgba(156, 163, 175, 0.5)"
                    minDataLengthForScrollbar={5}
                    forceScrollbar={true}
                    responsiveScrollbar={true}
                    className="categories-trend-scrollable"
                  >
                    <ResponsiveContainer
                      width="100%"
                       height={expandedCard === 2 ? window.innerHeight - 120 : 260}
                    >
                      <AreaChart
                        data={categoryTrendData}
                         margin={{ 
                           top: expandedCard === 2 ? 20 : 10, 
                           right: expandedCard === 2 ? 50 : 30, 
                           left: expandedCard === 2 ? 20 : 0, 
                           bottom: expandedCard === 2 ? 20 : 5 
                         }}
                      >
                        <XAxis
                          dataKey="date"
                           style={{ fontSize: expandedCard === 2 ? '14px' : '10px' }}
                          interval={0}
                           tickMargin={expandedCard === 2 ? 25 : 15}
                           height={expandedCard === 2 ? 80 : (selectedFrequencyCategories.toLowerCase() === 'weekly' ? 60 : 60)}
                        />
                                                 <YAxis style={{ fontSize: expandedCard === 2 ? '14px' : '10px' }} />
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (!active || !payload?.length) return null;

                            return (
                               <div className={`grid items-start gap-1.5 rounded-lg border border-border/50 bg-background shadow-xl ${
                                 expandedCard === 2 
                                   ? 'min-w-[15rem] px-4 py-2 text-sm' 
                                   : 'min-w-[10rem] px-2.5 py-1.5 text-xs'
                               }`}>
                                <div className="font-medium bg-gray-100 dark:text-white">
                                  {label}
                                </div>
                                <div className="grid gap-1.5">
                                  {payload.map((item, index) => {
                                    const indicatorColor = item.color;

                                    return (
                                      <div
                                        key={item.dataKey}
                                        className="flex w-full flex-wrap items-center gap-2"
                                      >
                                        <div
                                           className={`shrink-0 rounded-full border-[--color-border] bg-[--color-bg] ${
                                             expandedCard === 2 ? 'h-4 w-4' : 'h-3 w-3'
                                           }`}
                                          style={
                                            {
                                              "--color-bg": indicatorColor,
                                              "--color-border": indicatorColor,
                                            } as React.CSSProperties
                                          }
                                        />
                                         <div className="flex flex-1 justify-between leading-none items-center">
                                          <div className="grid gap-1.5">
                                             <span className={`text-muted-foreground ${
                                               expandedCard === 2 ? 'text-sm' : 'text-small-font'
                                             }`}>
                                              {item.name}
                                            </span>
                                          </div>
                                          {item.value !== undefined && (
                                             <span className={`font-mono font-medium tabular-nums text-foreground ${
                                               expandedCard === 2 ? 'text-sm' : 'text-small-font'
                                             }`}>
                                              {typeof item.value === 'number' ?
                                                item.value.toLocaleString() :
                                                "-"
                                              }
                                            </span>
                                          )}
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            );
                          }}
                        />

                        {/* Responsive shaded area coloring */}
                        {(() => {
                          const selectedColor = selectedCategory && categoryDonutChartConfig[selectedCategory]?.color
                            ? categoryDonutChartConfig[selectedCategory].color
                            : singleCategoryConfig.unique.color;
                          const shades = getColorShades(selectedColor);
                          return (
                            <>
                              <Area
                                type="linear"
                                dataKey="unique"
                                name="Unique"
                                stroke={shades.dark}
                                fill={shades.dark}
                                fillOpacity={1}
                              />
                              <Area
                                type="linear"
                                dataKey="incidents"
                                name="Incidents"
                                stroke={shades.light}
                                fill={shades.light}
                                fillOpacity={1}
                              />
                            </>
                          );
                        })()}
                      </AreaChart>
                    </ResponsiveContainer>
                  </ScrollableChartContainer>

                  {/* Legend outside the scrollable area */}
                   <div className={`flex justify-center items-center ${expandedCard === 2 ? 'mt-4 py-4' : 'mt-2 py-2'}`}>
                    <div className="flex items-center gap-4">
                      {(() => {
                        const selectedColor = selectedCategory && categoryDonutChartConfig[selectedCategory]?.color
                          ? categoryDonutChartConfig[selectedCategory].color
                          : singleCategoryConfig.unique.color;
                        const shades = getColorShades(selectedColor);

                        return (
                          <>
                            <div className="flex items-center gap-2">
                              <div
                                 className={`rounded-full ${
                                   expandedCard === 2 ? 'w-4 h-4' : 'w-3 h-3'
                                 }`}
                                style={{ backgroundColor: shades.light }}
                              />
                               <span className={`${expandedCard === 2 ? 'text-sm' : 'text-xs'}`} style={{ color: 'black' }}>Incidents</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div
                                 className={`rounded-full ${
                                   expandedCard === 2 ? 'w-4 h-4' : 'w-3 h-3'
                                 }`}
                                style={{ backgroundColor: shades.dark }}
                              />
                               <span className={`${expandedCard === 2 ? 'text-sm' : 'text-xs'}`} style={{ color: 'black' }}>Unique</span>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Care Number Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 m-2">
        <Card
          ref={(el) => {
            if (el) cardRefs.current[11] = el;
          }}
          className="w-full shadow-md rounded-md bg-white gap-1 dark:bg-card dark:text-white text-header"
        >
          <CardHeader className="p-2">
            <div className="flex flex-col gap-1">
              <HeaderRow
                title="Customer Care Number"
                onExpand={() => handleExpand(11)}
                onExport={() => onExport("png", "Customer Care Number", 11)}
                handleExport={() => handleCSVDownload("customer_care_bar_chart", "Customer Care Number")}
                isRadioButton={false}
                isSelect={false}
                titleFontSize="text-base"
                isExpanded={expandedCard === 11}
              />
              <div className="flex justify-start pl-4 gap-4">
                  <div className="text-xs font-text-sm">
                    <span style={{ color: '#274745', fontWeight: 'bold' }}>Total Unique Count</span> : <span style={{ color: '#274745', fontWeight: 'bold' }}>{customerCareLoading ? 'Loading...' : (
                      customerCareResponse?.total_unique_count || 0
                    )}</span>
                  </div>
                  <div className="text-xs font-text-sm">
                    <span style={{ color: '#E8C468', fontWeight: 'bold'}}>Total Incidents Count</span> : <span style={{ color: '#E8C468', fontWeight: 'bold' }}>{customerCareLoading ? 'Loading...' : (
                      customerCareResponse?.total_incidents_count || 0
                    )}</span>
                  </div>
              </div>
            </div>
          </CardHeader>
                     <CardContent className={`w-full p-0 ${expandedCard === 11 ? 'h-[100vh]' : 'h-[300px]'}`}>
             <div className={`w-full overflow-hidden ${expandedCard === 11 ? 'h-[calc(100vh-48px)]' : 'h-full'}`}>
              <DynamicBarChart
                data={customerCareData}
                config={barChartConfig}
                isHorizontal={true}
                onExpand={() => handleExpand(11)}
                position="right"
                isLoading={customerCareLoading}
                formatterType="number"
                isRadioButton={false}
                isSelect={false}
                selectoptions={[]}
                showHeader={false}
                isScrollable={false}
                  barSize={expandedCard === 11 ? 30 : 20}
                  yAxisTextGap={expandedCard === 11 ? -40 : -30}
                  yAxisTruncateLength={expandedCard === 11 ? 20 : 15}
                  height={expandedCard === 11 ? window.innerHeight - 120 : 300}
                  legendGap={expandedCard === 11 ? 12 : 8}
                  isExpanded={expandedCard === 11}
              />
            </div>
          </CardContent>
        </Card>

        <Card
          ref={(el) => {
            if (el) cardRefs.current[13] = el;
          }}
          className="w-full shadow-md rounded-md bg-white gap-1 dark:bg-card dark:text-white text-header"
        >
 <CardHeader className="p-2">
            <div className="flex flex-col gap-1">
              <HeaderRow
                title="Offers"
                onExpand={() => handleExpand(13)}
                onExport={() => onExport("png", "Offers", 13)}
                handleExport={() => handleCSVDownload("get_offers", "Offers")}
                isRadioButton={false}
                isSelect={false}
                titleFontSize="text-base"
                isExpanded={expandedCard === 13}
              />
              <div className="flex justify-start pl-4">
                <div className="text-xs font-text-sm">

                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="w-full h-[300px] p-0">
            <div className="w-full h-full overflow-hidden">
                              <ChartBarStacked
                  chartData={offersData}
                  chartConfig={generateOffersChartConfig(offersResponse)}
                  isHorizontal={false}
                  onExpand={() => handleExpand(13)}
                  isLoading={offersLoading}
                  title=""
                  titleFontSize="text-base"
                  xAxis={{
                    dataKey: "value",
                    title: "Count",
                    tickFormatter: (value: string | number) => {
                      // Convert to number and ensure no percentage formatting
                      const numValue = typeof value === 'string' ? parseFloat(value) : value;
                      return isNaN(numValue) ? '0' : Math.round(numValue).toString();
                    }
                  }}
                  yAxis={{
                    dataKey: "label",
                    title: ""
                  }}
                  barSize={20}
                  barGap={2}
                  tickMargin={2}
                  margin={{ left: 23, right: 25, top: 10, bottom: 15 }}
                  isCartesian={false}
                  layoutDirection="flex-row"
                  isSelect={false}
                  isRadioButton={false}
                  selectoptions={[]}
                  visitEventOptions={[]}
                  handleTypeChange={() => { }}
                  handleFrequencyChange={() => { }}
                  onExport={() => onExport("png", "Offers", 13)}
                  handleExport={() => handleCSVDownload("get_offers", "Offers")}
                  showMenuDots={false}
                  legendFontSize="12px"
                  labelTruncateLength={12}
                  yAxisFontSize="12px"
                  isExpanded={expandedCard === 13}
                />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Summary;
