.rep{
    border: 1px black solid;
    border-radius: 10px;
    background-color: white;
}
h1{
    color: black;
    font-size: 1.2rem;
}
h3{
    color: rgba(0, 0, 0, 0.382);
    font-size: 1rem;
}
h2{
    color: black;
    font-size: 1rem;
}
#ace{
    margin: 10px;
}
#a1{
    margin: 20px 0;
    display: flex;
    justify-content: space-between;
}

.drop1{
    margin: 2px 0;
    padding: 8px;
    appearance: none;
    padding-right: 30px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 140 140' width='16' height='16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='20,50 70,100 120,50' stroke='%23999' stroke-width='15' fill='none'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
}
#text1{
    width: 100%;
    border-radius: 5px;
    padding: 8px;
    margin: 2px 0;
    font-size: 1rem;
}

#a2{
    margin: 10px 0;
}

#text2 {
  width: 100%;
  border-radius: 5px;
  padding: 8px;
  margin: 2px 0;
  font-size: 1rem;
  resize: none;
  overflow: hidden;
  box-sizing: border-box;
}

.report-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 400px;
  max-height: 200vh;
  overflow-y: auto;
  background-color: white;
  border: 1px solid black;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 0 10px rgba(0,0,0,0.3);
}
#close-btn {
  margin-top: 10px;
  background: white;
  color: black;
  border: none;
  padding: 6px 10px;
  cursor: pointer;
  font: 1rem bold;
}

#o{
    
} 