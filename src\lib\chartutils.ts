export interface FraudDataVisits {
    fraud_sub_category: string;
    total_count: number;
    percentage:string;
  
  }
  
  export interface ColorConfi {
    [key: string]: {
      label: string;
      color: string;
    };
  }
  
  export interface ChartConfi {
    [key: string]: {
      label: string;
      color: string;
    };
  }
  
  export interface ChartData {
    label: string;
    visit: number;
    percentage:string;
    fill: string;
  }
  
  
  export const generateChartConfig = (
    existingData: FraudDataVisits[],
    colorConfig: ColorConfi
  ): { chartData: ChartData[]; chartConfig: ChartConfi } => {
    const chartData = existingData.map((item) => ({
      label: item.fraud_sub_category,
      visit: item.total_count,
      percentage: `(${item.percentage})`,     
       fill: colorConfig[item.fraud_sub_category.replace(' ','')]?.color || "#000000", // Default to black if not found
    }));
  
    // Create chartConfig
    const chartConfig: ChartConfi = {};
    for (const key in colorConfig) {
      if (colorConfig.hasOwnProperty(key)) {
        chartConfig[key] = {
          label: colorConfig[key].label,
          color: colorConfig[key].color,
        };
      }
    }
  
    return { chartData, chartConfig };
  };
  