import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import ResizableTable from '@/components/mf/TableComponent';
import type { Column } from '@/components/mf/TableComponent';

interface TableData extends Record<string, string | number> {
  website: string;
  traffic: number;
  status: string;
  category: string;
  createdOn: string;
  owner: string;
}

interface CustomChartCardProps {
  title: string;
  subtitle: string;
  data: any[];
  dataKey: string;
  categoryKey: string;
  barColor: string;
  showDownloadButton?: boolean;
  className?: string;
  tableData: TableData[];
  columns: Column<Record<string, string | number>>[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  totalPages: number;
}

const CustomChartCard: React.FC<CustomChartCardProps> = ({
  title,
  subtitle,
  data,
  dataKey,
  categoryKey,
  barColor,
  showDownloadButton = false,
  className = '',
  tableData,
  columns,
  searchTerm,
  setSearchTerm,
  currentPage,
  itemsPerPage,
  onPageChange,
  onLimitChange,
  totalPages,
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 w-full ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-500">{subtitle}</p>
        </div>
        {showDownloadButton && (
          <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Download Report
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chart Section */}
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey={categoryKey}
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#6B7280', fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#6B7280', fontSize: 12 }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #E5E7EB',
                  borderRadius: '6px',
                  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                }}
                cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
              />
              <Bar
                dataKey={dataKey}
                fill={barColor}
                radius={[4, 4, 0, 0]}
                maxBarSize={40}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Table Section */}
        <div className="h-[300px] overflow-auto">
          <ResizableTable
            data={tableData}
            columns={columns}
            SearchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            onPageChangeP={onPageChange}
            onLimitChange={onLimitChange}
            pageNo={currentPage}
            totalPages={totalPages}
            isSearchable={true}
            isPaginated={true}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomChartCard; 