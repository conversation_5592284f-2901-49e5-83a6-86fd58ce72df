'use client'
import React, { useState, useMemo, useRef, Dispatch, SetStateAction, useCallback, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FolderSearch } from "lucide-react";
import { FilterPill } from "@/components/mf/Filters";
import { useQuery } from "react-query";
import axios from "axios";
import Endpoint from "@/common/endpoint";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { usePackage } from "@/components/mf/PackageContext";
import { FilterState } from "@/components/mf/Filters/FilterPill";
import { PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip, Area, AreaChart, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Label } from 'recharts';
import HeaderRow from "@/components/mf/HeaderRow";
import { formatNumber, onExpand, handleCSVDownloadFromResponse } from "@/lib/utils";
import { format } from "date-fns";
import DynamicBar<PERSON>hart from "@/components/mf/DynamicBarChart";
import ResizableTable from "@/components/mf/TableComponent";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


interface FilterResponse {
  data: string[];
  status: boolean;
}


interface GeographicalMapData {
  region: string;
  Active: number;
  "In Progress": number;
  Closed: number;
}

interface GeographicalMapConfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

interface GeographicalDistributionResponse {
  data: GeographicalMapData[];
  status: boolean;
}

interface InternationalHostingResponse {
  status: string;
  message: string;
  data: Array<{
    type: string;
    value: number;
    percentage: number;
  }>;
}

interface HostingProvidersResponse {
  data: Array<{
    product: string;
    sales: number;
  }>;
}

interface HostingProvidersInfoResponse {
  status: string;
  message: string;
  data: Array<{
    website: string;
    ip_address: string;
    creation_date: string;
    update_date: string;
    brand_logo_name: string;
    investigation_link: string;
  }>;
}

interface PlatformInfoResponse {
  data: Array<{
    platform: string;
    reported_volume: number;
    unique: number;
  }>;
  total_incidents_count: number;
  total_unique_count: number;
}

interface ContactNumberResponse {
  status: string;
  message: string;
  data: Array<{
    date: string;
    incidents: number;
    unique: number;
  }>;
  total_incidents_count: number;
  total_unique_count: number;
    unique_percent_of_incidents: number;
}

interface DomainDropdownResponse {
  domain_options: string[];
}

interface ApkDropdownResponse {
  application_options: string[];
}

interface DomainsTableResponse {
  status: string;
  message: string;
  data: {
    ip_location: string;
    hosting_provider_country: string;
    registrar_country: string;
    domain_creation_date: string;
    domain_update_date: string;
    brand_logo_used: string;
    brand_name_used: string;
  };
}

interface ApkTableResponse {
  status: string;
  message: string;
  data: {
    application_name: string;
    app_creation_date: string;
    app_update_date: string;
    brand_logo_used: string;
    brand_name_used: string;
    developer_details: string;
    app_version_size: string;
  };
}

const geographicalMapChartConfig: GeographicalMapConfig = {
  Active: { label: "Active", color: "#3B82F6" },
  "In Progress": { label: "In Progress", color: "#F59E0B" },
  Closed: { label: "Closed", color: "#10B981" }
};

// Default colors for pie chart segments
const pieChartColors = ['#0088CC', '#FF8042'];

// Custom legend renderer for pie chart
const CustomLegend = (props: any) => {
  const { payload } = props;
  const isExpanded = document.fullscreenElement !== null;

  return (
    <div className={`flex justify-center gap-4 mt-4 ${isExpanded ? 'gap-6' : 'gap-4'}`}>
      {payload.map((entry: any, index: number) => {
        return (
          <div key={`legend-${index}`} className="flex items-center gap-2">
            <div
              className={`rounded-full ${isExpanded ? 'w-4 h-4' : 'w-3 h-3'}`}
              style={{ backgroundColor: entry.color }}
            />
            <span style={{ fontSize: isExpanded ? '16px' : '12px' }}>{entry.value} - {entry.payload.percentage}%</span>
          </div>
        );
      })}
    </div>
  );
};

// Custom legend renderer for radar chart
const RadarChartLegend = (props: any) => {
  const { payload } = props;

  return (
    <div className="flex justify-center gap-4 mt-4">
      {payload.map((entry: any, index: number) => (
        <div key={`legend-${index}`} className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span style={{ fontSize: '12px' }}>{entry.value}</span>
        </div>
      ))}
    </div>
  );
};

interface WebsiteData extends Record<string, string | number> {
  website: string;
  ip_address: string;
  creation_date: string;
  update_date: string;
  brand_logo_name: string;
  investigation_link: string;
}

interface Column<T> {
  key: string;
  title: string;
  render?: (item: T) => React.ReactNode;
}

const Analytics = () => {
  const [selectedBrand, setSelectedBrand] = useState<string[]>(["all"]);
  const [selectedPriority, setSelectedPriority] = useState<string[]>(["all"]);
  const [selectedCountry, setSelectedCountry] = useState<string[]>(["all"]);

  const [appliedBrand, setAppliedBrand] = useState<string[]>(["all"]);
  const [appliedPriority, setAppliedPriority] = useState<string[]>(["all"]);
  const [appliedCountry, setAppliedCountry] = useState<string[]>(["all"]);

  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  // const [selectedFrequency, setSelectedFrequency] = useState<string>("daily");

  // const handleFrequencyChange = (frequency: string) => {
  //   setSelectedFrequency(frequency);
  // };


  const [leftTableFilter, setLeftTableFilter] = useState<string>("");
  const [rightTableFilter, setRightTableFilter] = useState<string>("");

  const { startDate: fromDate, endDate: toDate } = useDateRange();
  const { selectedPackage } = usePackage();

  const queryParams = useMemo(() => {
    const formatFilterValue = (filters: string[]) => {
      if (filters.length === 0 || (filters.length === 1 && filters.includes("all"))) {
        return ["all"];
      }

      if (filters.includes("all")) {
        return ["all"];
      }

      return filters.filter(item => item !== "all");
    };

    return {
      package_name: selectedPackage,
      fromDate,
      toDate,
      brand: formatFilterValue(appliedBrand),
      priority: formatFilterValue(appliedPriority),
      country: formatFilterValue(appliedCountry),
    };
  }, [fromDate, toDate, appliedBrand, appliedPriority, appliedCountry, selectedPackage]);

  // Separate query params for Contact Number chart with frequency
  // const contactNumberQueryParams = useMemo(() => {
  //   return {
  //     ...queryParams,
  //     frequency: selectedFrequency.toLowerCase()
  //   };
  // }, [queryParams, selectedFrequency]);

  // Fetch filter data
  const { data: brandFilterData } = useQuery<FilterResponse>({
    queryKey: ['brandFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'brand'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu: "website_app"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    refetchOnWindowFocus: false,
  });

  const { data: priorityFilterData } = useQuery<FilterResponse>({
    queryKey: ['priorityFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'priority'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu: "website_app"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    refetchOnWindowFocus: false,
  });

  const { data: countryFilterData } = useQuery<FilterResponse>({
    queryKey: ['countryFilter', selectedPackage, fromDate, toDate],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'country'),
        {
          package_name: selectedPackage,
          fromDate,
          toDate,
          menu: "website_app"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!selectedPackage && !!fromDate && !!toDate,
    refetchOnWindowFocus: false,
  });

  // Fetch geographical distribution data
  const { data: geographicalDistributionData } = useQuery<GeographicalDistributionResponse>({
    queryKey: ['geographicalDistribution', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.GEOGRAPHICAL_DISTRIBUTION,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Fetch international hosting data
  const { data: internationalHostingData } = useQuery<InternationalHostingResponse>({
    queryKey: ['internationalHosting', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.INTERNATIONAL_HOSTING,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Fetch hosting providers info data
  const { data: hostingProvidersInfoData } = useQuery<HostingProvidersInfoResponse>({
    queryKey: ['hostingProvidersInfo', queryParams, selectedProduct],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const requestPayload = {
        ...queryParams,
        domain: selectedProduct ? selectedProduct : "all"
      };

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.HOSTING_PROVIDERS_INFO,
        requestPayload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Fetch platform info data
  const { data: platformInfoData } = useQuery<PlatformInfoResponse>({
    queryKey: ['platformInfo', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.PLATFORM_INFO,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Fetch contact number data
  const { data: contactNumberData } = useQuery<ContactNumberResponse>({
    queryKey: ['contactNumber', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.CONTACT_NUMBER,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Fetch domain dropdown data
  const { data: domainDropdownData } = useQuery<DomainDropdownResponse>({
    queryKey: ['domainDropdown', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.DOMAIN_DROPDOWN,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Fetch APK dropdown data
  const { data: apkDropdownData } = useQuery<ApkDropdownResponse>({
    queryKey: ['apkDropdown', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.APK_DROPDOWN,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Set default domain when domain dropdown data is loaded and no domain is selected
  useEffect(() => {
    if (domainDropdownData?.domain_options && domainDropdownData.domain_options.length > 0 && !leftTableFilter) {
      setLeftTableFilter(domainDropdownData.domain_options[0]);
    }
  }, [domainDropdownData, leftTableFilter]);

  // Reset domain selection when package changes
  useEffect(() => {
    setLeftTableFilter("");
  }, [selectedPackage]);

  // Set default APK when APK dropdown data is loaded and no APK is selected
  useEffect(() => {
    if (apkDropdownData?.application_options && apkDropdownData.application_options.length > 0 && !rightTableFilter) {
      setRightTableFilter(apkDropdownData.application_options[0]);
    }
  }, [apkDropdownData, rightTableFilter]);

  // Reset APK selection when package changes
  useEffect(() => {
    setRightTableFilter("");
  }, [selectedPackage]);

  // Fetch domains table data
  const { data: domainsTableData } = useQuery<DomainsTableResponse>({
    queryKey: ['domainsTable', leftTableFilter, queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.DOMIANS_TABLE,
        {
          ...queryParams,
          domain: leftTableFilter
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('Domains Table API Request Payload:', {
        ...queryParams,
        domain: leftTableFilter
      });
      console.log('Domains Table API Response:', response.data);
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage && !!leftTableFilter,
  });

  // Fetch APK table data
  const { data: apkTableData } = useQuery<ApkTableResponse>({
    queryKey: ['apkTable', rightTableFilter, queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.APK_TABLE,
        {
          ...queryParams,
          apk: rightTableFilter
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('APK Table API Request Payload:', {
        ...queryParams,
        apk: rightTableFilter
      });
      console.log('APK Table API Response:', response.data);
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage && !!rightTableFilter && 
             apkDropdownData?.application_options && apkDropdownData.application_options.length > 0,
    refetchOnWindowFocus: false,
  });

  // Memoized filter data
  const brandFilters = useMemo(() =>
    (brandFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedBrand.includes(label) || selectedBrand.includes("all")
    })),
    [brandFilterData?.data, selectedBrand]
  );

  const priorityFilters = useMemo(() =>
    (priorityFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedPriority.includes(label) || selectedPriority.includes("all")
    })),
    [priorityFilterData?.data, selectedPriority]
  );

  const countryFilters = useMemo(() =>
    (countryFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedCountry.includes(label) || selectedCountry.includes("all")
    })),
    [countryFilterData?.data, selectedCountry]
  );

  // Handle filter search
  const handleFilterSearch = (id: string, query: string) => {
    console.log(`Searching ${id} with query: ${query}`);
  };

  // Handle filter submit - this will update applied filters and trigger API refetch
  const handleFilterSubmit = (id: string, data: FilterState) => {
    switch (id) {
      case 'brand':
        setSelectedBrand(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        // Update applied filters to trigger API refetch
        setAppliedBrand(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'priority':
        setSelectedPriority(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        // Update applied filters to trigger API refetch
        setAppliedPriority(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'country':
        setSelectedCountry(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        // Update applied filters to trigger API refetch
        setAppliedCountry(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        break;
    }
  };

  // Chart data from API
  const chartData = useMemo(() => {
    if (!geographicalDistributionData?.data) return [];
    
    return geographicalDistributionData.data.map((item) => ({
      label: item.region,
      value: item.Active + item["In Progress"] + item.Closed, // Total for the main value
      Active: item.Active,
      "In Progress": item["In Progress"],
      Closed: item.Closed
    }));
  }, [geographicalDistributionData]);

  // Pie chart data from API
  const pieChartData = useMemo(() => {
    if (!internationalHostingData?.data) return [];
    return internationalHostingData.data.map((item, index) => ({
      name: item.type,
      value: item.value,
      percentage: item.percentage,
      color: pieChartColors[index % pieChartColors.length]
    }));
  }, [internationalHostingData]);

  const cardRefs = useRef<HTMLElement[]>([]);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  // Handle fullscreen exit to reset expandedCard state
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && !document.webkitFullscreenElement && 
          !document.mozFullScreenElement && !document.msFullscreenElement) {
        if (expandedCard !== null) {
          setExpandedCard(null);
        }
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [expandedCard]);

  // Fetch hosting providers data
  const { data: hostingProvidersData } = useQuery<HostingProvidersResponse>({
    queryKey: ['hostingProviders', queryParams],
    queryFn: async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.HOSTING_PROVIDERS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    enabled: !!fromDate && !!toDate && !!selectedPackage,
    refetchOnWindowFocus: false,
  });

  // Handle bar click in Hosting Providers chart
  const handleBarClick = (data: { label: string }) => {
    setSelectedProduct(data.label);
  };

  // Handle refresh click to reset selected product
  const handleRefresh = () => {
    setSelectedProduct(null);
  };

  // Hosting providers data for chart
  const hostingProvidersChartData = useMemo(() => {
    if (!hostingProvidersData?.data) return [];
    return hostingProvidersData.data.map((item) => ({
      label: item.product,
      value: item.sales,
      Sales: item.sales
    }));
  }, [hostingProvidersData]);

  // Configuration for Hosting Providers chart
  const hostingProvidersConfig = {
    Sales: {
      label: "Sales",
      color: "#147878"
    }
  };



  // Hosting providers info data for table
  const websitesTableData = useMemo(() => {
    if (!hostingProvidersInfoData?.data) return [];
    return hostingProvidersInfoData.data.map((item) => ({
      website: item.website,
      ip_address: item.ip_address,
      creation_date: item.creation_date,
      update_date: item.update_date,
      brand_logo_name: item.brand_logo_name,
      investigation_link: item.investigation_link
    }));
  }, [hostingProvidersInfoData]);





  const PlatformChartConfig = {
    "Incidents": {
      label: "Incidents",
      color: "#E8C468"
    },
    "Unique": {
      label: "Unique",
      color: "#274745"
    }
  };

  // Platform chart data from API
  const platformChartData = useMemo(() => {
    if (!platformInfoData?.data) return [];
    return platformInfoData.data.map((item) => ({
      label: item.platform,
      value: item.reported_volume,
      "Unique": item.unique,
      "Incidents": item.reported_volume
    }));
  }, [platformInfoData]);

  // Contact Number bar chart data from API
  const contactNumberChartData = useMemo(() => {
    if (!contactNumberData?.data) return [];
    return contactNumberData.data.map((item) => ({
      label: item.date,
      value: item.incidents,
      "Incidents": item.incidents,
      "Unique": item.unique
    }));
  }, [contactNumberData]);

  // Contact Number bar chart config
  const contactNumberChartConfig = {
    "Incidents": {
      label: "Incidents",
      color: "#E8C468"
    },
    "Unique": {
      label: "Unique",
      color: "#274746"
    }
  };

  // Add CSV export functions for each chart
  const handleGeographicalDistributionCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.GEOGRAPHICAL_DISTRIBUTION,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const success = await handleCSVDownloadFromResponse(response, 'geographical_distribution.csv');
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error exporting geographical distribution CSV:', error);
    }
  }, [queryParams]);

  const handleInternationalHostingCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.INTERNATIONAL_HOSTING,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const success = await handleCSVDownloadFromResponse(response, 'international_hosting.csv');
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error exporting international hosting CSV:', error);
    }
  }, [queryParams]);

  const handleHostingProvidersCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.HOSTING_PROVIDERS,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const success = await handleCSVDownloadFromResponse(response, 'hosting_providers.csv');
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error exporting hosting providers CSV:', error);
    }
  }, [queryParams]);

  const handlePlatformInfoCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.PLATFORM_INFO,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const success = await handleCSVDownloadFromResponse(response, 'platform_info.csv');
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error exporting platform info CSV:', error);
    }
  }, [queryParams]);

  const handleContactNumberCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_ANALYTICS.CONTACT_NUMBER,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const success = await handleCSVDownloadFromResponse(response, 'contact_number.csv');
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error exporting contact number CSV:', error);
    }
  }, [queryParams]);

  // Generic CSV download function for tables
  const handleCSVDownload = useCallback(async (apiDetail: string, cardTitle: string) => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      let endpoint = '';
      let payload: any = {
        ...queryParams,
        export_type: "csv"
      };

      // Determine endpoint and payload based on apiDetail
      switch (apiDetail) {
        case "domain_providers_info":
          endpoint = Endpoint.BI.WEB_ANALYTICS.HOSTING_PROVIDERS_INFO;
          payload = {
            ...queryParams,
            domain: selectedProduct ? selectedProduct : "all",
            export_type: "csv"
          };
          break;
        default:
          throw new Error(`Unknown API detail: ${apiDetail}`);
      }

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + endpoint,
        payload,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          },
        }
      );

      // Use the utility function to handle CSV download
      const fileName = `${cardTitle}_${fromDate}_to_${toDate}.csv`;
      const success = await handleCSVDownloadFromResponse(response, fileName);
      
      if (!success) {
        throw new Error('Failed to download CSV file');
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    }
  }, [queryParams, selectedProduct, fromDate, toDate]);

  // Base expand handler
  const handleExpandBase = useCallback((index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  }, [expandedCard]);

  // Individual expand handlers for each chart
  const handleGeographicalMapExpand = useCallback(() => {
    handleExpandBase(3);
  }, [handleExpandBase]);

  const handlePieChartExpand = useCallback(() => {
    handleExpandBase(4);
  }, [handleExpandBase]);

  const handleTopDomainProvidersExpand = useCallback(() => {
    handleExpandBase(5);
  }, [handleExpandBase]);

  const handleWebsitesTableExpand = useCallback(() => {
    handleExpandBase(6);
  }, [handleExpandBase]);

  const handleInvestigateClick = (item: WebsiteData) => {
    console.log("Investigating item:", item);
    // Add your investigation logic here
  };

  // Update the columns type
  const websitesColumns: Column<WebsiteData>[] = [
    {
      key: "website",
      title: "List of Websites",
      render: (item: WebsiteData) => {
        const website = item.website as string;
        const truncatedWebsite = website.length > 20 ? website.substring(0, 20) + '...' : website;
        return (
          <div
            className="cursor-pointer text-primary hover:text-primary hover:underline text-small-font"
            title={website}
            onClick={() => window.open(website, '_blank')}
          >
            {truncatedWebsite}
          </div>
        );
      }
    },
    {
      key: "ip_address",
      title: "IP Address"
    },
    {
      key: "creation_date",
      title: "Creation Date"
    },
    {
      key: "update_date",
      title: "Update Date"
    },
    {
      key: "brand_logo_name",
      title: "Brand Logo Name"
    },
    {
      key: "investigate",
      title: "Investigation",
      render: (item: WebsiteData) => (
        <div
          className="flex items-center gap-2 cursor-pointer text-primary hover:text-primary"
          onClick={() => handleInvestigateClick(item)}
        >
          {/* <FolderSearch className="w-5 h-5 " /> */}
          {/* <span className="text-small-font hover:underline">Investigate More</span> */}
        </div>
      )
    }
  ];

  return (
    <div className="min-h-screen w-full bg-[#F3F4F6]">
      <style>{`
        .domain-provider-table .text-small-font {
          font-size: 0.6rem !important;
        }
        .domain-provider-table td, .domain-provider-table th {
          font-size: 0.6rem !important;
        }
        .domain-provider-table * {
          font-size: 0.6rem !important;
        }
      `}</style>
      {/* Filter Bar */}
      <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)] bg-white dark:bg-gray-700">
        <div className="flex flex-wrap gap-3">
          <FilterPill
            id="brand"
            title="Brand"
            filters={brandFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={!brandFilterData}
            isSearchable={true}
          />
          <FilterPill
            id="priority"
            title="Priority"
            filters={priorityFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={!priorityFilterData}
          />
          <FilterPill
            id="country"
            title="Country"
            filters={countryFilters}
            onSubmit={handleFilterSubmit}
            onSearch={handleFilterSearch}
            loading={!countryFilterData}
            isSearchable={true}
          />
        </div>
      </div>

      {/* First Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2 ">
        {/* Geographical Map Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[3] = el;
          }}
          className={`shadow-md ${expandedCard === 3 ? 'h-[100vh]' : ''}`}
        >
          <HeaderRow
            title="Geographical Distribution"
            onExpand={handleGeographicalMapExpand}
            handleExport={handleGeographicalDistributionCSVExport}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
            isExpanded={expandedCard === 3}
          />
          <CardContent className={expandedCard === 3 ? 'h-[calc(100vh-48px)]' : ''}>
            <div className={`overflow-hidden ${expandedCard === 3 ? 'h-[calc(100vh-120px)]' : 'h-[300px]'}`}>
              {!geographicalDistributionData ? (
                <div className={`flex items-center justify-center w-full h-full ${expandedCard === 3 ? 'min-h-[calc(100vh-120px)]' : 'min-h-[300px]'}`}>
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !geographicalDistributionData.data || geographicalDistributionData.data.length === 0 ? (
                <div className={`flex items-center justify-center w-full h-full ${expandedCard === 3 ? 'min-h-[calc(100vh-120px)]' : 'min-h-[300px]'}`}>
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
                <DynamicBarChart
                  data={chartData}
                  config={geographicalMapChartConfig}
                  isHorizontal={false}
                  onExpand={handleGeographicalMapExpand}
                  position="right"
                  isLoading={!geographicalDistributionData}
                  formatterType="number"
                  isRadioButton={false}
                  isSelect={false}
                  selectoptions={[]}
                  showHeader={false}
                  width="25"
                  isScrollable={true}
                  enableHorizontalScroll={chartData.length > 6}
                  barSize={expandedCard === 3 ? 30 : 20}
                  barGap={0}
                  yAxisTextGap={expandedCard === 3 ? -50 : -34}
                  height={expandedCard === 3 ? window.innerHeight - 120 : 290}
                  xAxisTruncateLength={expandedCard === 3 ? 20 : 12}
                  isExpanded={expandedCard === 3}
                />
              )}
            </div>
          </CardContent>
        </Card>

        {/* Pie Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[4] = el;
          }}
           className={`shadow-md ${expandedCard === 4 ? 'h-[100vh]' : ''}`}
        >
          <HeaderRow
            title="Indian / International Hosting"
            onExpand={handlePieChartExpand}
            handleExport={handleInternationalHostingCSVExport}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
             isExpanded={expandedCard === 4}
          />
           <CardContent className={expandedCard === 4 ? 'h-[calc(100vh-48px)]' : ''}>
             <div className={`w-full overflow-hidden ${expandedCard === 4 ? 'h-[calc(100vh-120px)]' : 'h-[300px]'}`}>
              {!internationalHostingData ? (
                 <div className={`flex items-center justify-center w-full h-full ${expandedCard === 4 ? 'min-h-[calc(100vh-120px)]' : 'min-h-[300px]'}`}>
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !internationalHostingData.data || internationalHostingData.data.length === 0 ? (
                 <div className={`flex items-center justify-center w-full h-full ${expandedCard === 4 ? 'min-h-[calc(100vh-120px)]' : 'min-h-[300px]'}`}>
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
                                 <ResponsiveContainer width="100%" height={expandedCard === 4 ? "100%" : "100%"}>
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                       cy={expandedCard === 4 ? "50%" : "45%"}
                       innerRadius={expandedCard === 4 ? 120 : 70}
                       outerRadius={expandedCard === 4 ? 200 : 120}
                      paddingAngle={0}
                      dataKey="value"
                    >
                      {pieChartData.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                      <Label
                        content={({ viewBox }) => {
                          if (viewBox && "cx" in viewBox && "cy" in viewBox && viewBox.cy !== undefined) {
                            const totalCount = pieChartData.reduce((sum, item) => sum + (item.value || 0), 0);
                            return (
                              <g>
                                <text
                                  x={viewBox.cx}
                                  y={viewBox.cy - 8}
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                  className="fill-foreground font-bold"
                                   style={{ fontSize: expandedCard === 4 ? '28px' : '18px' }}
                                >
                                  {totalCount.toLocaleString()}
                                </text>
                                <text
                                  x={viewBox.cx}
                                  y={viewBox.cy + 12}
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                  className="fill-muted-foreground"
                                   style={{ fontSize: expandedCard === 4 ? '18px' : '12px' }}
                                >
                                  Total Count
                                </text>
                              </g>
                            );
                          }
                          return null;
                        }}
                      />
                    </Pie>
                    <Tooltip
                      content={({ active, payload, label }) => {
                        if (!active || !payload?.length) return null;
                        
                        return (
                           <div className={`grid items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 shadow-xl ${expandedCard === 4 ? 'min-w-[15rem] text-sm' : 'min-w-[10rem] text-xs'}`}>
                            <div className="font-medium bg-gray-100 text-black dark:text-white">
                              {label}
                            </div>
                            <div className="grid gap-1.5">
                              {payload.map((item, index) => {
                                const indicatorColor = item.payload.fill || item.color || "#540094";
                                const percentage = item.payload.percentage;
                                
                                return (
                                  <div
                                    key={item.dataKey}
                                    className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                                  >
                                    <div
                                      className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                      style={
                                        {
                                          "--color-bg": indicatorColor,
                                          "--color-border": indicatorColor,
                                        } as React.CSSProperties
                                      }
                                    />
                                    <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                      <span className="text-muted-foreground">
                                        {item.name || "Value"}
                                      </span>
                                      <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                        {typeof item.value === 'number' ? `${item.value} (${percentage}%)` : item.value}
                                      </span>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      }}
                    />
                    <Legend
                      content={CustomLegend}
                      verticalAlign="bottom"
                      align="center"
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2">

        {/* Top Hosting Providers Chart */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[5] = el;
          }}
          className="shadow-md"
        >
          <CardContent>
            <div className="w-full h-full overflow-hidden">
              <DynamicBarChart
                data={hostingProvidersChartData}
                config={hostingProvidersConfig}
                isHorizontal={true}
                onExpand={handleTopDomainProvidersExpand}
                position="right"
                isLoading={!hostingProvidersData}
                formatterType="number"
                isRadioButton={false}
                isSelect={false}
                selectoptions={[]}
                showHeader={true}
                title="Top Hosting Providers"
                onBarClick={handleBarClick}
                isScrollable={false}
                barSize={20}
                showRefresh={true}
                onRefresh={handleRefresh}
                titleFontSize="text-base"
                handleExport={handleHostingProvidersCSVExport}
                yAxisTextGap={-30}
                yAxisTruncateLength={10}
                showLegend={false}
                height={350}
              />
            </div>
          </CardContent>
        </Card>

        {/* Top Hosting Provider Table */}
        <Card
          ref={(el) => {
            if (el) cardRefs.current[6] = el;
          }}
          className="shadow-md"
        >
          <HeaderRow
            title={selectedProduct ? `${selectedProduct}` : "Hosting Provider Information"}
            onExpand={handleWebsitesTableExpand}
            handleExport={() => handleCSVDownload("domain_providers_info", "Hosting Provider Information")}
            isRadioButton={false}
            isSelect={false}
            titleFontSize="text-base"
          />
          <CardContent className="h-[350px] flex flex-col">
            <div className="flex-1 flex flex-col overflow-hidden">
              <div className="domain-provider-table">
                {!hostingProvidersInfoData ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[300px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                  </div>
                ) : !hostingProvidersInfoData.data || hostingProvidersInfoData.data.length === 0 ? (
                  <div className="flex items-center justify-center w-full h-full min-h-[300px]">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                ) : (
                  <ResizableTable
                    columns={websitesColumns}
                    data={websitesTableData}
                    isSearchable={true}
                    isSelectable={false}
                    isPaginated={true}
                    isLoading={false}
                    headerColor="#DCDCDC"
                    SearchTerm={searchTerm}
                    setSearchTerm={setSearchTerm}
                    height={300}
                    buttonTextName=""
                  />
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Third Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 m-2">
        <Card
          ref={(el) => {
            if (el) cardRefs.current[11] = el;
          }}
          className="shadow-md"
        >
          <CardHeader className="p-2">
            <div className="flex flex-col gap-1">
              <HeaderRow
                title="Platforms"
                onExpand={() => handleExpandBase(11)}
                handleExport={handlePlatformInfoCSVExport}
                isRadioButton={false}
                isSelect={false}
                titleFontSize="text-base"
              />
              <div className="flex justify-start pl-4 gap-4">
              <div className="text-xs font-text-sm">
                  <span style={{ color: '#E8C468', fontWeight: 'bold'}}>Total Incidents Count</span> : <span style={{ color: '#E8C468', fontWeight: 'bold' }}>{!platformInfoData ? 'Loading...' : (
                    platformInfoData?.total_incidents_count || 0
                  )}</span>
                </div>
                <div className="text-xs font-text-sm">
                  <span style={{ color: '#274745', fontWeight: 'bold'}}>Total Unique Count</span> : <span style={{ color: '#274745', fontWeight: 'bold' }}>{!platformInfoData ? 'Loading...' : (
                    platformInfoData?.total_unique_count || 0
                  )}</span>
                </div>
               
              </div>
            </div>
          </CardHeader>
          <CardContent className="w-full min-h-[300px] overflow-hidden">
            <div className="w-full h-full">
              <DynamicBarChart
                data={platformChartData}
                config={PlatformChartConfig}
                isHorizontal={true}
                onExpand={() => handleExpandBase(11)}
                position="right"
                isLoading={!platformInfoData}
                formatterType="number"
                isRadioButton={false}
                isSelect={false}
                selectoptions={[]}
                showHeader={false}
                width="25"
                isScrollable={false}
                yAxisTextGap={-34}
                height={320}
                legendGap={0}
                showLegend={platformChartData.length > 0}
              />
            </div>
          </CardContent>
        </Card>

        <Card
          ref={(el) => {
            if (el) cardRefs.current[2] = el;
          }}
          className="shadow-md"
        >
          <CardHeader className="p-2">
            <div className="flex flex-col gap-1">
              <HeaderRow
                title="Contact Number"
                onExpand={() => handleExpandBase(2)}
                handleExport={handleContactNumberCSVExport}
                isRadioButton={false}
                isSelect={false}
                titleFontSize="text-base"
              />
              <div className="flex justify-start pl-4 gap-4">
              <div className="text-xs font-text-sm">
                  <span style={{ color: '#E8C468', fontWeight: 'bold' }}>Total Incidents Count</span> : <span style={{ color: '#E8C468', fontWeight: 'bold' }}>{!contactNumberData ? 'Loading...' : (
                    contactNumberData?.total_incidents_count || 0
                  )}</span>
                </div>
                <div className="text-xs font-text-sm">
                  <span style={{ color: '#274746', fontWeight: 'bold' }}>Total Unique Count</span> : <span style={{ color: '#274746', fontWeight: 'bold' }}>{!contactNumberData ? 'Loading...' : (
                    contactNumberData?.total_unique_count || 0
                  )}</span>
                </div>
               
              </div>
            </div>
          </CardHeader>
          <CardContent className="w-full min-h-[300px] overflow-hidden">
            <div className="w-full h-full">
              <DynamicBarChart
                data={contactNumberChartData}
                config={contactNumberChartConfig}
                isHorizontal={false}
                onExpand={() => handleExpandBase(2)}
                position="right"
                isLoading={!contactNumberData}
                formatterType="number"
                isRadioButton={false}
                isSelect={false}
                selectoptions={[]}
                showHeader={false}
                width="25"
                isScrollable={true}
                enableHorizontalScroll={contactNumberChartData.length > 8}
                yAxisTextGap={-34}
                height={310}
                legendGap={0}
                showLegend={contactNumberChartData.length > 0}
                barSize={20}
                barGap={2}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Two Tables Section */}
      <div className="flex gap-2 m-2 shadow-md">
        {/* Left Table */}
        <div className="w-1/2 bg-white rounded-lg p-4 bg-white dark:bg-gray-900">
          <div className="mb-4 flex items-center justify-between">
            <h6 className="text-base font-semibold text-gray-800 dark:text-white">Domain Details</h6>
            <Select
              value={leftTableFilter}
              onValueChange={setLeftTableFilter}
            >
              <SelectTrigger className="w-[300px] border-2 text-black dark:text-white">
                <SelectValue placeholder="Select Domain" />
              </SelectTrigger>
              <SelectContent
                position="popper"
                side="bottom"
                align="start"
                className="max-h-[300px] overflow-y-auto"
              >
                {domainDropdownData?.domain_options?.map((domain, index) => {
                  const truncatedDomain = domain.length > 30 ? domain.substring(0, 30) + '...' : domain;
                  return (
                    <SelectItem key={index} value={domain} title={domain}>
                      {truncatedDomain}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse ">
              <thead>
                <tr className="bg-gray-300">
                  <th className="border p-2 text-left text-sm dark:bg-gray-700 text-black dark:text-white">Hosting Provider Details</th>
                  <th className="border p-2 text-left text-sm dark:bg-gray-700 text-black dark:text-white">Value To be Fetched</th>
                </tr>
              </thead>
              <tbody>
                {!domainDropdownData?.domain_options || domainDropdownData.domain_options.length === 0 ? (
                  <tr>
                    <td colSpan={2} className="border p-2 text-center text-black dark:text-white">
                      <div className="flex items-center justify-center w-full h-full min-h-[200px]">
                        <span className="text-sm dark:text-white">No Data Found !</span>
                      </div>
                    </td>
                  </tr>
                ) : !domainsTableData ? (
                  <tr>
                    <td colSpan={2} className="border p-2 text-center text-black dark:text-white">
                      <div className="flex items-center justify-center w-full h-full min-h-[200px]">
                        <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                      </div>
                    </td>
                  </tr>
                ) : !domainsTableData.data ? (
                  <tr>
                    <td colSpan={2} className="border p-2 text-center text-black dark:text-white">
                      <div className="flex items-center justify-center w-full h-full min-h-[200px]">
                        <span className="text-sm dark:text-white">No Data Found !</span>
                      </div>
                    </td>
                  </tr>
                ) : (
                  <>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border p-2 text-xs text-black dark:text-white">- IP Location</td>
                      <td className="border p-2 text-xs text-black dark:text-white">{domainsTableData.data.ip_location || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border p-2 text-xs text-black dark:text-white">- Hosting Provider Name, Country</td>
                      <td className="border p-2 text-xs text-black dark:text-white">{domainsTableData.data.hosting_provider_country || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- Registrar Name, Country</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{domainsTableData.data.registrar_country || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border p-2 text-xs text-black dark:text-white">- Domain Creation Date</td>
                      <td className="border p-2 text-xs text-black dark:text-white">{domainsTableData.data.domain_creation_date || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border p-2 text-xs text-black dark:text-white">- Domain Updated Date</td>
                      <td className="border p-2 text-xs text-black dark:text-white">{domainsTableData.data.domain_update_date || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border p-2 text-black text-xs dark:text-white">- Brand Logo Used</td>
                      <td className="border p-2 text-black text-xs dark:text-white">{domainsTableData.data.brand_logo_used || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border p-2 text-black text-xs dark:text-white">- Brand Name Used</td>
                      <td className="border p-2 text-black text-xs dark:text-white">{domainsTableData.data.brand_name_used || "N/A"}</td>
                    </tr>
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Right Table */}
        <div className="w-1/2 bg-white rounded-lg shadow-md p-4 bg-white dark:bg-gray-900">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-base font-semibold text-gray-800 dark:text-white">Application / APKs Details</h3>
            <Select
              value={rightTableFilter}
              onValueChange={setRightTableFilter}
            >
              <SelectTrigger className="w-[300px] border-2 text-black dark:text-white">
                <SelectValue placeholder="Select APK" />
              </SelectTrigger>
              <SelectContent
                position="popper"
                side="bottom"
                align="start"
                className="max-h-[300px] overflow-y-auto"
              >
                {apkDropdownData?.application_options?.map((apk, index) => {
                  const truncatedApk = apk.length > 30 ? apk.substring(0, 30) + '...' : apk;
                  return (
                    <SelectItem key={index} value={apk} title={apk}>
                      {truncatedApk}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-300">
                  <th className="border p-2 text-sm text-left dark:bg-gray-700 text-black dark:text-white">Application Details</th>
                  <th className="border p-2 text-sm text-left dark:bg-gray-700 text-black dark:text-white">Value to be Fetched</th>
                </tr>
              </thead>
              <tbody>
                {!apkDropdownData?.application_options || apkDropdownData.application_options.length === 0 ? (
                  <tr>
                    <td colSpan={2} className="border p-2 text-center text-black dark:text-white">
                      <div className="flex items-center justify-center w-full h-full min-h-[200px]">
                        <span className="text-sm dark:text-white">No Data Found !</span>
                      </div>
                    </td>
                  </tr>
                ) : !apkTableData ? (
                  <tr>
                    <td colSpan={2} className="border p-2 text-center text-black dark:text-white">
                      <div className="flex items-center justify-center w-full h-full min-h-[200px]">
                        <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                      </div>
                    </td>
                  </tr>
                ) : !apkTableData.data ? (
                  <tr>
                    <td colSpan={2} className="border p-2 text-center text-black dark:text-white">
                      <div className="flex items-center justify-center w-full h-full min-h-[200px]">
                        <span className="text-sm dark:text-white">No Data Found !</span>
                      </div>
                    </td>
                  </tr>
                ) : (
                  <>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- Application Name</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.application_name || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- App Creation Date</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.app_creation_date || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- App Updated Date</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.app_update_date || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- Brand Logo Used</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.brand_logo_used || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- Brand Name Used</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.brand_name_used || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- Developer Details</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.developer_details || "N/A"}</td>
                    </tr>
                    <tr className="hover:bg-gray-50 text-black dark:text-white dark:hover:bg-gray-700">
                      <td className="border text-xs p-2 text-black dark:text-white">- App version & Size</td>
                      <td className="border text-xs p-2 text-black dark:text-white">{apkTableData.data.app_version_size || "N/A"}</td>
                    </tr>
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;