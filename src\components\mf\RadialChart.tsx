"use client"

import { TrendingUp } from "lucide-react"
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from "recharts";
import { useState,useEffect } from "react";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Loader2 } from "lucide-react";

interface RadialChartProps {
    chartData?: { label?: string; [key: string]: string | number | undefined }[];
    chartConfig?: {
      [key: string]: {
        label: string;
        color: string;
      };
  };
  Device?:string;
  Vname?:string;
  Vvalue?:number;
  Oname?:string;
  Ovalue?:number;
  backgroundcolors?:string;
  textcolors?:string;
  value1?:string;
  value2?:string;
  isLoading?:boolean;
};

const RadialBars: React.FC<RadialChartProps> = ({
    chartConfig,
    chartData,
    <PERSON><PERSON>,
    Vname,
    Vvalue ="0",
    Oname,
    value1,
    value2,
   Ovalue ="0",
   backgroundcolors,
   textcolors,
   isLoading,

})=>{

    const [data, setData] = useState<{ label?: string; [key: string]: number | undefined }[]>([]);

    // Initialize with provided chartData or set default values
    useEffect(() => {
      if (chartData) {
        setData(chartData);
      } else {
        // Set default data if chartData is not provided
        setData([
          {
            label: "Visitors",
            key1: 300, // Example value
            key2: 500, // Example value
            key3: 200, // Example value
          },
        ]);
      }
    }, [chartData]);
  
    // Calculate total visitors dynamically based on the keys in the first data object
    const totalVisitors = data.length > 0
      ? Object.values(data[0]).reduce((acc, value) => {
          return acc + (typeof value === "number" ? value : 0);
        }, 0)
      : 0;
  return (
    <Card className="flex flex-col max-h-[250px]">
      <CardHeader className="items-center pb-0">
        <CardTitle className="text-body font-semibold" style={{ color: textcolors }}>
          {Device}
        </CardTitle>
      </CardHeader>

      <CardContent className="relative flex-1 min-h-[150px]">
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            className="mx-auto aspect-square w-full max-w-[200px]"
          >
            {data.length > 0 ? (
              <RadialBarChart
                data={chartData}
                endAngle={180}
                innerRadius={80}
                outerRadius={120}
              //  margin={{ top: 20 }}
              >
                <ChartTooltip
                  cursor={false}
                  content={({ active, payload, label }) => {
                    if (!active || !payload?.length) return null;
                    
                    return (
                      <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                        <div className="font-medium bg-gray-100 text-black dark:text-white">
                          {label}
                        </div>
                        <div className="grid gap-1.5">
                          {payload.map((item, index) => {
                            const itemConfig = chartConfig?.[item.dataKey as keyof typeof chartConfig];
                            const indicatorColor = item.payload.fill || item.color || itemConfig?.color || "#540094";
                            
                            return (
                              <div
                                key={item.dataKey}
                                className="flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
                              >
                                <div
                                  className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                  style={
                                    {
                                      "--color-bg": indicatorColor,
                                      "--color-border": indicatorColor,
                                    } as React.CSSProperties
                                  }
                                />
                                <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                  <span className="text-muted-foreground">
                                    {itemConfig?.label || item.name || "Value"}
                                  </span>
                                  <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                    {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  }}
                />
                <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
                  <Label
                    content={({ viewBox }) => {
                      if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                        return (
                          <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle">
                            <tspan
                              x={viewBox.cx}
                              y={(viewBox.cy || 0) - 16}
                              className="fill-foreground text-body font-bold"
                            >
                              {totalVisitors.toLocaleString()}
                            </tspan>
                            <tspan
                              x={viewBox.cx}
                              y={(viewBox.cy || 0) + 4}
                              className="fill-muted-foreground"
                            >
                            
                            </tspan>
                          </text>
                        )
                      }
                    }}
                  />
                </PolarRadiusAxis>
                <RadialBar
                  dataKey={value1 || "Visit %"}
                  stackId="a"
                  cornerRadius={5}
                  fill={chartConfig[value1 || "Visit %"].color}
                  className="stroke-transparent stroke-2"
                />
                <RadialBar
                 dataKey={value2 || "Event %"}
                  fill={chartConfig[value2 || "Event %"].color}
                  stackId="a"
                  cornerRadius={5}
                  className="stroke-transparent stroke-2"
                />
              </RadialBarChart>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-sm dark:text-white">No Data Found !</span>
              </div>
            )}
          </ChartContainer>
        )}
      </CardContent>
      <CardFooter className="flex-col pb-10">
        <div className={`${backgroundcolors}`} style={{ color: textcolors }}>
          <div className="grid grid-cols-4  gap-2 font-semibold text-body ml-1">
            <div>{Vname}:</div>
            <div>{Vvalue ?? 0}%</div>
            <div>{Oname}:</div>
            <div>{Ovalue ?? 0}%</div>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
export default RadialBars;
