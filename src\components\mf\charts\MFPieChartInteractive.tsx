"use client"

import * as React from "react"
import { Label, Pie, PieChart, Sector } from "recharts"
import { PieSectorDataItem } from "recharts/types/polar/Pie"

import {
  ChartConfig,
  ChartContainer,
  ChartStyle,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

export const description = "An interactive pie chart"

const chartConfig = {
  visitors: {
    label: "Visitors",
  },
  desktop: {
    label: "Desktop",
  },
  mobile: {
    label: "Mobile",
  },
  january: {
    label: "January",
    color: "var(--chart-1)",
  },
  february: {
    label: "February",
    color: "var(--chart-2)",
  },
  march: {
    label: "March",
    color: "var(--chart-3)",
  },
  april: {
    label: "April",
    color: "var(--chart-4)",
  },
  may: {
    label: "May",
    color: "var(--chart-5)",
  },
} satisfies ChartConfig

interface LegendItem {
  label: string;
  color: string;
}

interface MFPieChartInteractiveProps {
  legends: LegendItem[];
  data: { label: string; value: number; fill: string }[];
}

export function MFPieChartInteractive({ legends, data }: MFPieChartInteractiveProps) {
  const id = "pie-interactive";
  const [activeLegend, setActiveLegend] = React.useState(legends[0]?.label || "");

  // Map legend label to data index (assuming data order matches legends)
  const activeIndex = React.useMemo(
    () => legends.findIndex((item) => item.label === activeLegend),
    [activeLegend, legends]
  );

  return (
    <div data-chart={id} className="w-full h-full flex flex-row items-center justify-center">
      <ChartStyle id={id} config={chartConfig} />
      <div className="flex justify-center items-center">
        <ChartContainer
          id={id}
          config={chartConfig}
          className="aspect-square w-[260px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={data}
              dataKey="value"
              nameKey="label"
              innerRadius={60}
              strokeWidth={5}
              activeIndex={activeIndex}
              activeShape={({
                outerRadius = 0,
                ...props
              }: PieSectorDataItem) => (
                <g>
                  <Sector {...props} outerRadius={outerRadius + 10} />
                  <Sector
                    {...props}
                    outerRadius={outerRadius + 25}
                    innerRadius={outerRadius + 12}
                  />
                </g>
              )}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-3xl font-bold"
                        >
                          {data[activeIndex]?.value?.toLocaleString()}%
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground"
                        >
                          Incidents
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </div>

      {/* Right aligned and vertically centered legend */}
      <div className="flex flex-col justify-center gap-1 ml-8 w-[120px]">

        {legends.map((legend, idx) => (
          <div
            key={legend.label}
            className={`flex items-center gap-2 p-2 text-sub-header cursor-pointer transition-opacity duration-200 ${activeLegend === legend.label ? 'opacity-100 font-semibold' : 'opacity-70'}`}
            onClick={() => setActiveLegend(legend.label)}
            style={{ userSelect: 'none', fontSize: '0.75rem' }}
          >
            <span
              className="inline-block rounded-full"
              style={{ backgroundColor: legend.color, width: 16, height: 16 }}
            />
            <span>{legend.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
