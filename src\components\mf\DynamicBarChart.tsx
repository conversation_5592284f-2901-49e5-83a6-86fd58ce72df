"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, Car<PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LabelList } from "recharts";
import {formatValue} from '@/lib/utils';
import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import HeaderRow from "./HeaderRow";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useFullscreen } from "@/hooks/fullscreen";

interface data {
  label: string;
  value: number;
  percentage?: number;
  [key: string]: any;
}

interface config {
  [key: string]: {
    color: string;
    label: string;
  };
}
import { Loader2 } from "lucide-react";

interface CustomTickProps {
  x: number;
  y: number;
  payload: {
    value: string;
  };
  chartConfig: config;
}

interface BarClickData {
  label: string;
  value: number;
  percentage?: number;
}

interface DynamicBarChartProps {
  data: data[];
  config: config;
  xAxisTitle?: string;
  yAxisT<PERSON>le?: string;
  isHorizontal: boolean;
  multiple?:string;
  title?: string;
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  isRadioButton: boolean;
  isSelect: boolean;
  dynamicTitle?: string;
  formatterType?: "number" | "percentage"
  position?: "top" | "bottom" | "left" | "right"
  isLoading: boolean
  AxisLabel?:string;
  selectoptions?:string[] ;
  placeholder?: string;
  handleFrequencyChange?: (value: string) => void; 
  width?:string;
  selectedFrequency?:string;
  onBarClick?: (data: BarClickData) => void;
  showHeader?: boolean;
  isScrollable?: boolean;
  barSize?: number;
  showRefresh?: boolean;
  onRefresh?: () => void;
  titleFontSize?: string;
  height?: number;
  xAxisTruncateLength?: number;
  yAxisTextGap?: number;
  yAxisTruncateLength?: number;
  enableHorizontalScroll?: boolean;
  barGap?: number;
  showLegend?: boolean;
  legendGap?: number;
  isExpanded?: boolean; // New prop for fullscreen state
}

export function DynamicBarChart({
  data,
  config,
  xAxisTitle,
  isLoading,
  yAxisTitle,
  isHorizontal = false,
  handleExport,
  onExport,
  onExpand,
  multiple,
   placeholder,
   handleFrequencyChange,
  isSelect,
  selectedFrequency,
  selectoptions=[],
  AxisLabel,
  isRadioButton,
  title,
  dynamicTitle,
  formatterType = "number",
  position = "right",
  width,
  onBarClick,
  showHeader = true,
  isScrollable = true,
  barSize = 30,
  showRefresh = false,
  onRefresh,
  titleFontSize = "text-base",
  height = 280,
  xAxisTruncateLength,
  yAxisTextGap = 0,
  yAxisTruncateLength,
  enableHorizontalScroll = false,
  barGap = 0,
  showLegend = true,
  legendGap = 16,
  isExpanded = false, // New prop for fullscreen state
}: DynamicBarChartProps) {

  const isFullscreen = useFullscreen();
  
  // Function to get chart height based on fullscreen state
  const getFullscreenChartHeight = () => {
    if (isExpanded || isFullscreen) {
      return Math.max(window.innerHeight - 120, 400);
    }
    return height;
  };

  // Function to get bar size based on fullscreen state
  const getFullscreenBarSize = () => {
    if (isExpanded || isFullscreen) {
      return Math.max(barSize * 1.5, 40);
    }
    return barSize;
  };

  // Function to get legend gap based on fullscreen state
  const getFullscreenLegendGap = () => {
    if (isExpanded || isFullscreen) {
      return Math.max(legendGap * 1.2, 20);
    }
    return legendGap;
  };

  const currentHeight = getFullscreenChartHeight();
  const currentBarSize = getFullscreenBarSize();
  const currentLegendGap = getFullscreenLegendGap();

  const CustomSquareLegend = ({ payload }: { payload?: any[] }) => {
    if (!payload) return null;
    // Calculate dynamic height based on data length
    const baseHeight = 50; // Base height for each data entry
    const dynamicHeight = Math.max(data.length * baseHeight, 200); // Minimum height of 200px 
    return (
      <div className="flex flex-wrap justify-center items-center gap-4" style={{ marginTop: `${currentLegendGap}px` }}>
        {payload.map((entry, index) => (
          <div key={`legend-${index}`} className="flex items-center gap-2">
            <div
              style={{
                width: 12,
                height: 12,
                backgroundColor: entry.color,
                borderRadius: 6,
              }}
            ></div>
            <span style={{ fontSize: "12px" }}>{entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  const CustomTick = ({ x, y, payload, chartConfig, data, isHorizontal, yAxisTruncateLength, xAxisTruncateLength, yAxisTextGap }: CustomTickProps & { data: any[]; isHorizontal: boolean; yAxisTruncateLength?: number; xAxisTruncateLength?: number; yAxisTextGap?: number; }) => {
    const label = (chartConfig[payload.value]?.label || payload.value || '').toString();
        // Determine if this is a y-axis tick (horizontal chart) or x-axis tick
    const isYAxisTick = isHorizontal;
    
    // Apply truncation based on axis type
    let displayLabel = label;
    if (isYAxisTick && yAxisTruncateLength && label.length > yAxisTruncateLength) {
      displayLabel = `${label.slice(0, yAxisTruncateLength)}...`;
    } else if (!isYAxisTick && xAxisTruncateLength && label.length > xAxisTruncateLength) {
      displayLabel = `${label.slice(0, xAxisTruncateLength)}...`;
    }
    
    // Get the full label for tooltip - check if data has fullLabel property
    const dataItem = data.find((item: any) => item.label === payload.value);
    const tooltipLabel = dataItem?.fullLabel || label;

    return (
      <g transform={`translate(${x},${y})`}>
        <title>{tooltipLabel}</title>
        <text
          x={0}
          y={0}
          dy={8}
          dx={isHorizontal ? yAxisTextGap : -8}
          textAnchor="middle"
          fontSize={12}
          className="truncate w-30"
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            width: "80px",
          }}
        >
          {displayLabel}
        </text>
      </g>
    );
  };
  const dataKeys = Object.keys(config || {});

  
  const formatLabel = (value: number) => {
    if (formatterType === "percentage") {
      return `${value}%`;
    }else{
    return `${value}`;
    }
  };
  return (

    <Card className="flex flex-col border-none">
      {showHeader && (
        <>
          <HeaderRow
            title={title || ""}
            onExpand={onExpand}
            handleExport={handleExport}
            isRadioButton={isRadioButton}
            isSelect={isSelect}
            selectoptions={selectoptions}
            onExport={onExport}
            placeholder={placeholder}
            handleFrequencyChange={handleFrequencyChange}
            isMultiplSelect={true}
            width={width}
            selectedFrequency={selectedFrequency}
            showRefresh={showRefresh}
            onRefresh={onRefresh}
            titleFontSize={titleFontSize}
          />
          <CardHeader className="items-center pb-0">
            <CardTitle className="text-body font-semibold">{dynamicTitle}</CardTitle>
          </CardHeader>
        </>
      )}
      <CardContent className={`flex-1 ${isScrollable ? (enableHorizontalScroll ? 'overflow-x-auto' : 'overflow-y-auto') : 'overflow-hidden'}`} style={{ height: `${currentHeight}px` }}>

        {isLoading ? (
          <div className="flex items-center justify-center w-full" style={{ height: `${currentHeight - 80}px` }}>
            <Loader2 className=" h-8 w-8 animate-spin text-primary dark:text-white" />
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* Scrollable Chart Area */}
            <div className={`flex-1 ${enableHorizontalScroll ? 'overflow-x-auto' : 'overflow-hidden'}`}>
              <ChartContainer config={config} style={{ height: `${currentHeight - 60}px`, width: enableHorizontalScroll ? `${Math.max(data.length * 120, 800)}px` : "100%" }}>
            {data?.length > 0 ? (
              enableHorizontalScroll ? (
                <BarChart
                  data={data}
                  layout={isHorizontal ? "vertical" : "horizontal"}
                  barGap={barGap}
                  margin={{ top: 0, right: 10, bottom: 0, left: 10 }}
                  width={Math.max(data.length * 120, 800)}
                  height={currentHeight - 10}
                >
                  {isHorizontal ? (
                    <XAxis className="text-body"
                      type="number"
                      interval={0}
                      label={{ value: xAxisTitle, position: "insideBottom", offset: -5 }}
                      tickFormatter={(value) => formatValue(value as number, AxisLabel || '')}
                      style={{fontSize:'10px'}}
                    />
                  ) : (
                    <XAxis className="text-body"
                      dataKey="label"
                      interval={0}
                      label={{ value: xAxisTitle, position: "insideBottom", offset: -5, }}
                      tick={(props) => <CustomTick {...props} chartConfig={config} data={data} isHorizontal={isHorizontal} yAxisTruncateLength={yAxisTruncateLength} xAxisTruncateLength={xAxisTruncateLength} yAxisTextGap={yAxisTextGap} />}
                      style={{fontSize:'14px'}}
                    />
                  )}
                  {isHorizontal ? (
                    <YAxis className="mr-10 text-black dark:text-white"
                      type="category"
                      dataKey="label"
                      width={70}
                      label={{
                        value: yAxisTitle,
                        angle: -90,
                        position: "insideLeft",
                        offset: 0
                      }}
                      tick={(props) => <CustomTick {...props} chartConfig={config} data={data} isHorizontal={isHorizontal} yAxisTruncateLength={yAxisTruncateLength} xAxisTruncateLength={xAxisTruncateLength} yAxisTextGap={yAxisTextGap} />}
                      style={{ fontSize: '14px' }}
                    />
                  ) : (
                    <YAxis className="text-black dark:text-white font-semibold"
                      label={{
                        value: yAxisTitle,
                        angle: -90,
                        position: "insideLeft",
                      }}
                      tickFormatter={(value) => {
                        const displayValue = value.length > 3 ? value.slice(0, 5) + "..." : value;
                        return displayValue;
                      }}
                      tick={(props) => <CustomTick {...props} chartConfig={config} data={data} isHorizontal={isHorizontal} yAxisTruncateLength={yAxisTruncateLength} xAxisTruncateLength={xAxisTruncateLength} yAxisTextGap={yAxisTextGap} />}
                      style={{ fontSize: '14px' }}
                    />
                  )}
                  <ChartTooltip content={<ChartTooltipContent formatterType={formatterType} />} />
                  {dataKeys.map((key) => (
                    <Bar
                      key={key}
                      dataKey={key}
                      fill={config[key].color}
                      onClick={(data) => onBarClick && onBarClick(data)}
                      cursor="pointer"
                      barSize={currentBarSize}
                    >
                      {/* Add LabelList here to show labels on the bars */}
                      <div className="dark:text-white">
                        <LabelList
                          dataKey={key}
                          position={position}
                          style={{ fontSize: "12px", fill: "#000" }}
                          formatter={formatLabel}
                        />
                      </div>
                    </Bar>
                  ))}
                </BarChart>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={data}
                    layout={isHorizontal ? "vertical" : "horizontal"}
                    barGap={barGap}
                    margin={{ top: 0, right: 10, bottom: 0, left: 10 }}
                  >
                    {isHorizontal ? (
                      <XAxis className="text-body"
                        type="number"
                        interval={0}
                        label={{ value: xAxisTitle, position: "insideBottom", offset: -5 }}
                        tickFormatter={(value) => formatValue(value as number, AxisLabel || '')}
                        style={{fontSize:'10px'}}
                      />
                    ) : (
                      <XAxis className="text-body"
                        dataKey="label"
                        interval={0}
                        label={{ value: xAxisTitle, position: "insideBottom", offset: -5, }}
                        tick={(props) => <CustomTick {...props} chartConfig={config} data={data} isHorizontal={isHorizontal} yAxisTruncateLength={yAxisTruncateLength} xAxisTruncateLength={xAxisTruncateLength} yAxisTextGap={yAxisTextGap} />}
                        style={{fontSize:'14px'}}
                      />
                    )}
                    {isHorizontal ? (
                      <YAxis className="mr-10 text-black dark:text-white"
                        type="category"
                        dataKey="label"
                        width={70}
                        label={{
                          value: yAxisTitle,
                          angle: -90,
                          position: "insideLeft",
                          offset: 0
                        }}
                        tick={(props) => <CustomTick {...props} chartConfig={config} data={data} isHorizontal={isHorizontal} yAxisTruncateLength={yAxisTruncateLength} xAxisTruncateLength={xAxisTruncateLength} yAxisTextGap={yAxisTextGap} />}
                        style={{ fontSize: '14px' }}
                      />
                    ) : (
                      <YAxis className="text-black dark:text-white font-semibold"
                        label={{
                          value: yAxisTitle,
                          angle: -90,
                          position: "insideLeft",
                        }}
                        tickFormatter={(value) => {
                          const displayValue = value.length > 3 ? value.slice(0, 5) + "..." : value;
                          return displayValue;
                        }}
                        tick={(props) => <CustomTick {...props} chartConfig={config} data={data} isHorizontal={isHorizontal} yAxisTruncateLength={yAxisTruncateLength} xAxisTruncateLength={xAxisTruncateLength} yAxisTextGap={yAxisTextGap} />}
                        style={{ fontSize: '14px' }}
                      />
                    )}
                    <ChartTooltip content={<ChartTooltipContent formatterType={formatterType} />} />
                    {dataKeys.map((key) => (
                      <Bar
                        key={key}
                        dataKey={key}
                        fill={config[key].color}
                        onClick={(data) => onBarClick && onBarClick(data)}
                        cursor="pointer"
                        barSize={currentBarSize}
                      >
                        {/* Add LabelList here to show labels on the bars */}
                        <div className="dark:text-white">
                          <LabelList
                            dataKey={key}
                            position={position}
                            style={{ fontSize: "12px", fill: "#000" }}
                            formatter={formatLabel}
                          />
                        </div>
                      </Bar>
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              )
            ) : (
              <div className="flex items-center justify-center w-full h-full">
                <span className="text-sm dark:text-white">  No Data Found !</span>
              </div>
            )}
          </ChartContainer>
            </div>
            
            {/* Fixed Legend below scrollbar */}
            {showLegend && (
              <div className="flex justify-center mt-2">
                <CustomSquareLegend payload={Object.keys(config).map(key => ({
                  value: config[key].label,
                  color: config[key].color,
                  type: 'circle'
                }))} />
              </div>
            )}
          </div>
        )}
      </CardContent>

    </Card>
  );
}
export default DynamicBarChart;
