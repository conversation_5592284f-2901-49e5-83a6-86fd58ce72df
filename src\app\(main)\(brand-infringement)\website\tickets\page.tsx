"use client";
import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Filter,
  MoreHorizontal,
  Clock,
  Users,
  File,
  CircleX,
  CheckCircleIcon,
  FileText,
  Paperclip,
  TrendingUp,
  Edit,
  Trash2,
  Eye,
  MessageCircle,
  Send,
  Clock4,
  Minus,
  Zap,
  TriangleAlert,
  Triangle,
  CheckCircle,
  AlertCircle,
  XCircle,
  RefreshCw,
  Calendar,
  Tag,
  Archive,
  User,
  Loader2,
  Folder,
} from "lucide-react";
import TicketingTable from "@/components/mf/TicketingTable";
import type { Ticket, Column } from "@/components/mf/TicketingTable";
import CircularProgress from "@/components/mf/CircularProgress";
import {
  useFetchUsers,
  type User as ApiUser,
  useGetTickets,
  type TicketData,
  type TicketsResponse,
  useFetchProjects,
  useGetTicketById,
  useUpdateTicket,
  useGetTicketLog,
  type TicketLogData,
  type TicketLogResponse,
  useTicketOverview,
} from "../tickets/queries/ticket-api";

// API base URL
const TICKET_API_BASE =
  "https://dev-ticket-protal.mfilterit.net/v1/ticket_portal/ticket_portal";
import ToastContent, { ToastType } from "@/components/mf/ToastContent";
import EllipsisTooltip from "@/components/mf/EllipsisTooltip";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";

interface Comment {
  id: string;
  author: string;
  content: string;
  timestamp: string;
  isInternal: boolean;
}

interface ActivityHistory {
  id: string;
  type:
    | "status_change"
    | "priority_change"
    | "assignee_change"
    | "comment_added"
    | "ticket_created"
    | "ticket_updated"
    | "attachment_added";
  description: string;
  author: string;
  timestamp: string;
  oldValue?: string;
  newValue?: string;
  metadata?: {
    project?: string;
    tracker?: string;
    category?: string;
    commentId?: string;
    commentContent?: string;
    changes?: Record<string, { old: string; new: string }>;
  };
}



interface EditTicketFormErrors {
  project?: string;
  subject?: string;
  description?: string;
  status?: string;
  priority?: string;
  assignee?: string;
  due_date?: string;
  percent_done?: string;
}



// Edit Ticket Modal Component
interface EditTicketForm {
  project: string;
  subject: string;
  description: string;
  status:
    | "new"
    | "open"
    | "in_progress"
    | "resolved"
    | "closed"
    | "cancelled"
    | "draft";
  priority: "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate";
  assignee: string;
  category: string;
  start_date: string;
  due_date: string;
  percent_done: string;
}

interface EditTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  editForm: EditTicketForm;
  setEditForm: React.Dispatch<React.SetStateAction<EditTicketForm>>;
  onUpdateTicket: () => void;
  editingTicket: Ticket | null;
  projects: string[];
  isLoadingProjects: boolean;
  users: string[];
  isLoadingUsers: boolean;
  isLoadingTicketData?: boolean;
  getAssigneeColor: (assignee: string | undefined) => string;
  getStatusColor: (status: string) => string;
  normalizeStatus: (status: string) => string;
  selectedTicketLogs: TicketLogData[];
  statusColors: Record<string, string>;
  statusIcons: Record<string, React.ReactNode>;
  statusMap: Record<string, "raised" | "new" | "closed" | "resolved">;
  assigneeColors: Record<string, string>;
  ticketLogApi: any;
  currentTicketId: string | null;
  errors: EditTicketFormErrors;
  clearErrors: () => void;
}

const EditTicketModal = React.memo(
  ({
    isOpen,
    onClose,
    editForm,
    setEditForm,
    onUpdateTicket,
    editingTicket,
    projects,
    isLoadingProjects,
    users,
    isLoadingUsers,
    isLoadingTicketData = false,
    getAssigneeColor,
    getStatusColor,
    normalizeStatus,
    selectedTicketLogs,
    statusColors,
    statusIcons,
    statusMap,
    assigneeColors,
    ticketLogApi,
    currentTicketId,
    errors,
    clearErrors,
  }: EditTicketModalProps) => {
    

    const handleFormChange = useCallback(
      (field: keyof EditTicketForm, value: any) => {
       
        // Ensure value is a string for string fields
        if (typeof value === "object" && value !== null) {
          
          return; // Don't update if it's an object
        }
        setEditForm((prev) => ({ ...prev, [field]: value }));
        // Clear error for this field when user starts typing
        if (errors?.[field as keyof EditTicketFormErrors]) {
          clearErrors();
        }
      },
      [setEditForm, errors, clearErrors]
    );
    console.log("yaha chl rha",editForm.assignee);
    console.log("users",users);
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent
          className="max-w-4xl max-h-[90vh] overflow-y-auto"
          aria-describedby="edit-ticket-description"
        >
          <DialogHeader>
            <DialogTitle>
              {isLoadingTicketData
                ? "Loading ticket data..."
                : `Assign ${editingTicket?.id}`}
            </DialogTitle>
          </DialogHeader>
          <div id="edit-ticket-description" className="sr-only">
            Form to edit ticket details including project, subject, description,
            and other fields
          </div>
          <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2 relative">
            {isLoadingTicketData && (
              <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center z-10">
                <div className="flex items-center space-x-2">
                  {/* <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Loading ticket data...</span> */}
                  <Loader2 className="animate-spin text-primary" />
                </div>
              </div>
            )}


            {/* Subject */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-subject">Subject *</Label>
                <Input
                  id="edit-subject"
                  placeholder="Enter ticket subject"
                  value={editForm.subject}
                  onChange={(e) => handleFormChange("subject", e.target.value)}
                  disabled={true}
                  className={errors.subject ? "border-red-500" : ""}
                />
                {errors.subject && (
                  <p className="text-sm text-red-500 mt-1">{errors.subject}</p>
                )}
              </div>
              <div>
                <Label htmlFor="edit-status">Status *</Label>
                <Select
                  value={editForm.status}
                  onValueChange={(value: any) =>
                    handleFormChange("status", value)
                  }
                >
                  <SelectTrigger className={errors.status ? "border-red-500" : ""}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="raised">Raised</SelectItem>
                    <SelectItem value="in progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500 mt-1">{errors.status}</p>
                )}
              </div>
            </div>

            {/* Status and Priority Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-priority">Priority *</Label>
                <Select
                  value={editForm.priority}
                  onValueChange={(value: any) =>
                    handleFormChange("priority", value)
                  }
                  disabled={true}
                >
                  <SelectTrigger className={errors.priority ? "border-red-500" : ""}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Normal">Normal</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Urgent">Urgent</SelectItem>
                    <SelectItem value="Immediate">Immediate</SelectItem>
                  </SelectContent>
                </Select>
                {errors.priority && (
                  <p className="text-sm text-red-500 mt-1">{errors.priority}</p>
                )}
              </div>
              <div>
                <Label htmlFor="edit-assignee">Assignee *</Label>
                <Select
                  value={editForm.assignee || ""}
                  onValueChange={(value) => handleFormChange("assignee", value)}
                  disabled={isLoadingUsers}
                >
                  <SelectTrigger className={errors.assignee ? "border-red-500" : ""}>
                    <SelectValue
                      placeholder={
                        isLoadingUsers ? "Loading users..." : "Select assignee"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingUsers ? (
                      <SelectItem value="loading-users" disabled>
                        Loading users...
                      </SelectItem>
                    ) : users.length > 0 ? (
                      users.map((user) => (
                        <SelectItem key={user} value={user}>
                          {user}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-users" disabled>
                        No users available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.assignee && (
                  <p className="text-sm text-red-500 mt-1">{errors.assignee}</p>
                )}
              </div>
            </div>

            {/* Date Range Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-due_date">Incident Date *</Label>
                <Input
                  id="edit-due_date"
                  type="date"
                  min={new Date().toISOString().split('T')[0]}
                  value={editForm.due_date}
                  onChange={(e) => handleFormChange("due_date", e.target.value)}
                  className={errors.due_date ? "border-red-500" : ""}
                />
                {errors.due_date && (
                  <p className="text-sm text-red-500 mt-1">{errors.due_date}</p>
                )}
              </div>
              <div>
                <Label htmlFor="edit-percent_done">Percent Done *</Label>
                <Select
                  value={editForm.percent_done}
                  onValueChange={(value) =>
                    handleFormChange("percent_done", value)
                  }
                >
                  <SelectTrigger className={errors.percent_done ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select progress" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0%">0%</SelectItem>
                    <SelectItem value="25%">25%</SelectItem>
                    <SelectItem value="50%">50%</SelectItem>
                    <SelectItem value="75%">75%</SelectItem>
                    <SelectItem value="100%">100%</SelectItem>
                  </SelectContent>
                </Select>
                {errors.percent_done && (
                  <p className="text-sm text-red-500 mt-1">{errors.percent_done}</p>
                )}
              </div>
            </div>



            {/* Description */}
            <div>
              <Label htmlFor="edit-description">Description *</Label>
              <Textarea
                id="edit-description"
                placeholder="Describe the issue in detail"
                rows={4}
                value={editForm.description}
                onChange={(e) =>
                  handleFormChange("description", e.target.value)
                }
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description}</p>
              )}
            </div>

            <div className="flex justify-end space-x-2">
              <Button onClick={onClose} disabled={isLoadingTicketData}>
                Cancel
              </Button>
              <Button onClick={onUpdateTicket} disabled={isLoadingTicketData}>
                Update Ticket
              </Button>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 dark:border-gray-700 my-6"></div>

            {/* Ticket Overview Section */}
            {editingTicket && (
              <div className="space-y-6">
                {/* Ticket Overview Card */}
                <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50 rounded-2xl border border-gray-200/60 dark:border-gray-600/60 shadow-lg overflow-hidden">
                  {/* Header */}
                  <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                          <FileText className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-white">
                            {(() => {
                              // Get the title from the first object in selectedTicketLogs
                              if (
                                selectedTicketLogs &&
                                selectedTicketLogs.length > 0
                              ) {
                                const firstLog = selectedTicketLogs[0];
                                if (
                                  firstLog.details &&
                                  firstLog.details.title
                                ) {
                                  return firstLog.details.title;
                                }
                              }
                              return "Ticket Overview";
                            })()}
                          </h3>
                        </div>
                      </div>
                      <Badge className="bg-white/20 text-white border-white/30 text-xs px-3 py-1">
                        #{editingTicket.id}
                      </Badge>
                    </div>
                  </div>

                  {/* Content - Single Unified Layout */}
                  <div className="p-6">
                    <div className="space-y-4">
                      {/* First Row - Three Columns */}
                      <div className="flex justify-between items-center">
                        {/* Column 1 - Created By */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Created By:
                          </div>
                          <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                const creator =
                                  firstLog.action_by ||
                                  editingTicket?.author_name ||
                                  "Unknown";
                                return creator;
                              }
                              return editingTicket?.author_name || "Unknown";
                            })()}
                          </div>
                        </div>

                        {/* Column 2 - Current Assignee */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Assignee:
                          </div>
                          <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                const assignee =
                                  firstLog.new_state?.assignee ||
                                  firstLog.details?.assignee ||
                                  editingTicket?.assignee ||
                                  "Unassigned";
                                return assignee;
                              }
                              return editingTicket?.assignee || "Unassigned";
                            })()}
                          </div>
                        </div>

                        {/* Column 3 - Status */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Status:
                          </div>
                          <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                return (
                                  firstLog.new_state?.status ||
                                  editingTicket?.status ||
                                  "new"
                                );
                              }
                              return typeof editingTicket?.status === "string"
                                ? normalizeStatus(
                                    editingTicket.status
                                  ).replace("_", " ")
                                : "new";
                            })()}
                          </div>
                        </div>
                      </div>

                      {/* second Row - Additional Fields */}
                      <div className="flex justify-between items-center">
                        {/* Column 1 - Percent Done */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Percent Done:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                             
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );

                              const firstLog = sortedLogs[0];

                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                               

                                // Get percent done from the ticket creation log
                                let percentDone =
                                  firstLog.new_state?.percent_done;

                                // If not found in new_state, check if it's in details
                                if (
                                  !percentDone &&
                                  firstLog.details?.percent_done
                                ) {
                                  if (
                                    typeof firstLog.details.percent_done ===
                                      "object" &&
                                    firstLog.details.percent_done.new
                                  ) {
                                    percentDone =
                                      firstLog.details.percent_done.new;
                                  } else if (
                                    typeof firstLog.details.percent_done ===
                                    "string"
                                  ) {
                                    percentDone = firstLog.details.percent_done;
                                  }
                                }

                                const result = percentDone || "0%";
                                return result;
                              }
                             
                              return "0%";
                            })()}
                          </div>
                        </div>

                        {/* Column 2 - Start Date */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Start Date:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              if (!editingTicket?.start_date) return "Not set";
                              try {
                                const date = new Date(
                                  editingTicket.start_date
                                );
                                return date.toISOString().split("T")[0]; // Returns YYYY-MM-DD format
                              } catch {
                                return editingTicket.start_date;
                              }
                            })()}
                          </div>
                        </div>

                        {/* Column 3 - Due Date */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Due Date:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              if (!editingTicket?.due_date) return "Not set";
                              try {
                                const date = new Date(editingTicket.due_date);
                                return date.toISOString().split("T")[0]; // Returns YYYY-MM-DD format
                              } catch {
                                return editingTicket.due_date;
                              }
                            })()}
                          </div>
                        </div>
                      </div>

                      {/* Third Row - Additional Fields */}
                      <div className="flex justify-between items-center">
                        {/* Column 1 - Priority */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Priority:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {editingTicket?.priority || "Medium"}
                          </div>
                        </div>

                        {/* Column 2 - Tracker */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Tracker:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                return (
                                  firstLog.new_state?.tracker ||
                                  firstLog.details?.tracker ||
                                  "Not set"
                                );
                              }
                              return "Not set";
                            })()}
                          </div>
                        </div>

                        {/* Column 3 - Estimate Time */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Estimate Time:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                const estimateTime =
                                  firstLog.new_state?.estimate_time;
                                return estimateTime
                                  ? `${estimateTime} hours`
                                  : "Not set";
                              }
                              return "Not set";
                            })()}
                          </div>
                        </div>
                      </div>

                      {/* Fourth Row - Description */}
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          Description:
                        </div>
                        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 flex-1">
                          {(() => {
                            const sortedLogs = [...selectedTicketLogs].sort(
                              (a, b) =>
                                new Date(a.timestamp).getTime() -
                                new Date(b.timestamp).getTime()
                            );
                            const firstLog = sortedLogs[0];
                            const description =
                              firstLog &&
                              firstLog.action_type === "Ticket Created"
                                ? firstLog.new_state?.description
                                : editingTicket?.description;

                            return description || "No description available";
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* History Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                        Activity History
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Track all changes and updates to this ticket
                      </p>
                    </div>
                  </div>
                </div>

                {ticketLogApi.type === "mutation" &&
                ticketLogApi.loading &&
                currentTicketId ? (
                  <div className="flex flex-col items-center justify-center py-16">
                    <div className="relative">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg animate-pulse">
                        <Loader2 className="w-8 h-8 text-white animate-spin" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                    </div>
                    <p className="mt-4 text-gray-600 dark:text-gray-400 font-medium">
                      Loading activity history...
                    </p>
                  </div>
                ) : selectedTicketLogs.length > 0 ? (
                  <div className="relative">
                    {/* Timeline container */}
                    <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-indigo-200 to-purple-200 dark:from-blue-700 dark:via-indigo-700 dark:to-purple-700"></div>

                    <div className="space-y-8">
                      {selectedTicketLogs
                        .filter((log) => log.action_type !== "Ticket Created")
                        .sort(
                          (a, b) =>
                            new Date(b.timestamp).getTime() -
                            new Date(a.timestamp).getTime()
                        )
                        .map((log, index) => (
                          <div key={log._id} className="relative">
                            {/* Timeline dot */}
                            <div className="absolute left-6 top-4 w-4 h-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full border-4 border-white dark:border-gray-800 shadow-lg z-10"></div>

                            {/* Activity card */}
                            <div className="ml-12 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
                              {/* Card header */}
                              <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                                      {log.action_type === "Ticket Created" && (
                                        <FileText className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type === "Status Changed" && (
                                        <RefreshCw className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type ===
                                        "Priority Changed" && (
                                        <AlertCircle className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type ===
                                        "Assignee Changed" && (
                                        <User className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type === "Ticket Updated" && (
                                        <Edit className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type === "Comment Added" && (
                                        <MessageCircle className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type ===
                                        "Attachment Added" && (
                                        <Paperclip className="w-5 h-5 text-white" />
                                      )}
                                      {![
                                        "Ticket Created",
                                        "Status Changed",
                                        "Priority Changed",
                                        "Assignee Changed",
                                        "Ticket Updated",
                                        "Comment Added",
                                        "Attachment Added",
                                      ].includes(log.action_type) && (
                                        <FileText className="w-5 h-5 text-white" />
                                      )}
                                    </div>
                                    <div>
                                      <div className="flex items-center space-x-2">
                                        <span className="font-semibold text-gray-900 dark:text-white">
                                          {log.action_by}
                                        </span>

                                        <Badge
                                          variant="outline"
                                          className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700"
                                        >
                                          {log.action_type}
                                        </Badge>
                                      </div>
                                      {log.details.assignee && (
                                        <div>
                                          <span className="text-gray-500 dark:text-gray-400 font-medium">
                                            Assignee:
                                          </span>
                                          <span className="ml-2 text-gray-900 dark:text-white">
                                            {log.details.assignee}
                                          </span>
                                        </div>
                                      )}
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {new Date(log.timestamp).toLocaleString(
                                          "en-US",
                                          {
                                            year: "numeric",
                                            month: "long",
                                            day: "numeric",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                          }
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      Active
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Card content */}
                              <div className="p-6">
                                {/* Conditional rendering based on action type */}
                                {log.action_type === "Ticket Created" &&
                                  log.details && (
                                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                                      <h4 className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                                        <FileText className="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" />
                                        Ticket Details
                                      </h4>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                        {log.details.status && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Status:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.details.status}
                                            </span>
                                          </div>
                                        )}
                                        {log.details.priority && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Priority:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.details.priority}
                                            </span>
                                          </div>
                                        )}

                                        {log.details.tracker && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Tracker:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.details.tracker}
                                            </span>
                                          </div>
                                        )}
                                        {log.new_state &&
                                          log.new_state.description && (
                                            <div className="md:col-span-2">
                                              <span className="text-blue-600 dark:text-blue-400 font-medium">
                                                Description:
                                              </span>
                                              <div className="mt-1 text-blue-900 dark:text-blue-100 whitespace-pre-wrap leading-relaxed">
                                                {log.new_state.description}
                                              </div>
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  )}

                                {log.action_type === "Ticket Updated" &&
                                  log.new_state && (
                                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                                      <h4 className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                                        <FileText className="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" />
                                        Current Ticket Information
                                      </h4>
                                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 text-sm">
                                        {/* Status */}
                                        {log.new_state.status && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Status:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.new_state.status}
                                            </span>
                                          </div>
                                        )}

                                        {/* Priority */}
                                        {log.new_state.priority && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Priority:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.new_state.priority}
                                            </span>
                                          </div>
                                        )}

                                        {/* Progress */}
                                        {log.new_state.percent_done && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Progress:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.new_state.percent_done}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                      {/* Description - Spans all 3 columns */}
                                      {log.new_state.description && (
                                        <div className="flex items-center space-x-2 mt-2">
                                          <span className="text-blue-600 dark:text-blue-400 font-medium">
                                            Description:
                                          </span>
                                          <div className="mt-1 text-blue-900 dark:text-blue-100 whitespace-pre-wrap leading-relaxed">
                                            {log.new_state.description}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                {/* For other action types, show generic details */}
                                {log.action_type !== "Ticket Created" &&
                                  log.action_type !== "Ticket Updated" &&
                                  log.details && (
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                        <FileText className="w-4 h-4 mr-2 text-gray-600 dark:text-gray-400" />
                                        Action Details
                                      </h4>
                                      <div className="space-y-2">
                                        {Object.entries(log.details).map(
                                          ([key, value]) => (
                                            <div
                                              key={key}
                                              className="flex justify-between items-center"
                                            >
                                              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 capitalize">
                                                {key.replace(/_/g, " ")}:
                                              </span>
                                              <span className="text-sm text-gray-900 dark:text-white">
                                                {typeof value === "string"
                                                  ? value
                                                  : typeof value === "object"
                                                    ? JSON.stringify(value)
                                                    : String(value)}
                                              </span>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-16">
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center shadow-lg">
                        <Clock className="w-10 h-10 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <div className="w-4 h-4 bg-gray-400 dark:bg-gray-500 rounded-full"></div>
                      </div>
                    </div>
                    <h3 className="mt-6 text-lg font-semibold text-gray-900 dark:text-white">
                      No Activity Found
                    </h3>

                    
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    );
  }
);

EditTicketModal.displayName = "EditTicketModal";

const TicketingDashboard = () => {
  const { selectedPackage } = usePackage();
  const { startDate: fromDate, endDate: toDate } = useDateRange();
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);

  // Ticket overview state
  const [ticketOverview, setTicketOverview] = useState<any>(null);
  const [isLoadingOverview, setIsLoadingOverview] = useState(false);

  // Fetch projects from API
  const projectsApi = useFetchProjects();
  const [projects, setProjects] = useState<string[]>([]);
  const isLoadingProjects =
    projectsApi.type === "mutation" ? projectsApi.loading : false;
  const projectsError =
    projectsApi.type === "mutation" ? projectsApi.result.error : null;
  const hasCalledApi = React.useRef(false);

  // Fetch tickets from API
  const ticketsApi = useGetTickets();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const isLoadingTickets = isInitialLoading || ticketsApi.loading;
  const ticketsError =
    ticketsApi.type === "mutation" ? ticketsApi.result.error : null;

  // Fetch users from API
  const usersApi = useFetchUsers();
  const [users, setUsers] = useState<string[]>([]);
  const isLoadingUsers = usersApi.type === "mutation" ? usersApi.loading : false;
  const usersError = usersApi.type === "query" ? usersApi.result.error : null;

  // Get ticket by ID state
  const [editingTicketId, setEditingTicketId] = useState<string | null>(null);
  const [isLoadingTicketById, setIsLoadingTicketById] = useState(false);

  // Ticket log state
  const [selectedTicketLogs, setSelectedTicketLogs] = useState<TicketLogData[]>(
    []
  );

  // Filter state
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterTakeDown, setFilterTakeDown] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");

  // Ticket overview API
  const ticketOverviewApi = useTicketOverview();

  // Fetch ticket overview data
  useEffect(() => {
    if (ticketOverviewApi.type === "mutation" && ticketOverviewApi.trigger && selectedPackage) {
      setIsLoadingOverview(true);
      const trigger = ticketOverviewApi.trigger;
      trigger({
        package_name: selectedPackage,
        fromDate: fromDate,
        toDate: toDate,
      })
        .then((data: any) => {
          if (data && data.result) {
            setTicketOverview(data.result);
          }
        })
        .catch((error: unknown) => {
          setToastData({
            type: "error",
            title: "Failed to load ticket overview",
            description:
              "Unable to fetch ticket overview data. Please try again later.",
            variant: "destructive",
          });
        })
        .finally(() => {
          setIsLoadingOverview(false);
        });
    }
  }, [selectedPackage, fromDate, toDate]);

  // Trigger the POST request when component mounts (only once)
  // Using empty dependency array and ref to prevent continuous API calls
  useEffect(() => {
    if (
      !hasCalledApi.current &&
      projectsApi.type === "mutation" &&
      projectsApi.trigger
    ) {
      hasCalledApi.current = true;
      const trigger = projectsApi.trigger;
      trigger({ product_name: "App Performance" })
        .then((data: unknown) => {
          // Handle both string and object responses
          let projectNames: string[] = [];
          if (Array.isArray(data)) {
            projectNames = data
              .map((item: any) => {
                if (typeof item === "string") {
                  return item;
                } else if (
                  item &&
                  typeof item === "object" &&
                  item.PackageName
                ) {
                  return item.PackageName;
                }
                return null;
              })
              .filter(Boolean);
          }
          setProjects(projectNames);
        })
        .catch((error: unknown) => {
          setToastData({
            type: "error",
            title: "Failed to load projects",
            description:
              "Unable to fetch project options. Please try again later.",
            variant: "default",
          });
        });
    }
  }, []);

  // Reset filters and pagination when package changes
  useEffect(() => {
    if (selectedPackage) {
      setCurrentPage(1);
      setItemsPerPage(50);
      setFilterStatus("all");
      setFilterTakeDown("all");
      setFilterPriority("all");
      setSearchTerm("");
    }
  }, [selectedPackage]);

  // Trigger tickets API call when component mounts or pagination changes
  useEffect(() => {
    console.log("selectedPackage:", selectedPackage);
    // Temporarily bypass selectedPackage check to test API
    if (true) { // selectedPackage) {
      // When selectedPackage becomes available, set initial loading to false
      setIsInitialLoading(false);
      
      console.log("ticketsApi:", ticketsApi);
      console.log("ticketsApi.type:", ticketsApi.type, "ticketsApi.trigger:", !!(ticketsApi as any).trigger);
      if (ticketsApi.type === "mutation" && (ticketsApi as any).trigger) {
        const trigger = (ticketsApi as any).trigger;
        const payload = {
          page_number: currentPage,
          record_limit: itemsPerPage,
          package_name: selectedPackage,
          fromDate: fromDate,
          toDate: toDate,
          ...(filterStatus !== "all" && { ticket_status: filterStatus }),
          ...(filterTakeDown !== "all" && { status: filterTakeDown }),
          ...(filterPriority !== "all" && { priority: filterPriority })
        };
        console.log("API Call Payload:", payload);
        console.log("About to trigger API call...");
        trigger(payload)
          .then((data: unknown) => {
            // Handle the response data
            console.log("API Response:", data);
            if (data && typeof data === "object") {
              const response = data as TicketsResponse;
              const ticketsData = response.result.data || [];
              console.log("Tickets Data:", ticketsData);
                                 const transformedTickets = ticketsData.map(
                     (apiTicket: TicketData): Ticket => {
                       return {
                         ticket_id: apiTicket.ticket_id,
                         subject: apiTicket.subject,
                         description: apiTicket.description,
                         status: apiTicket.status,
                         priority: apiTicket.priority,
                         type: apiTicket.meta.type,
                         author_name: apiTicket.author_name,
                         assignee: apiTicket.assignee,
                         start_date: apiTicket.start_date,
                         due_date: apiTicket.due_date,
                         mfe_id: apiTicket.meta.mfe_id,
                         created_at: apiTicket.created_at,
                         incident_date: apiTicket.meta.incident_date,
                         // Keep other required fields for compatibility
                         id: apiTicket.ticket_id,
                         title: apiTicket.subject,
                         environment: `${apiTicket.project || ''} / ${apiTicket.category || ''}`,
                         project: apiTicket.project || '',
                         caller: apiTicket.author_name,
                         lastUpdated: new Date().toLocaleString(),
                         createdAt: new Date().toLocaleString(),
                         eta: "NA",
                         diagnosis: "NA",
                         attachments: 0,
                         tags: [],
                         percent_done: "0",
                         comments: [],
                         takeDown: apiTicket.type,
                         activityHistory: [
                           {
                             id: Date.now().toString(),
                             type: "ticket_created" as const,
                             description: `Ticket created by ${apiTicket.author_name}`,
                             author: apiTicket.author_name,
                             timestamp: new Date().toLocaleString(),
                             metadata: {
                               project: apiTicket.project || '',
                               tracker: apiTicket.tracker || '',
                               category: apiTicket.category || '',
                             },
                           },
                         ],
                       };
                     }
                   );
              console.log("Transformed Tickets:", transformedTickets);
              setTickets(transformedTickets);
              setTotalRecords(response.result.total_records);
              setTotalPages(response.result.total_pages);
            }
          })
          .catch((error: unknown) => {
            setToastData({
              type: "error",
              title: "Failed to load tickets",
              description: "Unable to fetch tickets. Please try again later.",
              variant: "default",
            });
          });
      }
    } else {
      // If no selectedPackage is available, keep showing loading until it's available
      setIsInitialLoading(true);
    }
  }, [currentPage, itemsPerPage, selectedPackage, filterStatus, filterTakeDown, filterPriority, fromDate, toDate]);

  // Show error toast if projects fail to load
  useEffect(() => {
    if (projectsError) {
      setToastData({
        type: "error",
        title: "Failed to load projects",
        description: "Unable to fetch project options. Please try again later.",
        variant: "default",
      });
    }
  }, [projectsError]);

  // Show error toast if users fail to load
  useEffect(() => {
    if (usersError) {
      setToastData({
        type: "error",
        title: "Failed to load users",
        description: "Unable to fetch user options. Please try again later.",
        variant: "default",
      });
    }
  }, [usersError]);

  const [toastData, setToastData] = useState<{
    type: ToastType;
    title: string;
    description?: string;
    variant?: "default" | "destructive" | null;
  } | null>(null);

  const [searchTerm, setSearchTerm] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [editingTicket, setEditingTicket] = useState<Ticket | null>(null);

  // Add validation state
  const [editFormErrors, setEditFormErrors] = useState<EditTicketFormErrors>({});

  // Validation functions
  const validateEditForm = (): boolean => {
    const errors: EditTicketFormErrors = {};
    
    if (!editForm.project?.trim()) {
      errors.project = "Package is required";
    }

    if (!editForm.subject?.trim()) {
      errors.subject = "Subject is required";
    }
    if (!editForm.description?.trim()) {
      errors.description = "Description is required";
    }
    if (!editForm.status?.trim()) {
      errors.status = "Status is required";
    }
    if (!editForm.priority?.trim()) {
      errors.priority = "Priority is required";
    }
    if (!editForm.assignee?.trim()) {
      errors.assignee = "Assignee is required";
    }
    
    // Enhanced due date validation for editing
    if (!editForm.due_date?.trim()) {
      errors.due_date = "Due date is required";
    } else {
      const dueDate = new Date(editForm.due_date);
      
      if (isNaN(dueDate.getTime())) {
        errors.due_date = "Please enter a valid date";
      }
      // Remove the past date validation for editing - allow existing past dates to be preserved
    }
    

    if (!editForm.percent_done?.trim()) {
      errors.percent_done = "Percent done is required";
    }
    
    setEditFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Clear validation errors when form changes
  const clearEditFormErrors = () => {
    setEditFormErrors({});
  };

  // Users API will be called manually with package name
  useEffect(() => {
    if (usersApi.type === "mutation" && usersApi.trigger && selectedPackage) {
      const trigger = usersApi.trigger;
      trigger({ package_name: selectedPackage })
        .then((data: any) => {
          console.log("data",data);
          if (data && data.result ) {
            setUsers(data.result);
          }
        })
        .catch((error: unknown) => {
          setToastData({
            type: "error",
            title: "Failed to load users",
            description: "Unable to fetch user options. Please try again later.",
            variant: "default",
          });
        });
    }
  }, [selectedPackage, usersApi.type === "mutation" ? usersApi.trigger : null]);

  // Handle users API errors
  useEffect(() => {
    if (usersApi.type === "mutation" && usersApi.result.error) {
      setToastData({
        type: "error",
        title: "Failed to load users",
        description: "Unable to fetch user options. Please try again later.",
        variant: "default",
      });
    }
  }, [usersApi.type === "mutation" ? usersApi.result.error : null]);

  // Handle projects API errors
  useEffect(() => {
    if (projectsApi.type === "mutation" && projectsApi.result.error) {
      setToastData({
        type: "error",
        title: "Failed to load projects",
        description: "Unable to fetch project options. Please try again later.",
        variant: "default",
      });
    }
  }, [projectsApi.type === "mutation" ? projectsApi.result.error : null]);



  // Edit ticket form state
  const [editForm, setEditForm] = useState<EditTicketForm>({
    project: "",
    subject: "",
    description: "",
    status: "new",
    priority: "Normal",
    assignee: "",
    category: "",
    start_date: new Date().toISOString().slice(0, 16), // Set to today's date and time
    due_date: "",
    percent_done: "0%",
  });

  // New comment state
  const [newComment, setNewComment] = useState("");
  const [activeTab, setActiveTab] = useState("details");

  // Clear toast data after it's shown
  useEffect(() => {
    if (toastData) {
      const timer = setTimeout(() => {
        setToastData(null);
      }, 5000); // Clear after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [toastData]);

  // Show error toast if tickets fail to load
  useEffect(() => {
    if (ticketsError) {
      setToastData({
        type: "error",
        title: "Failed to load tickets",
        description: "Unable to fetch tickets. Please try again later.",
        variant: "default",
      });
    }
  }, [ticketsError]);

  // Summary statistics
  const stats = useMemo(() => {
    // Use ticket overview data if available, otherwise fallback to tickets data
    if (ticketOverview) {
      // Map the new API structure
      const overall = ticketOverview.overall_tickets || {};
      const whitelist = ticketOverview.Whitelist || {};
      const raiseTicket = ticketOverview["Raise Ticket"] || {};
      const closeNoAction = ticketOverview["Close:No Action"] || {};
      const holdCase = ticketOverview["Hold Case"] || {};
      const takeDown = ticketOverview["Take Down"] || {};
      
      return {
        total: overall.total_tickets || 0,
        active: overall.active || 0,
        closed: overall.closed || 0,
        completionRate: overall.closed_per || 0,
        new: 0, // Not provided in overview
        open: 0, // Not provided in overview
        inProgress: overall.in_progress || 0,
        unassigned: 0, // Not provided in overview
        // Additional data from overview
        activeBreakdown: {
          in_progress: overall.in_progress || 0,
          pending: overall.active || 0,
          percentage: {
            in_progress: overall.in_progress_per || 0,
            pending: overall.active_per || 0
          }
        },
        closedBreakdown: {
          completed_on_time: overall.closed || 0,
          completed_late: 0,
          percentage: {
            completed_on_time: overall.closed_per || 0,
            completed_late: 0
          }
        },
        percentage: {
          active: overall.active_per || 0,
          closed: overall.closed_per || 0
        },
        // Category breakdowns
        whitelist: whitelist.total_tickets || 0,
        raiseTicket: raiseTicket.total_tickets || 0,
        closeNoAction: closeNoAction.total_tickets || 0,
        holdCase: holdCase.total_tickets || 0,
        takeDown: takeDown.total_tickets || 0
      };
    }

    // Fallback to tickets data
    const total = tickets.length;
    const active = tickets.filter(
      (t) => !["closed", "cancelled", "resolved"].includes(t.status)
    ).length;
    const closed = tickets.filter((t) => t.status === "closed").length;
    const resolved = tickets.filter((t) => t.status === "resolved").length;
    const completionRate =
      total > 0 ? Math.round(((closed + resolved) / total) * 100) : 0;

    return {
      total: tickets.length,
      active,
      closed: closed + resolved,
      completionRate,
      new: tickets.filter((t) => t.status === "new").length,
      open: tickets.filter((t) => t.status === "open").length,
      inProgress: tickets.filter((t) => t.status === "in_progress").length,
      unassigned: tickets.filter((t) => !t.assignee).length,
    };
  }, [tickets, ticketOverview]);
  //ststus mapping
  const statusMap: Record<string, "raised" | "new" | "closed" | "resolved"> = {
    raised: "raised",
    new: "new",
    close: "closed",
    closed: "closed",
    resolved: "resolved",
  };

  const statusColors = {
    raised:
      "bg-orange-500 text-white hover:bg-orange-600 transition-colors duration-200",
    new: "bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200",
    closed:
      "bg-red-500 text-white hover:bg-red-600 transition-colors duration-200",
    resolved:
      "bg-green-500 text-white hover:bg-green-600 transition-colors duration-200",
  };

  const assigneeColors = {
    assigned:
      "bg-purple-500 text-white hover:bg-purple-600 transition-colors duration-200",
    unassigned:
      "bg-gray-500 text-white hover:bg-gray-600 transition-colors duration-200",
  };

  const normalizeStatus = (status: string): keyof typeof statusColors => {
    const key = status.trim()?.toLowerCase();
    return statusMap[key] || "new"; // default fallback to 'new'
  };

  const getAssigneeColor = (assignee: string | undefined) => {
    return assignee && assignee !== "Unassigned"
      ? assigneeColors.assigned
      : assigneeColors.unassigned;
  };

  const getStatusColor = (status: string) =>
    statusColors[normalizeStatus(status)];
  const getStatusIcon = (status: string) =>
    statusIcons[normalizeStatus(status)];
  // const getPriorityColor = (priority: any) => {
  //   return typeof priority === 'string' && priority in priorityColors ? priorityColors[priority as keyof typeof priorityColors] : priorityColors.medium;
  // };

  // Priority colors
  // const priorityColors = {
  //   Low: "bg-green-500",
  //   medium: "bg-yellow-500",
  //   high: "bg-orange-500",
  //   critical: "bg-red-500",
  //   Urgent: "bg-red-600"
  // };

  // const priorityColors = {
  //   low: <Minus className="w-4 h-4 rounded-full text-white bg-green-700" />,
  //   medium: (
  //     <Clock4 className="w-4 h-4  rounded-full text-white bg-yellow-700" />
  //   ),
  //   high: (
  //     <Triangle className="w-4 h-4  rounded-full  text-white bg-orange-700" />
  //   ),
  //   critical: (
  //     <TriangleAlert className="w-4 h-4  rounded-full text-white bg-red-900" />
  //   ),
  //   urgent: <Zap className="w-4 h-4  rounded-full text-white bg-red-600" />,
  // };

  const statusIcons = {
    raised: <TrendingUp className="w-3 h-3" />,
    new: <File className="w-3 h-3" />,
    closed: <CircleX className="w-3 h-3" />,
    resolved: <CheckCircleIcon size={14} />,
  };

  // Filter tickets
  const filteredTickets = useMemo(() => {
    console.log("Filtering tickets:", tickets?.length, "searchTerm:", searchTerm, "filterStatus:", filterStatus, "filterTakeDown:", filterTakeDown, "filterPriority:", filterPriority);
    console.log("Raw tickets data:", tickets);
    return tickets?.filter((ticket) => {
      const matchesSearch =
        ticket?.title?.toLowerCase().includes(searchTerm?.toLowerCase() || "") ||
        ticket?.id?.toLowerCase().includes(searchTerm?.toLowerCase() || "") ||
        ticket?.caller?.toLowerCase().includes(searchTerm?.toLowerCase() || "") ||
        ticket?.author_name?.toLowerCase().includes(searchTerm?.toLowerCase() || "") ||
        ticket?.description?.toLowerCase().includes(searchTerm?.toLowerCase() || "");

      const matchesStatus =
        filterStatus === "all" || ticket?.status === filterStatus;
      const matchesTakeDown =
        filterTakeDown === "all" || ticket?.takeDown === filterTakeDown;
      const matchesPriority =
        filterPriority === "all" || ticket?.priority === filterPriority;

      console.log("Ticket:", ticket?.id, "matchesSearch:", matchesSearch, "matchesStatus:", matchesStatus, "matchesTakeDown:", matchesTakeDown, "matchesPriority:", matchesPriority, "takeDown:", ticket?.takeDown, "status:", ticket?.status, "priority:", ticket?.priority);
      return matchesSearch && matchesStatus && matchesTakeDown && matchesPriority;
    }) || [];
  }, [tickets, searchTerm, filterStatus, filterTakeDown, filterPriority]);

  // Use API data directly since pagination is handled by the backend
  const paginatedTickets = useMemo(() => {
    return filteredTickets;
  }, [filteredTickets]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleLimitChange = (limit: number) => {
    setItemsPerPage(limit);
    setCurrentPage(1); // Reset to first page when changing limit
  };

  // Update ticket API hook - will be recreated when editingTicketId changes
  const updateTicketMutation = useUpdateTicket(editingTicketId || "");

  // Type guard to check if it's a mutation
  const isUpdateMutation = updateTicketMutation.type === "mutation";

  // Helper function to add activity to ticket
  const addActivityToTicket = (
    ticketId: string,
    activity: {
      type:
        | "status_change"
        | "priority_change"
        | "assignee_change"
        | "comment_added"
        | "ticket_created"
        | "ticket_updated"
        | "attachment_added";
      description: string;
      author: string;
      oldValue?: string;
      newValue?: string;
      metadata?: Record<string, any>;
    }
  ) => {
    const newActivity = {
      id: Date.now().toString(),
      timestamp: new Date().toLocaleString(),
      ...activity,
    };

    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? {
              ...ticket,
              activityHistory: [...(ticket.activityHistory || []), newActivity],
              lastUpdated: new Date().toLocaleString(),
            }
          : ticket
      )
    );
  };

  // Handle status change
  const handleStatusChange = (
    ticketId: string,
    newStatus: Ticket["status"]
  ) => {
    const ticket = tickets.find((t) => t.id === ticketId);
    if (!ticket) return;

    const oldStatus = ticket.status;

    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? {
              ...ticket,
              status: newStatus,
              lastUpdated: new Date().toLocaleString(),
            }
          : ticket
      )
    );

    // Add activity history
    addActivityToTicket(ticketId, {
      type: "status_change",
      description: `Status changed from ${oldStatus} to ${newStatus}`,
      author: "Current User",
      oldValue: oldStatus,
      newValue: newStatus,
    });
  };

  // Handle assignee change
  const handleAssigneeChange = (ticketId: string, newAssignee: string) => {
    const ticket = tickets.find((t) => t.id === ticketId);
    if (!ticket) return;

    const oldAssignee = ticket.assignee || "Unassigned";

    const oldAssigneeDisplay = oldAssignee || "Unassigned";
    const newAssigneeDisplay = newAssignee || "Unassigned";

    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? {
              ...ticket,
              assignee: newAssignee || undefined,
              lastUpdated: new Date().toLocaleString(),
            }
          : ticket
      )
    );

    // Add activity history for reassignment
    addActivityToTicket(ticketId, {
      type: "assignee_change",
      description: `Ticket reassigned from ${oldAssigneeDisplay} to ${newAssigneeDisplay}`,
      author: "Current User",
      oldValue: oldAssigneeDisplay,
      newValue: newAssigneeDisplay,
    });
  };

  // Handle ticket deletion
  const handleDeleteTicket = (ticketId: string) => {
    const ticketToDelete = tickets.find((ticket) => ticket.id === ticketId);
    if (confirm("Are you sure you want to delete this ticket?")) {
      setTickets((prev) => prev.filter((ticket) => ticket.id !== ticketId));
      setToastData({
        type: "success",
        title: "Success",
        description: `Ticket "${ticketToDelete?.title || ticketId}" deleted successfully!`,
        variant: "default",
      });
    }
  };


  // Get ticket log API hook - will automatically fetch when currentTicketId changes
  const [currentTicketId, setCurrentTicketId] = useState<string | null>(null);
  const ticketLogApi = useGetTicketLog(currentTicketId || undefined);


  // Trigger ticket log API when currentTicketId changes
  const isTicketLogLoadingRef = useRef(false);
  useEffect(() => {
    if (!currentTicketId) return;
    if (ticketLogApi.type !== "mutation" || !ticketLogApi.trigger) return;
    if (isTicketLogLoadingRef.current) return; // prevent duplicate triggers

    isTicketLogLoadingRef.current = true;
    console.log("🚀 Triggering ticket log API with ticketId:", currentTicketId);
    const trigger = ticketLogApi.trigger;
    trigger({ ticket_id: currentTicketId })
      .then((data: any) => {
        console.log("✅ Ticket log API response:", data);
        if (data && data.result) {
          setSelectedTicketLogs(data.result || []);
        }
      })
      .catch((error: any) => {
        console.error("❌ Ticket log API error:", error);
        setToastData({
          type: "error",
          title: "Error",
          description: "Failed to fetch ticket activity. Please try again.",
          variant: "default",
        });
        setSelectedTicketLogs([]);
      })
      .finally(() => {
        isTicketLogLoadingRef.current = false;
      });
  }, [currentTicketId]);

  // View ticket details
  const openingViewRef = useRef(false);
  const [isOpeningView, setIsOpeningView] = useState(false);
  const handleViewTicket = useCallback(async (ticket: Ticket) => {
    if (openingViewRef.current || isOpeningView) return; // prevent rapid double open
    openingViewRef.current = true;
    setIsOpeningView(true);

    setSelectedTicket(ticket);
    setActiveTab("activity");

    try {
      if (ticketLogApi.type === "mutation" && ticketLogApi.trigger) {
        const data: any = await ticketLogApi.trigger({ ticket_id: ticket.id });
        if (data && data.result) {
          setSelectedTicketLogs(data.result || []);
        }
      }
      setIsViewModalOpen(true); // open only once, after data is ready
    } catch (error: any) {
      setToastData({
        type: "error",
        title: "Error",
        description: "Failed to fetch ticket activity. Please try again.",
        variant: "default",
      });
      setSelectedTicketLogs([]);
      setIsViewModalOpen(true); // still open to show base info
    } finally {
      setIsOpeningView(false);
      setTimeout(() => { openingViewRef.current = false; }, 300);
    }
  }, [ticketLogApi]);

  // Close modal handler
  const handleCloseModal = useCallback(() => {
    setIsViewModalOpen(false);
    setSelectedTicket(null);
    setActiveTab("details");
    setNewComment(""); // Clear comment form when closing
    setSelectedTicketLogs([]); // Clear ticket logs when closing
    setCurrentTicketId(null); // Reset current ticket ID when closing modal
  }, []);

  // Helper function to map API priority values to form values
  const mapPriorityToFormValue = useCallback(
    (priority: string): "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate" => {
      const normalizedPriority = priority.toLowerCase();
      if (normalizedPriority === "high") return "High";
      if (normalizedPriority === "medium") return "Medium";
      if (normalizedPriority === "low") return "Low";
      if (normalizedPriority === "normal") return "Normal";
      if (normalizedPriority === "urgent") return "Urgent";
      if (normalizedPriority === "immediate") return "Immediate";
      return "Normal"; // default fallback
    },
    []
  );

  // Handle edit ticket
  const handleEditTicket = useCallback(
    (ticket: Ticket) => {
      setEditingTicket(ticket);
      setEditingTicketId(ticket.id);
      setCurrentTicketId(ticket.id); // Add this line to fetch ticket logs

      const initialFormData: EditTicketForm = {
        project: "Identity Scan", // Default value

        subject: ticket.title,
        description: ticket.description || "",
        status: ticket.status,
        priority: ticket.priority
          ? mapPriorityToFormValue(ticket.priority)
          : "Normal",
        assignee: ticket.assignee || "",
        category: "Advanced IDScan", // Default value
        start_date: new Date().toISOString().slice(0, 16), // Set to today's date and time
        due_date: ticket.due_date ? ticket.due_date.split('T')[0] : "", // Format for date input

        percent_done: ticket.percent_done === "NA" ? "0%" : (ticket.percent_done || "0%"),
      };

      setEditForm(initialFormData);

      setIsEditModalOpen(true);
    },
    [users, mapPriorityToFormValue]
  );

  // Effect to fetch ticket data when editingTicketId changes
  useEffect(() => {

    if (editingTicketId) {
      setIsLoadingTicketById(true);
      const fetchTicketData = async () => {
        try {
          const token = sessionStorage.getItem("IDToken") || "";
          const url = `${TICKET_API_BASE}/tickets/${editingTicketId}`;

          const response = await fetch(url, {
            method: "GET",
            headers: {
              Authorization: token,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const ticketData = await response.json();


          // Extract the actual ticket data from the response
          const actualTicketData = ticketData.result || ticketData;



          // Use the fetched data to populate the edit form
          setEditForm({
            project: actualTicketData.project || "Identity Scan",
    
            subject: actualTicketData.subject || "",
            description: actualTicketData.description || "",
            status: actualTicketData.status || "new",
            priority: actualTicketData.priority
              ? (mapPriorityToFormValue(actualTicketData.priority) as "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate")
              : "Normal",
            assignee: actualTicketData.assignee || "",
            category: actualTicketData.category || "Advanced IDScan",
            start_date: actualTicketData.start_date
              ? actualTicketData.start_date.substring(0, 16)
              : "", // Format for datetime-local input
            due_date: actualTicketData.due_date
              ? actualTicketData.due_date.split('T')[0]
              : "", // Format for date input (YYYY-MM-DD)
    
            percent_done: actualTicketData.percent_done
              ? actualTicketData.percent_done.includes("%")
                ? actualTicketData.percent_done
                : actualTicketData.percent_done === "NA"
                  ? "0%"
                  : `${actualTicketData.percent_done}%`
              : "0%",
          });
        } catch (error) {
          setToastData({
            type: "error",
            title: "Error",
            description: "Failed to fetch ticket details. Please try again.",
            variant: "default",
          });

          // Fallback to using the ticket data we have
          if (editingTicket) {
            setEditForm({
              project: "Identity Scan",
              subject: editingTicket.title,
              description: editingTicket.description || "",
              status: editingTicket.status,
              priority: editingTicket.priority
                ? (mapPriorityToFormValue(editingTicket.priority) as "Low" | "Normal" | "Medium" | "High" | "Urgent" | "Immediate")
                : "Normal",
              assignee: editingTicket.assignee || "",
              category: "Advanced IDScan",
              start_date: new Date().toISOString().slice(0, 16), // Set to today's date and time
              due_date: editingTicket.due_date
                ? editingTicket.due_date.split('T')[0]
                : "", // Format for date input (YYYY-MM-DD)

              percent_done: editingTicket.percent_done === "NA" ? "0%" : (editingTicket.percent_done || "0%"),
            });
          }
        } finally {
          setIsLoadingTicketById(false);
        }
      };

      fetchTicketData();
    }
  }, [editingTicketId, users, editingTicket, mapPriorityToFormValue]);

  // Handle update ticket
  const handleUpdateTicket = useCallback(async () => {
    if (!editingTicket) {
      return;
    }

    // Use the new validation function
    if (!validateEditForm()) {
      return; // Validation failed, errors are already set
    }


    const oldTicket = tickets.find((t) => t.id === editingTicket.id);
    if (!oldTicket) return;

    // Create the payload structure exactly as required for update
    const ticketPayload = {
      project: editForm.project,
      subject: editForm.subject,
      description: editForm.description,
      status: editForm.status,
      priority: editForm.priority,
      assignee: editForm.assignee,
      category: editForm.category,
      start_date: editForm.start_date,
      due_date: editForm.due_date,
      percent_done: editForm.percent_done,
      sub_projects_of: "App Performance",
    };

    try {
      // Check if it's a mutation and has trigger
      if (isUpdateMutation && updateTicketMutation.trigger) {
        const response = await updateTicketMutation.trigger(ticketPayload);



        // Update the local ticket data
        setTickets((prev) => {
          const updatedTickets = prev.map((ticket) =>
            ticket.id === editingTicket.id
              ? {
                  ...ticket,
                  title: editForm.subject,
                  description: editForm.description,
                  status: editForm.status,
                  priority: editForm.priority,
                  author_name: "Current User",
                  caller: "Current User",
                  assignee: editForm.assignee,
                  lastUpdated: new Date().toLocaleString(),
                }
              : ticket
          );
          return updatedTickets;
        });

        // Add activity history for ticket update
        addActivityToTicket(editingTicket.id, {
          type: "ticket_updated",
          description: "Ticket details updated",
          author: "Current User",
          metadata: {
            changes: {
              title:
                oldTicket.title !== editForm.subject
                  ? { old: oldTicket.title, new: editForm.subject }
                  : undefined,
              description:
                oldTicket.description !== editForm.description
                  ? {
                      old: oldTicket.description.substring(0, 50),
                      new: editForm.description.substring(0, 50),
                    }
                  : undefined,
              status:
                oldTicket.status !== editForm.status
                  ? { old: oldTicket.status, new: editForm.status }
                  : undefined,
              priority:
                oldTicket.priority !== editForm.priority
                  ? { old: oldTicket.priority, new: editForm.priority }
                  : undefined,
              assignee:
                oldTicket.assignee !== editForm.assignee
                  ? {
                      old: oldTicket.assignee || "Unassigned",
                      new: editForm.assignee || "Unassigned",
                    }
                  : undefined,
            },
          },
        });

        setEditForm({
          project: "Identity Scan",
          subject: "",
          description: "",
          status: "new",
          priority: "Normal",
          assignee: users.length > 0 ? users[0] : "",
          category: "Advanced IDScan",
          start_date: new Date().toISOString().slice(0, 16), // Set to today's date and time
          due_date: "",
          percent_done: "0%",
        });
        setEditingTicket(null);
        setIsEditModalOpen(false);
        setCurrentTicketId(null); // Clear currentTicketId when closing edit modal

        setToastData({
          type: "success",
          title: "Success",
          description:
            response.message ||
            `Ticket "${editForm.subject}" updated successfully!`,
          variant: "default",
        });
      } else {
        throw new Error("Update mutation not available");
      }
    } catch (error: any) {
      setToastData({
        type: "error",
        title: error?.response?.data?.title || "Error",
        description:
          error?.response?.data?.message ||
          error?.message ||
          "Failed to update ticket. Please try again.",
        variant: "destructive",
      });
    }
  }, [editingTicket, editForm, users]);

  const ViewTicketModal = React.memo(() => {
    // Don't render if no ticket is selected
    if (!selectedTicket) return null;

    return (
      <Dialog open={isViewModalOpen} onOpenChange={handleCloseModal}>
        <DialogContent
          className="max-w-4xl h-[85vh] flex flex-col overflow-hidden"
          aria-describedby="view-ticket-description"
        >
          <div id="view-ticket-description" className="sr-only">
            View ticket activity history
          </div>

          <div className="w-full flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-y-auto p-6 min-h-0">
              <div className="space-y-8">
                {/* Ticket Overview Section - Single Unified Card */}
                <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50 rounded-2xl border border-gray-200/60 dark:border-gray-600/60 shadow-lg overflow-hidden">
                  {/* Header */}
                  <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                          <FileText className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-white">
                            {(() => {
                              // Get the title from the first object in selectedTicketLogs
                              if (
                                selectedTicketLogs &&
                                selectedTicketLogs.length > 0
                              ) {
                                const firstLog = selectedTicketLogs[0];
                                if (
                                  firstLog.details &&
                                  firstLog.details.title
                                ) {
                                  return firstLog.details.title;
                                }
                              }
                              return "Ticket Overview";
                            })()}
                          </h3>
                        </div>
                      </div>
                      <Badge className="bg-white/20 text-white border-white/30 text-xs px-3 py-1">
                        #{selectedTicket.id}
                      </Badge>
                    </div>
                  </div>

                  {/* Content - Single Unified Layout */}
                  <div className="p-6">
                    <div className="space-y-4">
                      {/* First Row - Three Columns */}
                      <div className="flex justify-between items-center">
                        {/* Column 1 - Created By */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Created By:
                          </div>
                          <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                const creator =
                                  firstLog.action_by ||
                                  selectedTicket.author_name ||
                                  "Unknown";
                                return creator;
                              }
                              return selectedTicket.author_name || "Unknown";
                            })()}
                          </div>
                        </div>

                        {/* Column 2 - Current Assignee */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Assignee:
                          </div>
                          <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                const assignee =
                                  firstLog.new_state?.assignee ||
                                  firstLog.details?.assignee ||
                                  selectedTicket.assignee ||
                                  "Unassigned";
                                return assignee;
                              }
                              return selectedTicket.assignee || "Unassigned";
                            })()}
                          </div>
                        </div>

                        {/* Column 3 - Status */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Status:
                          </div>
                          <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );
                              const firstLog = sortedLogs[0];
                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                                return (
                                  firstLog.new_state?.status ||
                                  selectedTicket.status ||
                                  "new"
                                );
                              }
                              return typeof selectedTicket.status === "string"
                                ? normalizeStatus(
                                    selectedTicket.status
                                  ).replace("_", " ")
                                : "new";
                            })()}
                          </div>
                        </div>
                      </div>

                      {/* second Row - Additional Fields */}
                      <div className="flex justify-between items-center">
                        {/* Column 1 - Percent Done */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Percent Done:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                             

                              const sortedLogs = [...selectedTicketLogs].sort(
                                (a, b) =>
                                  new Date(a.timestamp).getTime() -
                                  new Date(b.timestamp).getTime()
                              );

                              const firstLog = sortedLogs[0];

                              if (
                                firstLog &&
                                firstLog.action_type === "Ticket Created"
                              ) {
                               
                                // Get percent done from the ticket creation log
                                let percentDone =
                                  firstLog.new_state?.percent_done;

                                // If not found in new_state, check if it's in details
                                if (
                                  !percentDone &&
                                  firstLog.details?.percent_done
                                ) {
                                  if (
                                    typeof firstLog.details.percent_done ===
                                      "object" &&
                                    firstLog.details.percent_done.new
                                  ) {
                                    percentDone =
                                      firstLog.details.percent_done.new;
                                  } else if (
                                    typeof firstLog.details.percent_done ===
                                    "string"
                                  ) {
                                    percentDone = firstLog.details.percent_done;
                                  }
                                }

                                const result = percentDone || "0%";
                                return result;
                              }
                              
                              return "0%";
                            })()}
                          </div>
                        </div>

                        {/* Column 2 - Start Date */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Start Date:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              if (!selectedTicket.start_date) return "Not set";
                              try {
                                const date = new Date(
                                  selectedTicket.start_date
                                );
                                return date.toISOString().split("T")[0]; // Returns YYYY-MM-DD format
                              } catch {
                                return selectedTicket.start_date;
                              }
                            })()}
                          </div>
                        </div>

                        {/* Column 3 - Due Date */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Due Date:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {(() => {
                              if (!selectedTicket.due_date) return "Not set";
                              try {
                                const date = new Date(selectedTicket.due_date);
                                return date.toISOString().split("T")[0]; // Returns YYYY-MM-DD format
                              } catch {
                                return selectedTicket.due_date;
                              }
                            })()}
                          </div>
                        </div>
                      </div>

                      {/* Third Row - Additional Fields */}
                      <div className="flex justify-between items-center">
                        {/* Column 1 - Percent Done */}
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Priority:
                          </div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-lg px-3 py-2">
                            {selectedTicket.priority || "Medium"}
                          </div>
                        </div>


                      </div>

                      {/* Fourth Row - Description */}
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          Description:
                        </div>
                        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 flex-1">
                          {(() => {
                            const sortedLogs = [...selectedTicketLogs].sort(
                              (a, b) =>
                                new Date(a.timestamp).getTime() -
                                new Date(b.timestamp).getTime()
                            );
                            const firstLog = sortedLogs[0];
                            const description =
                              firstLog &&
                              firstLog.action_type === "Ticket Created"
                                ? firstLog.new_state?.description
                                : selectedTicket.description;

                            return description || "No description available";
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* History Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                        Activity History
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Track all changes and updates to this ticket
                      </p>
                    </div>
                  </div>
                </div>

                {ticketLogApi.type === "mutation" &&
                ticketLogApi.loading &&
                currentTicketId ? (
                  <div className="flex flex-col items-center justify-center py-16">
                    <div className="relative">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg animate-pulse">
                        <Loader2 className="w-8 h-8 text-white animate-spin" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                    </div>
                    <p className="mt-4 text-gray-600 dark:text-gray-400 font-medium">
                      Loading activity history...
                    </p>
                  </div>
                ) : selectedTicketLogs.length > 0 ? (
                  <div className="relative">
                    {/* Timeline container */}
                    <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-indigo-200 to-purple-200 dark:from-blue-700 dark:via-indigo-700 dark:to-purple-700"></div>

                    <div className="space-y-8">
                      {selectedTicketLogs
                        .filter((log) => log.action_type !== "Ticket Created")
                        .sort(
                          (a, b) =>
                            new Date(b.timestamp).getTime() -
                            new Date(a.timestamp).getTime()
                        )
                        .map((log, index) => (
                          <div key={log._id} className="relative">
                            {/* Timeline dot */}
                            <div className="absolute left-6 top-4 w-4 h-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full border-4 border-white dark:border-gray-800 shadow-lg z-10"></div>

                            {/* Activity card */}
                            <div className="ml-12 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
                              {/* Card header */}
                              <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                                      {log.action_type === "Ticket Created" && (
                                        <FileText className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type === "Status Changed" && (
                                        <RefreshCw className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type ===
                                        "Priority Changed" && (
                                        <AlertCircle className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type ===
                                        "Assignee Changed" && (
                                        <User className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type === "Ticket Updated" && (
                                        <Edit className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type === "Comment Added" && (
                                        <MessageCircle className="w-5 h-5 text-white" />
                                      )}
                                      {log.action_type ===
                                        "Attachment Added" && (
                                        <Paperclip className="w-5 h-5 text-white" />
                                      )}
                                      {![
                                        "Ticket Created",
                                        "Status Changed",
                                        "Priority Changed",
                                        "Assignee Changed",
                                        "Ticket Updated",
                                        "Comment Added",
                                        "Attachment Added",
                                      ].includes(log.action_type) && (
                                        <FileText className="w-5 h-5 text-white" />
                                      )}
                                    </div>
                                    <div>
                                      <div className="flex items-center space-x-2">
                                        <span className="font-semibold text-gray-900 dark:text-white">
                                          {log.action_by}
                                        </span>

                                        <Badge
                                          variant="outline"
                                          className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700"
                                        >
                                          {log.action_type}
                                        </Badge>
                                      </div>
                                      {log.details.assignee && (
                                        <div>
                                          <span className="text-gray-500 dark:text-gray-400 font-medium">
                                            Assignee:
                                          </span>
                                          <span className="ml-2 text-gray-900 dark:text-white">
                                            {log.details.assignee}
                                          </span>
                                        </div>
                                      )}
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {new Date(log.timestamp).toLocaleString(
                                          "en-US",
                                          {
                                            year: "numeric",
                                            month: "long",
                                            day: "numeric",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                          }
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      Active
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Card content */}
                              <div className="p-6">
                                {/* Conditional rendering based on action type */}
                                {log.action_type === "Ticket Created" &&
                                  log.details && (
                                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                                      <h4 className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                                        <FileText className="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" />
                                        Ticket Details
                                      </h4>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                        {log.details.status && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Status:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.details.status}
                                            </span>
                                          </div>
                                        )}
                                        {log.details.priority && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Priority:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.details.priority}
                                            </span>
                                          </div>
                                        )}

                                        {log.details.tracker && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Tracker:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.details.tracker}
                                            </span>
                                          </div>
                                        )}
                                        {log.new_state &&
                                          log.new_state.description && (
                                            <div className="md:col-span-2">
                                              <span className="text-blue-600 dark:text-blue-400 font-medium">
                                                Description:
                                              </span>
                                              <div className="mt-1 text-blue-900 dark:text-blue-100 whitespace-pre-wrap leading-relaxed">
                                                {log.new_state.description}
                                              </div>
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  )}

                                {log.action_type === "Ticket Updated" &&
                                  log.new_state && (
                                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                                      <h4 className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                                        <FileText className="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" />
                                        Current Ticket Information
                                      </h4>
                                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 text-sm">
                                        {/* Status */}
                                        {log.new_state.status && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Status:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.new_state.status}
                                            </span>
                                          </div>
                                        )}

                                        {/* Priority */}
                                        {log.new_state.priority && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Priority:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.new_state.priority}
                                            </span>
                                          </div>
                                        )}

                                        {/* Progress */}
                                        {log.new_state.percent_done && (
                                          <div>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                              Progress:
                                            </span>
                                            <span className="ml-2 text-blue-900 dark:text-blue-100">
                                              {log.new_state.percent_done}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                      {/* Description - Spans all 3 columns */}
                                      {log.new_state.description && (
                                        <div className="flex items-center space-x-2 mt-2">
                                          <span className="text-blue-600 dark:text-blue-400 font-medium">
                                            Description:
                                          </span>
                                          <div className="mt-1 text-blue-900 dark:text-blue-100 whitespace-pre-wrap leading-relaxed">
                                            {log.new_state.description}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                {/* For other action types, show generic details */}
                                {log.action_type !== "Ticket Created" &&
                                  log.action_type !== "Ticket Updated" &&
                                  log.details && (
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                        <FileText className="w-4 h-4 mr-2 text-gray-600 dark:text-gray-400" />
                                        Action Details
                                      </h4>
                                      <div className="space-y-2">
                                        {Object.entries(log.details).map(
                                          ([key, value]) => (
                                            <div
                                              key={key}
                                              className="flex justify-between items-center"
                                            >
                                              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 capitalize">
                                                {key.replace(/_/g, " ")}:
                                              </span>
                                              <span className="text-sm text-gray-900 dark:text-white">
                                                {typeof value === "string"
                                                  ? value
                                                  : typeof value === "object"
                                                    ? JSON.stringify(value)
                                                    : String(value)}
                                              </span>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-16">
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center shadow-lg">
                        <Clock className="w-10 h-10 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <div className="w-4 h-4 bg-gray-400 dark:bg-gray-500 rounded-full"></div>
                      </div>
                    </div>
                    <h3 className="mt-6 text-lg font-semibold text-gray-900 dark:text-white">
                      No Activity Found
                    </h3>

                   
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  });
  ViewTicketModal.displayName = "ViewTicketModal";

  // Define columns for the table
  const columns: Column<Ticket>[] = [
    {
      title: "Ticket ID",
      key: "ticket_id",
      width: 120,
      render: (ticket: Ticket) => <EllipsisTooltip content={ticket.ticket_id || ''} />,
    },
    {
      title: "Subject",
      key: "subject",
      width: 250,
      render: (ticket: Ticket) => <EllipsisTooltip content={ticket.subject || ''} />,
    },
    {
      title: "Description",
      key: "description",
      width: 200,
      render: (ticket: Ticket) => <EllipsisTooltip content={ticket.description || ''} />,
    },
    {
      title: "Status",
      key: "status",
      width: 140,
      render: (ticket: Ticket) => (
        <Badge className={getStatusColor(ticket.status)}>
          {getStatusIcon(ticket.status)}
          <span className="ml-1 capitalize">
            {ticket.status}
          </span>
        </Badge>
      ),
    },
    {
      title: "Priority",
      key: "priority",
      width: 120,
      render: (ticket: Ticket) => (
        <div className="flex items-center gap-2">
          <span className="text-sm capitalize">{ticket.priority}</span>
        </div>
      ),
    },
    {
      title: "Type",
      key: "meta.type",
      width: 150,
      render: (ticket: Ticket) => (
        <div className="flex items-center">
          <span className="text-sm capitalize">{ticket.type || ''}</span>
        </div>
      ),
    },
    {
      title: "Author",
      key: "author_name",
      width: 150,
      render: (ticket: Ticket) => (
        <EllipsisTooltip content={ticket.author_name} />
      ),
    },
    {
      title: "Assignee",
      key: "assignee",
      width: 150,
      render: (ticket: Ticket) => (
        <div className="flex items-center">
          {ticket.assignee ? (
            <>
              <EllipsisTooltip content={ticket.assignee} />
            </>
          ) : (
            <span className="text-sm text-gray-500">Unassigned</span>
          )}
        </div>
      ),
    },
    {
      title: "Created At",
      key: "created_at",
      width: 150,
      render: (ticket: Ticket) => (
        <EllipsisTooltip content={ticket.created_at || ''} />
      ),
    },
    {
      title: "MFE ID",
      key: "meta.mfe_id",
      width: 120,
      render: (ticket: Ticket) => (
        <EllipsisTooltip content={ticket.mfe_id || 'null'} />
      ),
    },
    {
      title: "Incident Date",
      key: "meta.incident_date",
      width: 150,
      render: (ticket: Ticket) => (
        <EllipsisTooltip content={ticket.incident_date || 'N/A'} />
      ),
    },
  ];

  return (
    <div className=" space-y-2 bg-gray-50 dark:bg-gray-900 p-2">
      {toastData && (
        <ToastContent
          type={toastData.type}
          title={toastData.title}
          description={toastData.description}
          variant={toastData.variant}
        />
      )}
      {/* Header */}
      {/* <div className="flex items-center justify-between mb-3">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Support
        </h1>
      </div> */}

      {/* Summary Section */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4 lg:gap-2">
        {/* First Card */}
        <div className="col-span-1 lg:col-span-2 min-w-[250px]">
          <Card className="p-4 bg-gradient-to-br from-orange-50 to-emerald-50 dark:from-orange-900/20 dark:to-emerald-900/20 border-orange-200 dark:border-orange-800">
            <div className="flex items-center justify-between mb-3">
              <div className="flex-1">
                <CircularProgress
                  segments={[
                    {
                      label: "Active",
                      value: isLoadingOverview ? 0 : ticketOverview?.overall_tickets?.active || 0,
                      color: "#3b82f6", // blue-500
                      maxValue: isLoadingOverview ? 0 : ticketOverview?.overall_tickets?.total_tickets || 1
                    },
                    {
                      label: "Closed",
                      value: isLoadingOverview ? 0 : ticketOverview?.overall_tickets?.closed || 0,
                      color: "#22c55e", // green-500
                      maxValue: isLoadingOverview ? 0 : ticketOverview?.overall_tickets?.total_tickets || 1
                    }
                  ]}
                  centerCount={isLoadingOverview ? 0 : ticketOverview?.overall_tickets?.total_tickets || 0}
                  centerLabel="Total Tickets"
                  size={120}
                  strokeWidth={8}
                  className="mx-auto"
                />
              </div>
              <div className="text-right ml-4">
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2.5 h-2.5 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="text-blue-500 text-small-font dark:text-gray-300">
                      {isLoadingOverview ? (
                        <Loader2 className="w-3 h-3 animate-spin" />
                      ) : (
                        `${ticketOverview?.overall_tickets?.active || 0} Active (${ticketOverview?.overall_tickets?.active_per || 0}%)`
                      )}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2.5 h-2.5 bg-red-500 rounded-full shadow-sm"></div>
                    <span className="text-red-500 text-small-font dark:text-gray-300">
                      {isLoadingOverview ? (
                        <Loader2 className="w-3 h-3 animate-spin" />
                      ) : (
                        `${ticketOverview?.overall_tickets?.in_progress || 0} In Progress (${ticketOverview?.overall_tickets?.in_progress_per || 0}%)`
                      )}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2.5 h-2.5 bg-green-500 rounded-full shadow-sm"></div>
                    <span className="text-green-700 text-small-font dark:text-gray-300">
                      {isLoadingOverview ? (
                        <Loader2 className="w-3 h-3 animate-spin" />
                      ) : (
                        `${ticketOverview?.overall_tickets?.closed || 0} Closed (${ticketOverview?.overall_tickets?.closed_per || 0}%)`
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
        
        {/* Second Card - Take Down */}
        <div className="min-w-[70px]">
          <Card className="p-4 bg-gradient-to-br from-blue-50 to-emerald-50 dark:from-blue-900/20 dark:to-emerald-900/20 border-blue-200 dark:border-blue-800">
            <div className="text-center">
              <h3 className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-1">Take Down</h3>
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-3">
                {isLoadingOverview ? (
                  <Loader2 className="w-8 h-8 animate-spin mx-auto" />
                ) : (
                  ticketOverview?.["Take Down"]?.total_tickets || 0
                )}
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="text-blue-600 dark:text-blue-400">Active:</span>
                  </div>
                  <span className="font-semibold text-blue-900 dark:text-blue-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Take Down"]?.active || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-red-500 rounded-full shadow-sm"></div>
                    <span className="text-red-600 dark:text-red-400">In Progress:</span>
                  </div>
                  <span className="font-semibold text-red-900 dark:text-red-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Take Down"]?.in_progress || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-green-500 rounded-full shadow-sm"></div>
                    <span className="text-green-600 dark:text-green-400">Closed:</span>
                  </div>
                  <span className="font-semibold text-green-900 dark:text-green-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Take Down"]?.closed || 0}`
                    )}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
        
        {/* Third Card - Whitelist */}
        <div className="min-w-[70px]">
          <Card className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
            <div className="text-center">
              <h3 className="text-sm font-semibold text-green-700 dark:text-green-300 mb-1">Whitelist</h3>
              <div className="text-2xl font-bold text-green-900 dark:text-green-100 mb-3">
                {isLoadingOverview ? (
                  <Loader2 className="w-8 h-8 animate-spin mx-auto" />
                ) : (
                  ticketOverview?.Whitelist?.total_tickets || 0
                )}
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="text-blue-600 dark:text-blue-400">Active:</span>
                  </div>
                  <span className="font-semibold text-blue-900 dark:text-blue-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.Whitelist?.active || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-red-500 rounded-full shadow-sm"></div>
                    <span className="text-red-600 dark:text-red-400">In Progress:</span>
                  </div>
                  <span className="font-semibold text-red-900 dark:text-red-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.Whitelist?.in_progress || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-green-500 rounded-full shadow-sm"></div>
                    <span className="text-green-600 dark:text-green-400">Closed:</span>
                  </div>
                  <span className="font-semibold text-green-900 dark:text-green-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.Whitelist?.closed || 0}`
                    )}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
        
        {/* Fourth Card - Raise Ticket */}
        <div className="min-w-[70px]">
          <Card className="p-4 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 border-purple-200 dark:border-purple-800">
            <div className="text-center">
              <h3 className="text-sm font-semibold text-purple-700 dark:text-purple-300 mb-1">Raise Ticket</h3>
              <div className="text-2xl font-bold text-purple-900 dark:text-purple-100 mb-3">
                {isLoadingOverview ? (
                  <Loader2 className="w-8 h-8 animate-spin mx-auto" />
                ) : (
                  ticketOverview?.["Raise Ticket"]?.total_tickets || 0
                )}
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="text-blue-600 dark:text-blue-400">Active:</span>
                  </div>
                  <span className="font-semibold text-blue-900 dark:text-blue-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Raise Ticket"]?.active || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-red-500 rounded-full shadow-sm"></div>
                    <span className="text-red-600 dark:text-red-400">In Progress:</span>
                  </div>
                  <span className="font-semibold text-red-900 dark:text-red-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Raise Ticket"]?.in_progress || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-green-500 rounded-full shadow-sm"></div>
                    <span className="text-green-600 dark:text-green-400">Closed:</span>
                  </div>
                  <span className="font-semibold text-green-900 dark:text-green-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Raise Ticket"]?.closed || 0}`
                    )}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
        
        {/* Fifth Card - Close:No Action */}
        <div className="min-w-[70px]">
          <Card className="p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-yellow-200 dark:border-yellow-800">
            <div className="text-center">
              <h3 className="text-sm font-semibold text-yellow-700 dark:text-yellow-300 mb-1">Close:No Action</h3>
              <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100 mb-3">
                {isLoadingOverview ? (
                  <Loader2 className="w-8 h-8 animate-spin mx-auto" />
                ) : (
                  ticketOverview?.["Close:No Action"]?.total_tickets || 0
                )}
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="text-blue-600 dark:text-blue-400">Active:</span>
                  </div>
                  <span className="font-semibold text-blue-900 dark:text-blue-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Close:No Action"]?.active || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-red-500 rounded-full shadow-sm"></div>
                    <span className="text-red-600 dark:text-red-400">In Progress:</span>
                  </div>
                  <span className="font-semibold text-red-900 dark:text-red-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Close:No Action"]?.in_progress || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-green-500 rounded-full shadow-sm"></div>
                    <span className="text-green-600 dark:text-green-400">Closed:</span>
                  </div>
                  <span className="font-semibold text-green-900 dark:text-green-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Close:No Action"]?.closed || 0}`
                    )}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
        
        {/* Sixth Card - Hold Case */}
        <div className="min-w-[70px]">
          <Card className="p-4 bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 border-teal-200 dark:border-teal-800">
            <div className="text-center">
              <h3 className="text-sm font-semibold text-teal-700 dark:text-teal-300 mb-1">Hold Case</h3>
              <div className="text-2xl font-bold text-teal-900 dark:text-teal-100 mb-3">
                {isLoadingOverview ? (
                  <Loader2 className="w-8 h-8 animate-spin mx-auto" />
                ) : (
                  ticketOverview?.["Hold Case"]?.total_tickets || 0
                )}
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="text-blue-600 dark:text-blue-400">Active:</span>
                  </div>
                  <span className="font-semibold text-blue-900 dark:text-blue-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Hold Case"]?.active || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-red-500 rounded-full shadow-sm"></div>
                    <span className="text-red-600 dark:text-red-400">In Progress:</span>
                  </div>
                  <span className="font-semibold text-red-900 dark:text-red-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Hold Case"]?.in_progress || 0}`
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2.5 h-2.5 bg-green-500 rounded-full shadow-sm"></div>
                    <span className="text-green-600 dark:text-green-400">Closed:</span>
                  </div>
                  <span className="font-semibold text-green-900 dark:text-green-100">
                    {isLoadingOverview ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      `${ticketOverview?.["Hold Case"]?.closed || 0}`
                    )}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Professional Ticketing Table */}
      <TicketingTable
        data={paginatedTickets}
        isLoading={isLoadingTickets}
        onView={handleViewTicket}
        onEdit={handleEditTicket}
        //onDelete={handleDeleteTicket}
        onSearch={setSearchTerm}
        onStatusFilter={setFilterStatus}
        onTakeDownFilter={setFilterTakeDown}
        onPriorityFilter={setFilterPriority}
        searchTerm={searchTerm}
        statusFilter={filterStatus}
        takeDownFilter={filterTakeDown}
        priorityFilter={filterPriority}
        emptyStateMessage="No tickets found!"
        // Pagination props
        isPaginated={true}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        columns={columns}
        //isCheckbox={true}
      />


      <EditTicketModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setCurrentTicketId(null);
          setEditingTicket(null);
          setEditingTicketId(null);
        }}
        editForm={editForm}
        setEditForm={setEditForm}
        onUpdateTicket={handleUpdateTicket}
        editingTicket={editingTicket}
        projects={projects}
        isLoadingProjects={isLoadingProjects}
        users={users}
        isLoadingUsers={isLoadingUsers}
        isLoadingTicketData={isLoadingTicketById}
        getAssigneeColor={getAssigneeColor}
        getStatusColor={getStatusColor}
        normalizeStatus={normalizeStatus}
        selectedTicketLogs={selectedTicketLogs}
        statusColors={statusColors}
        statusIcons={statusIcons}
        statusMap={statusMap}
        assigneeColors={assigneeColors}
        ticketLogApi={ticketLogApi}
        currentTicketId={currentTicketId}
        errors={editFormErrors}
        clearErrors={clearEditFormErrors}
      />
      <ViewTicketModal />
    </div>
  );
};

export default TicketingDashboard;