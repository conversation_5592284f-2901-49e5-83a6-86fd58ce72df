import React, { useEffect, useState, useCallback } from "react";
import { useApiCall } from "../../../app/(main)/webfraud/queries/api_base";
import { useMutation } from "@tanstack/react-query";
import { APICall } from "@/services/api_service";
import {
  <PERSON>alog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Loader2, Minus, Plus, X, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePackage } from "@/components/mf/PackageContext";
import { MFDateRangePicker } from "@/components/mf/MFDateRangePicker";
import { DateRangeProvider, useDateRange } from "@/components/mf/DateRangeContext";

interface DeliveryOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: "schedule" | "download";
  onSubmit: (payload: any) => void;
  defaultData?: any;
  mode?: any;
  frequency?: string;
  onFrequencyChange?: (value: string) => void;
  category?: string;
}

interface MailingList {
  id: string;
  mailing_list_name: string;
}

const DeliveryOptionsModalContent = ({
  isOpen,
  onClose,
  type,
  onSubmit,
  defaultData,
  mode,
  frequency,
  onFrequencyChange,
  category,
}: DeliveryOptionsModalProps) => {
  console.log("category",category)
  const { startDate, endDate } = useDateRange();
  const [sendViaEmail, setSendViaEmail] = useState(false);
  const [saveToCloud, setSaveToCloud] = useState(true);
  const [selectedCloudProvider, setSelectedCloudProvider] = useState<"AWS">("AWS");
  const [websiteAppEmails, setWebsiteAppEmails] = useState<string[]>([""]);
  const [socialMediaEmails, setSocialMediaEmails] = useState<string[]>([""]);
  const [selectedWebsiteAppMailingLists, setSelectedWebsiteAppMailingLists] = useState<string[]>([]);
  const [selectedSocialMediaMailingLists, setSelectedSocialMediaMailingLists] = useState<string[]>([]);
  const [isWebsiteAppDropdownOpen, setIsWebsiteAppDropdownOpen] = useState(false);
  const [isSocialMediaDropdownOpen, setIsSocialMediaDropdownOpen] = useState(false);
  const [isSocialMediaCopiedFromWebsiteApp, setIsSocialMediaCopiedFromWebsiteApp] = useState(false);
  const [cloudConfigs, setCloudConfigs] = useState({
    AWS: { accessKey: "", secretKey: "", bucketName: "" },
    GCP: { accessKey: "", secretKey: "", bucketName: "" },
    Azure: { accessKey: "", secretKey: "", bucketName: "" },
  });

  const [occurrenceError, setOccurrenceError] = useState<string>("");
  const [cloudError, setCloudError] = useState<string>("");
  const [emailError, setEmailError] = useState<string>("");
  const [mailinglist, setMailinglist] = useState<MailingList[]>([]);
  const { selectedPackage } = usePackage();
  const [occurrenceOptions, setOccurrenceOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  // Helper function to check if category includes a specific value
  const categoryIncludes = (value: string): boolean => {
    
    // If no category provided, show both sections by default
    if (!category || category === undefined || category === null) {
      return true;
    }
    
    // Convert to string and trim whitespace
    const categoryStr = String(category).trim().toLowerCase();
    const valueStr = value.trim().toLowerCase();
    
    // Check exact match or includes
    const result = categoryStr === valueStr || categoryStr.includes(valueStr);
    
    
    return result;
  };

  // api call for occurrence and schedule
  const { result: occurrenceApi, loading: occurrenceLoading } = useApiCall({
    url: type === "schedule" 
      ? process.env.NEXT_PUBLIC_BI_TEST + `360_dashboard/reporting_tool/reporting_tool_screen/scheduler/occurance`
      : process.env.NEXT_PUBLIC_BI_TEST + `360_dashboard/reporting_tool/reporting_tool_screen/download/occurance`,
    method: "POST",
    params: {
      package_name: selectedPackage,
    },
    onSuccess: (data: any) => {
      if (Array.isArray(data)) {
        setOccurrenceOptions(data);
      }
    },
  });

  useEffect(() => {
    if (isOpen) {
      occurrenceApi.mutate();
    }
  }, [isOpen]);

  const mailingListApi = useMutation({
    mutationFn: async (params: any) => {
      const response = await fetch(process.env.NEXT_PUBLIC_BI_TEST + `360_dashboard/reporting_tool/reporting_tool_screen/list_all_mailing_lists`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': localStorage.getItem("IDToken") || "",
        },
        body: JSON.stringify(params),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    },
    onSuccess: (data: any) => {
      if (Array.isArray(data.mailing_lists)) {
        setMailinglist(data.mailing_lists);
      }
    },
    onError: (error: any) => {
      console.error("Error fetching mailing list:", error);
    },
  });

  const mailingListLoading = mailingListApi.isPending;

  const refreshMailingList = useCallback(() => {
    if (mailingListApi) {
      mailingListApi.mutate({
        page_number: "1",
        record_list: "200",
        package_name: selectedPackage,
        occurance: frequency,
      });
    }
  }, [mailingListApi, selectedPackage, frequency]);

  useEffect(() => {
    if (isOpen && frequency) {
      refreshMailingList();
    }
  }, [isOpen, frequency, category]);

  useEffect(() => {
    
    if (defaultData) {
      const { email, aws, gcp, azure } = defaultData;

      if (email?.status) {
        setSendViaEmail(true);
        
        // Wait for mailing list data to be loaded
        if (mailinglist.length > 0) {
          
          // Handle website_app mailing lists with fallback to generic mail_id_list
          const websiteAppMailingListNames = email.website_app_mail_id_list || email.mail_id_list || [];
         
          
          const websiteAppLists = websiteAppMailingListNames.map((listName: string) => {
            const found = mailinglist.find((ml) => ml.mailing_list_name === listName);
            return found?.id;
          }).filter(Boolean) || [];
          
          // Handle social_media mailing lists with fallback to generic mail_id_list
          const socialMediaMailingListNames = email.social_media_mail_id_list || email.mail_id_list || [];
          
          const socialMediaLists = socialMediaMailingListNames.map((listName: string) => {
            const found = mailinglist.find((ml) => ml.mailing_list_name === listName);
            return found?.id;
          }).filter(Boolean) || [];
         
          setSelectedWebsiteAppMailingLists(websiteAppLists);
          setSelectedSocialMediaMailingLists(socialMediaLists);
        } 
        // Handle email addresses with fallback to generic 'to' field
        const websiteAppEmailAddresses = email.website_app_to || email.to || [""];
        const socialMediaEmailAddresses = email.social_media_to || email.to || [""];
        
        setWebsiteAppEmails(websiteAppEmailAddresses);
        setSocialMediaEmails(socialMediaEmailAddresses);
      } 

      if (aws?.status) {
        setSelectedCloudProvider("AWS");
        setSaveToCloud(true);
        setCloudConfigs((prev) => ({
          ...prev,
          AWS: {
            accessKey: aws.aws_access_key_id || "",
            secretKey: aws.aws_secret_access_key || "",
            bucketName: aws.bucket_name || "",
          },
        }));
      }
      // Commented out GCP and Azure support for now - can be uncommented later
      // } else if (gcp?.status) {
      //   setSelectedCloudProvider("GCP");
      //   setSaveToCloud(true);
      //   setCloudConfigs((prev) => ({
      //     ...prev,
      //     GCP: {
      //       accessKey: gcp.gcp_access_key_id || "",
      //       secretKey: gcp.gcp_secret_access_key || "",
      //       bucketName: gcp.storage_name || "",
      //     },
      //   }));
      // } else if (azure?.status) {
      //   setSelectedCloudProvider("Azure");
      //   setSaveToCloud(true);
      //   setCloudConfigs((prev) => ({
      //     ...prev,
      //     Azure: {
      //       accessKey: azure.azure_access_key_id || "",
      //       secretKey: azure.azure_secret_access_key || "",
      //       bucketName: azure.container_name || "",
      //     },
      //   }));
      // }
    }
  }, [defaultData, mailinglist]);

  // Add a separate useEffect to handle mailing list selection when mailing list data is loaded
  useEffect(() => {
    if (defaultData?.email?.status && mailinglist.length > 0) {
      // Handle website_app mailing lists with fallback to generic mail_id_list
      const websiteAppMailingListNames = defaultData.email.website_app_mail_id_list || defaultData.email.mail_id_list || [];
    
      
      const websiteAppLists = websiteAppMailingListNames.map((listName: string) => {
        const found = mailinglist.find((ml) => ml.mailing_list_name === listName);
        return found?.id;
      }).filter(Boolean) || [];
      
      // Handle social_media mailing lists with fallback to generic mail_id_list
      const socialMediaMailingListNames = defaultData.email.social_media_mail_id_list || defaultData.email.mail_id_list || [];
      
      const socialMediaLists = socialMediaMailingListNames.map((listName: string) => {
        const found = mailinglist.find((ml) => ml.mailing_list_name === listName);
        return found?.id;
      }).filter(Boolean) || [];
     
      setSelectedWebsiteAppMailingLists(websiteAppLists);
      setSelectedSocialMediaMailingLists(socialMediaLists);
    }
  }, [mailinglist, defaultData]);

  useEffect(() => {
    if (isOpen) {
      if (type === "download") {
        setSaveToCloud(false);
      } else {
        setSaveToCloud(false);
      }
    }
  }, [isOpen, type]);

  // Show date picker when type is download and frequency is custom range
  useEffect(() => {
    if (type === "download" && frequency === "Custom Range") {
      setShowDatePicker(true);
    } else {
      setShowDatePicker(false);
    }
  }, [type, frequency]);

  // Show date picker when type is download and frequency is custom range
  useEffect(() => {
    if (type === "download" && frequency === "Custom Range") {
      setShowDatePicker(true);
    } else {
      setShowDatePicker(false);
    }
  }, [type, frequency]);

  const handleWebsiteAppMailingListToggle = useCallback((listId: string) => {
    if (mode === "view") return;
    
    
    setSelectedWebsiteAppMailingLists((prev: string[]) => {
      const isCurrentlySelected = prev.includes(listId);
      
      let newSelection;
      if (isCurrentlySelected) {
        newSelection = prev.filter((id: string) => id !== listId);
      } else {
        newSelection = [...prev, listId];
      }
    
      return newSelection;
    });
  }, [mode, selectedWebsiteAppMailingLists]);

  const handleSocialMediaMailingListToggle = useCallback((listId: string) => {
    if (mode === "view") return;
  
    
    setSelectedSocialMediaMailingLists((prev: string[]) => {
     
      const isCurrentlySelected = prev.includes(listId);
     
      
      let newSelection;
      if (isCurrentlySelected) {
        newSelection = prev.filter((id: string) => id !== listId);
      
      } else {
        newSelection = [...prev, listId];
        
      }
     
      return newSelection;
    });
  }, [mode, selectedSocialMediaMailingLists]);

  const removeWebsiteAppMailingList = (listId: string) => {
    if (mode === "view") return;
    setSelectedWebsiteAppMailingLists((prev: string[]) => prev.filter((id: string) => id !== listId));
  };

  const removeSocialMediaMailingList = (listId: string) => {
    if (mode === "view") return;
    setSelectedSocialMediaMailingLists((prev: string[]) => prev.filter((id: string) => id !== listId));
  };

  // Function to copy website_app data to social_media section
  const copyWebsiteAppToSocialMedia = () => {
    setSelectedSocialMediaMailingLists([...selectedWebsiteAppMailingLists]);
    setSocialMediaEmails([...websiteAppEmails]);
    setIsSocialMediaCopiedFromWebsiteApp(true);
  };

  // Function to clear social_media section (uncopy)
  const clearSocialMediaData = () => {
    setSelectedSocialMediaMailingLists([]);
    setSocialMediaEmails([""]);
    setIsSocialMediaCopiedFromWebsiteApp(false);
  };

  // Check if website_app section has data
  const hasWebsiteAppData = () => {
    return selectedWebsiteAppMailingLists.length > 0 || websiteAppEmails.some(email => email.trim() !== "");
  };

  

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if ((isWebsiteAppDropdownOpen || isSocialMediaDropdownOpen) && !target.closest(".relative")) {
        setIsWebsiteAppDropdownOpen(false);
        setIsSocialMediaDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  
  }, [isWebsiteAppDropdownOpen, isSocialMediaDropdownOpen]);

  const getSelectedWebsiteAppMailingListNames = () => {
    const names = selectedWebsiteAppMailingLists
      .map((id: string) => {
        const found = mailinglist.find((ml) => ml.id === id);
        return found?.mailing_list_name;
      })
      .filter(Boolean);
  
    return names;
  };

  const getSelectedSocialMediaMailingListNames = () => {
    const names = selectedSocialMediaMailingLists
      .map((id: string) => {
        const found = mailinglist.find((ml) => ml.id === id);
        return found?.mailing_list_name;
      })
      .filter(Boolean);
   
    return names;
  };

  const handleConfirm = () => {
      // Clear any previous errors - using exact same pattern as deliveryoptionsmodal
      setOccurrenceError("");
      setCloudError("");
      setEmailError("");
   
      // Validate occurrence - using exact same validation logic
      if(!frequency || frequency.trim() === ""){
        setOccurrenceError("Please select occurrence");
        return;
      }
   
      // Validate cloud configuration if save to cloud is enabled - using exact same validation logic
      if(saveToCloud) {
        const config = cloudConfigs[selectedCloudProvider];
        if(!config.accessKey || !config.secretKey || !config.bucketName) {
          setCloudError("Please fill all cloud configuration fields");
          return;
        }
      }
   
      // Validate email configuration if send via email is enabled - using exact same validation logic
      if(sendViaEmail) {
        let hasValidEmailConfig = false;
       
        // Check if any mailing lists are selected or any email addresses are entered
        const hasAnyMailingLists = selectedWebsiteAppMailingLists.length > 0 || selectedSocialMediaMailingLists.length > 0;
        const hasAnyEmails = websiteAppEmails.some(email => email.trim() !== "") || socialMediaEmails.some(email => email.trim() !== "");
       
        if (hasAnyMailingLists || hasAnyEmails) {
          hasValidEmailConfig = true;
        }
       
        if (!hasValidEmailConfig) {
          setEmailError("Please select mailing lists or enter email addresses");
          return;
        }
      }
   
    const cloudOptions = ["AWS"].reduce((acc, provider) => {
      const key = provider.toLowerCase();
      const isSelected = selectedCloudProvider === provider && saveToCloud;
      const config = cloudConfigs[provider as "AWS"];

      acc[key] = {
        status: isSelected,
        [`${key}_access_key_id`]: isSelected ? config.accessKey : "",
        [`${key}_secret_access_key`]: isSelected ? config.secretKey : "",
        bucket_name: isSelected ? config.bucketName : "",
        
      };

      return acc;
    }, {} as Record<string, any>);
    
    // Commented out GCP and Azure cloud options - can be uncommented later
    // const cloudOptions = ["AWS", "GCP", "Azure"].reduce((acc, provider) => {
    //   const key = provider.toLowerCase();
    //   const isSelected = selectedCloudProvider === provider && saveToCloud;
    //   const config = cloudConfigs[provider as "AWS" | "GCP" | "Azure"];

    //   acc[key] = {
    //     status: isSelected,
    //     [`${key}_access_key_id`]: isSelected ? config.accessKey : "",
    //     [`${key}_secret_access_key`]: isSelected ? config.secretKey : "",
    //     [provider === "AWS"
    //       ? "bucket_name"
    //       : provider === "GCP"
    //       ? "storage_name"
    //       : "container_name"]: isSelected ? config.bucketName : "",
        
    //   };

    //   return acc;
    // }, {} as Record<string, any>);

    const websiteAppMailingListNames = getSelectedWebsiteAppMailingListNames();
    const socialMediaMailingListNames = getSelectedSocialMediaMailingListNames();
    const filteredWebsiteAppEmails = websiteAppEmails.filter((email: string) => email.trim() !== "");
    const filteredSocialMediaEmails = socialMediaEmails.filter((email: string) => email.trim() !== "");
    

    const hasWebsiteAppMailingLists = websiteAppMailingListNames.length > 0;
    const hasWebsiteAppEmails = filteredWebsiteAppEmails.length > 0;
    const hasSocialMediaMailingLists = socialMediaMailingListNames.length > 0;
    const hasSocialMediaEmails = filteredSocialMediaEmails.length > 0;

    // Create separate delivery configurations for website_app and social_media
    const deliveryPayload: any = {};

      // Add website_app delivery options if website_app category is selected
    if (categoryIncludes("website_app")) {
      const websiteAppEmailSection = {
        status: sendViaEmail,
        mail_type: hasWebsiteAppMailingLists ? "group" : "individual",
        mail_id_list: websiteAppMailingListNames,
        email_group: websiteAppMailingListNames,
        to: filteredWebsiteAppEmails,
        subject: "",
        name: ""
      };

      deliveryPayload.website_app = {
        ...cloudOptions,
        email: websiteAppEmailSection
      };
    }

    // Add social_media delivery options if social_media category is selected
    if (categoryIncludes("social_media")) {
      const socialMediaEmailSection = {
        status: sendViaEmail,
        mail_type: hasSocialMediaMailingLists ? "group" : "individual",
        mail_id_list: socialMediaMailingListNames,
        email_group: socialMediaMailingListNames,
        to: filteredSocialMediaEmails,
        subject: "",
        name: ""
      };

      deliveryPayload.social_media = {
        ...cloudOptions,
        email: socialMediaEmailSection
      };
    }

    if(startDate && endDate){
      deliveryPayload.dateRange = {
        startDate: startDate,
        endDate: endDate,
      };
    }
    console.log("deliveryPayload", deliveryPayload);
    onSubmit(deliveryPayload);
    onClose();
  };

  return (
   
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] h-[600px] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold dark:text-white">
            {type === "schedule" ? "Schedule Report" : "Download Report"}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto py-4 w-full">
          {/* Occurrence Dropdown */}
          <div className="space-y-2 mb-4 w-[95%] mx-auto">
            <Label className="dark:text-white">Occurrence</Label>
            <Select
              value={frequency}
              // onValueChange={onFrequencyChange}
              onValueChange={(value) => {
                onFrequencyChange?.(value);
                // Clear occurrence error when user selects a valid occurrence
                if (value && value !== "") {
                  setOccurrenceError("");
                }
              }}
              disabled={mode === "view" || mode === "edit"  }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Occurrence" />
              </SelectTrigger>
              <SelectContent>
                {occurrenceLoading ? (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  occurrenceOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            {occurrenceError && (
              <div className="text-red-500 text-sm mt-1">
                {occurrenceError}
              </div>
            )}
            {showDatePicker && (
              <div className="mt-2">
                <MFDateRangePicker />
              </div>
            )}
          </div>

          <div className="border-b">
            <div className="flex">
              <div className="flex items-center gap-2 min-w-[150px] px-4 py-2">
                <Checkbox
                  id="save-cloud"
                  checked={saveToCloud}
                  onCheckedChange={(checked) => {
                    setSaveToCloud(checked as boolean);
                    // Clear cloud error when checkbox is toggled - using exact same pattern
                    if (checked) {
                      setCloudError("");
                    }
                  }}
                  disabled={mode === "view"}
                />
                <Label htmlFor="save-cloud" className="text-sm font-medium dark:text-white">
                  Save to Cloud
                </Label>
              </div>
              <div className="flex items-center gap-2 min-w-[150px] px-4 py-2">
                <Checkbox
                  id="send-email"
                  checked={sendViaEmail}
                  onCheckedChange={(checked) => {
                    setSendViaEmail(checked as boolean);
                    // Clear email error when checkbox is toggled - using exact same pattern
                    if (checked) {
                      setEmailError("");
                    }
                  }}
                  disabled={mode === "view"}
                />
                <Label htmlFor="send-email" className="text-sm font-medium dark:text-white">
                  Send via Email
                </Label>
              </div>
            </div>
          </div>

          
          {sendViaEmail && (
            <div className="">
              {/* Website App Section */}

              <div className="space-y-6 rounded-lg border p-2 mt-4">
              {categoryIncludes("website_app") && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-primary">Website App</h3>
                  
                  <div className="space-y-2">
                    {/* Selected website_app mailing lists display */}
                    {selectedWebsiteAppMailingLists.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {selectedWebsiteAppMailingLists.map((listId) => {
                          const mailingListItem = mailinglist.find(ml => ml.id === listId);
                          if (!mailingListItem) return null;
                          
                          return (
                            <div key={listId} className="flex items-center gap-1 bg-primary/10 text-primary px-2 py-1 rounded-md text-sm">
                              <span>{mailingListItem.mailing_list_name}</span>
                              {mode !== "view" && (
                                <X 
                                  className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleWebsiteAppMailingListToggle(listId);
                                  }}
                                />
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}

                    {/* Custom dropdown for selecting website_app mailing lists */}
                    <div className="relative">
                      <div 
                        className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer ${mode === "view" ? "opacity-50 cursor-not-allowed" : ""}`}
                        onClick={() => mode !== "view" && setIsWebsiteAppDropdownOpen(!isWebsiteAppDropdownOpen)}
                      >
                        <span className="flex-1">
                          {selectedWebsiteAppMailingLists.length === 0 
                            ? "Choose Website App Mailing Lists" 
                            : `${selectedWebsiteAppMailingLists.length} mailing list${selectedWebsiteAppMailingLists.length > 1 ? 's' : ''} selected`
                          }
                        </span>
                        <ChevronDown className={`h-4 w-4 transition-transform ${isWebsiteAppDropdownOpen ? 'rotate-180' : ''}`} />
                      </div>
                      
                      {isWebsiteAppDropdownOpen && (
                        <div className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-md max-h-60 overflow-auto">
                          {mailingListLoading ? (
                            <div className="flex justify-center items-center p-4">
                              <Loader2 className="h-6 w-6 animate-spin text-primary" />
                            </div>
                          ) : mailinglist.length === 0 ? (
                            <div className="p-4 text-center text-muted-foreground">
                              No mailing lists available
                            </div>
                          ) : (
                            mailinglist.map((list) => {
                              const isSelected = selectedWebsiteAppMailingLists.includes(list.id);
                              
                              return (
                                <div 
                                  key={list.id}
                                  className="flex items-center gap-2 p-2 hover:bg-accent"
                                >
                                  <Checkbox 
                                    id={`website_app-mailing-list-checkbox-${list.id}`}
                                    checked={isSelected}
                                    onCheckedChange={(checked) => {
                                      if (mode !== "view") {
                                        handleWebsiteAppMailingListToggle(list.id);
                                      }
                                    }}
                                    disabled={mode === "view"}
                                  />
                                  <Label 
                                    htmlFor={`website_app-mailing-list-checkbox-${list.id}`}
                                    className="flex-1 cursor-pointer"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      if (mode !== "view") {
                                        handleWebsiteAppMailingListToggle(list.id);
                                        setEmailError("");
                                      }
                                    }}
                                  >
                                    {list.mailing_list_name}
                                  </Label>
                                </div>
                              );
                            })
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    {websiteAppEmails.map((email, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          type="email"
                            placeholder="Enter Website App Email"
                          value={email}
                          onChange={(e) =>{ 
                            setWebsiteAppEmails((prev) => {
                              const copy = [...prev];
                              copy[index] = e.target.value;
                              return copy;
                            })
                            if (e.target.value.trim() !== "") {
                              setEmailError("");
                            }
                          }}
                          className="flex-1"
                          disabled={mode === "view"}
                        />
                        {index === websiteAppEmails.length - 1 ? (
                          <Plus
                            onClick={() => setWebsiteAppEmails([...websiteAppEmails, ""])}
                            className="mt-2 h-4 w-4 cursor-pointer text-primary"
                          />
                        ) : (
                          <Minus
                            onClick={() =>
                              setWebsiteAppEmails(websiteAppEmails.filter((_, i) => i !== index))
                            }
                            className="mt-2 h-4 w-4 cursor-pointer text-primary"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              </div>

              
              {/* Social Media Section */}
              {categoryIncludes("social_media") && (
                <div className="space-y-6 rounded-lg border p-2 mt-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-primary">Social Media</h3>
                    
                    {/* Copy from Website App functionality */}
                    {categoryIncludes("website_app") && hasWebsiteAppData() && (
                      <div className="flex gap-2">
                        {!isSocialMediaCopiedFromWebsiteApp ? (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={copyWebsiteAppToSocialMedia}
                            disabled={mode === "view"}
                            className="text-xs"
                          >
                            📋 Copy from Website App
                          </Button>
                        ) : (
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-green-600 font-medium">✅ Copied from Website App</span>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={clearSocialMediaData}
                              disabled={mode === "view"}
                              className="text-xs"
                            >
                              🗑️ Clear
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    {/* Selected social_media mailing lists display */}
                    {selectedSocialMediaMailingLists.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {selectedSocialMediaMailingLists.map((listId) => {
                          const mailingListItem = mailinglist.find(ml => ml.id === listId);
                          if (!mailingListItem) return null;
                          
                          return (
                            <div key={listId} className="flex items-center gap-1 bg-primary/10 text-primary px-2 py-1 rounded-md text-sm">
                              <span>{mailingListItem.mailing_list_name}</span>
                              {mode !== "view" && (
                                <X 
                                  className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSocialMediaMailingListToggle(listId);
                                  }}
                                />
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}

                    {/* Custom dropdown for selecting social_media mailing lists */}
                    <div className="relative">
                      <div 
                        className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer ${mode === "view" ? "opacity-50 cursor-not-allowed" : ""}`}
                        onClick={() => mode !== "view" && setIsSocialMediaDropdownOpen(!isSocialMediaDropdownOpen)}
                      >
                        <span className="flex-1">
                          {selectedSocialMediaMailingLists.length === 0 
                            ? "Choose Social Media Mailing Lists" 
                            : `${selectedSocialMediaMailingLists.length} mailing list${selectedSocialMediaMailingLists.length > 1 ? 's' : ''} selected`
                          }
                        </span>
                        <ChevronDown className={`h-4 w-4 transition-transform ${isSocialMediaDropdownOpen ? 'rotate-180' : ''}`} />
                      </div>
                      
                      {isSocialMediaDropdownOpen && (
                        <div className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-md max-h-60 overflow-auto">
                          {mailingListLoading ? (
                            <div className="flex justify-center items-center p-4">
                              <Loader2 className="h-6 w-6 animate-spin text-primary" />
                            </div>
                          ) : mailinglist.length === 0 ? (
                            <div className="p-4 text-center text-muted-foreground">
                              No mailing lists available
                            </div>
                          ) : (
                            mailinglist.map((list) => {
                              const isSelected = selectedSocialMediaMailingLists.includes(list.id);
                              
                              return (
                                <div 
                                  key={list.id}
                                  className="flex items-center gap-2 p-2 hover:bg-accent"
                                >
                                  <Checkbox 
                                    id={`social_media-mailing-list-checkbox-${list.id}`}
                                    checked={isSelected}
                                    onCheckedChange={(checked) => {
                                      if (mode !== "view") {
                                        handleSocialMediaMailingListToggle(list.id);
                                      }
                                    }}
                                    disabled={mode === "view"}
                                  />
                                  <Label 
                                    htmlFor={`social_media-mailing-list-checkbox-${list.id}`}
                                    className="flex-1 cursor-pointer"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      if (mode !== "view") {
                                        handleSocialMediaMailingListToggle(list.id);
                                      }
                                    }}
                                  >
                                    {list.mailing_list_name}
                                  </Label>
                                </div>
                              );
                            })
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    
                    {socialMediaEmails.map((email, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          type="email"
                          placeholder="Enter Social Media Email"
                          value={email}
                          onChange={(e) =>
                              setSocialMediaEmails((prev) => {
                              const copy = [...prev];
                              copy[index] = e.target.value;
                              return copy;
                            })
                          }
                          className="flex-1"
                          disabled={mode === "view"}
                        />
                        {index === socialMediaEmails.length - 1 ? (
                          <Plus
                            onClick={() => setSocialMediaEmails([...socialMediaEmails, ""])}
                            className="mt-2 h-4 w-4 cursor-pointer text-primary"
                          />
                        ) : (
                          <Minus
                            onClick={() =>
                              setSocialMediaEmails(socialMediaEmails.filter((_, i) => i !== index))
                            }
                            className="mt-2 h-4 w-4 cursor-pointer text-primary"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                </div>
              )}
             {emailError && (
                <div className="text-red-500 text-sm mt-2 ml-4">
                  {emailError}
                </div>
              )}
 
            </div>
          )}

          {saveToCloud && (
            <div className="space-y-4 rounded-lg border p-4 mt-4">
              <div className="border-b">
                <div className="flex">
                  {["AWS"].map((provider) => (
                    <button
                      disabled={mode === "view"}
                      key={provider}
                      onClick={() =>
                        setSelectedCloudProvider(
                          provider as "AWS"
                        )
                      }
                      className={`relative min-w-[100px] border-b-2 px-4 py-2 text-sm font-medium transition-colors hover:text-primary ${
                        selectedCloudProvider === provider
                          ? "border-primary text-primary"
                          : "border-transparent text-muted-foreground"
                      }`}
                    >
                      {provider}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Commented out GCP and Azure provider buttons - can be uncommented later */}
              {/* <div className="border-b">
                <div className="flex">
                  {["AWS", "GCP", "Azure"].map((provider) => (
                    <button
                      disabled={mode === "view"}
                      key={provider}
                      onClick={() =>
                        setSelectedCloudProvider(
                          provider as "AWS" | "GCP" | "Azure"
                        )
                      }
                      className={`relative min-w-[100px] border-b-2 px-4 py-2 text-sm font-medium transition-colors hover:text-primary ${
                        selectedCloudProvider === provider
                          ? "border-primary text-primary"
                          : "border-transparent text-muted-foreground"
                      }`}
                    >
                      {provider}
                    </button>
                  ))}
                </div>
              </div> */}
              {(() => {
                const config = cloudConfigs[selectedCloudProvider];
                return (
                  <>
                    <div className="space-y-2">
                      <Label className="dark:text-white">Secret Key</Label>
                      <Input
                        value={config.secretKey}
                        onChange={(e) => {
                          setCloudConfigs((prev) => ({
                            ...prev,
                            [selectedCloudProvider]: {
                              ...prev[selectedCloudProvider],
                              secretKey: e.target.value,
                            },
                          }));
                          // Clear cloud error when user types - using exact same pattern
                          if (e.target.value.trim() !== "") {
                            setCloudError("");
                          }
                        }}
 
                        
                        placeholder={`Enter ${selectedCloudProvider} Secret Key`}
                        disabled={mode === "view"}
                        className="dark:text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="dark:text-white">Access Key</Label>
                      <Input
                        value={config.accessKey}
                        onChange={(e) => {
                          setCloudConfigs((prev) => ({
                            ...prev,
                            [selectedCloudProvider]: {
                              ...prev[selectedCloudProvider],
                              accessKey: e.target.value,
                            },
                          }));
                          // Clear cloud error when user types - using exact same pattern
                          if (e.target.value.trim() !== "") {
                            setCloudError("");
                          }
                        }}
 
                        placeholder={`Enter ${selectedCloudProvider} Access Key`}
                        disabled={mode === "view"}
                        className="dark:text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="dark:text-white">
                        Bucket Name
                      </Label>
                      {/* Commented out conditional labels for GCP and Azure - can be uncommented later */}
                      {/* <Label className="dark:text-white">
                        {selectedCloudProvider === "AWS"
                          ? "Bucket Name"
                          : selectedCloudProvider === "GCP"
                            ? "Storage Name"
                            : "Container Name"}
                      </Label> */}
                      <Input
                        value={config.bucketName}
                        onChange={(e) => {
                          setCloudConfigs((prev) => ({
                            ...prev,
                            [selectedCloudProvider]: {
                              ...prev[selectedCloudProvider],
                              bucketName: e.target.value,
                            },
                          }));
                          // Clear cloud error when user types - using exact same pattern
                          if (e.target.value.trim() !== "") {
                            setCloudError("");
                          }
                        }}
 
                        placeholder="Enter Bucket Name"
                        disabled={mode === "view"}
                        className="dark:text-white"
                                              />
                      </div>
                      
                      {/* Commented out conditional placeholders for GCP and Azure - can be uncommented later */}
                      {/* 
                      placeholder={`Enter ${
                        selectedCloudProvider === "AWS"
                          ? "Bucket Name"
                          : selectedCloudProvider === "GCP"
                            ? "Storage Name"
                            : "Container Name"
                      }`}
                      */}
                    </>
                  );
                })()}
                   {cloudError && (
                <div className="text-red-500 text-sm mt-2">
                  {cloudError}
                </div>
              )}
 
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button
            onClick={onClose}
            className="text-white bg-primary"
            disabled={mode === "view"}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="text-white bg-primary"
            disabled={mode === "view"}
          >
            {type === "schedule" ? "Schedule" : "Download"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const DeliveryOptionsModal = (props: DeliveryOptionsModalProps) => {
  return (
    <DateRangeProvider>
      <DeliveryOptionsModalContent {...props} />
    </DateRangeProvider>
  );
};

export default DeliveryOptionsModal;
