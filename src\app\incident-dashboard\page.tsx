"use client";

import React, { useState } from 'react';
import { useQuery } from 'react-query';
import axios from 'axios';
import { usePackage } from "@/components/mf/PackageContext";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export default function IncidentDashboard() {
  const { selectedPackage } = usePackage();
  const [incidentVolumeData, setIncidentVolumeData] = useState<any>(null);
  const [selectedBarData, setSelectedBarData] = useState<any>(null);
  const [showTable, setShowTable] = useState(false);

  // Fetch incident volume data
  const { data: incidentVolumeResponse } = useQuery({
    queryKey: ['incidentVolume', selectedPackage],
    queryFn: async () => {
      const response = await axios.post(
        'https://api.example.com/incident-volume',
        {
          package_name: selectedPackage,
          fromDate: '2025-01-01',
          toDate: '2025-06-01'
        }
      );
      setIncidentVolumeData(response.data);
      return response.data;
    },
  });

  const incidentVolumeChartConfig = {
    data: {
      labels: ['15 Days Period'],
      datasets: [
        {
          label: 'Incidents',
          data: [incidentVolumeData?.data?.total_incidents || 0],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: true,
          text: 'Incident by Volume',
          font: {
            size: 16,
            weight: 'bold',
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Number of Incidents',
          },
        },
        x: {
          title: {
            display: true,
            text: 'Time Period',
          },
        },
      },
    },
  };

  const handleBarClick = (data: any) => {
    if (data && data.length > 0) {
      const clickedData = data[0];
      setSelectedBarData(clickedData);
      setShowTable(true);
    }
  };

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Total Incidents</h3>
          <p className="text-2xl font-bold">{incidentVolumeData?.data?.total_incidents || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Under Review</h3>
          <p className="text-2xl font-bold">{incidentVolumeData?.data?.under_review || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">In Progress</h3>
          <p className="text-2xl font-bold">{incidentVolumeData?.data?.in_progress || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Closed</h3>
          <p className="text-2xl font-bold">{incidentVolumeData?.data?.closed || 0}</p>
        </div>
      </div>
      
      {/* Responsive Chart Container */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="w-full h-[400px] relative">
          <Bar 
            data={incidentVolumeChartConfig.data} 
            options={incidentVolumeChartConfig.options}
            onClick={handleBarClick}
          />
        </div>
      </div>
    </div>
  );
} 