"use client";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Loader2, X } from "lucide-react";
import React, { useEffect, useState } from "react";

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItem: { id: string; label: string } | null;
  onSave: (data: { field: string; value: string[] }) => void;
  filterData: any;
  filterloading: boolean;
  savedFilters?: Array<{ field: string; value: string[] }>;
  mode?: any;
}

const FilterModal = ({
  isOpen,
  onClose,
  selectedItem,
  onSave,
  filterData,
  filterloading,
  savedFilters = [],
  mode,
}: FilterModalProps) => {
  // Find any existing saved filters for this specific dimension
  const existingSavedFilters = React.useMemo(() => {
    if (!selectedItem) return [];
    const savedFilterForItem = savedFilters.find(
      (filter) => filter.field === selectedItem.id
    );
    return savedFilterForItem ? savedFilterForItem.value : [];
  }, [selectedItem, savedFilters]);

  // Initialize selectedItems state based on existing saved filters when modal opens or selectedItem changes
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSelected, setShowSelected] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Reset state when the modal opens with new selected item
  useEffect(() => {
    if (isOpen && selectedItem) {
      // Convert saved filter values to item IDs
      const savedItemIds = items
        .filter((item) => existingSavedFilters.includes(item.name))
        .map((item) => item.id);

      setSelectedItems(savedItemIds);
      setSearchQuery("");
      setShowSelected(false);
      setSelectAll(savedItemIds.length === items.length);
      setValidationError(null); // Clear validation error when modal opens
    }
  }, [isOpen, selectedItem, filterData]);

  useEffect(() => {
    if (filterloading) {
      setSelectAll(false);
    }
  }, [filterloading]);

  const items = Array.isArray(filterData)
    ? filterData.map((item, index) => ({
        id: String(index),
        name: item,
        selected: existingSavedFilters.includes(item),
      }))
    : [];

  const filteredItems = items.filter((item) => {
    const matchesSearch = item.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    if (showSelected) {
      return matchesSearch && selectedItems.includes(item.id);
    }
    return matchesSearch;
  });

  const handleSelectAllToggle = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedItems(items.map((item) => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSave = () => {
    const selectedFilters = items
      .filter((item) => selectedItems.includes(item.id))
      .map((item) => item.name);
    onSave({
      field: selectedItem?.id || "",
      value: selectedFilters,
    });
    onClose();
  };

  const toggleItem = (id: string) => {
    setSelectedItems((prev) => {
      if (prev.includes(id)) {
        return prev.filter((itemId) => itemId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 p-4 z-100"
      onClick={onClose}
    >
      <div
        className="flex max-h-[80vh] w-[85%]  flex-col rounded-lg bg-white shadow-lg"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="border-b p-4">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold">
              Filter {selectedItem?.label}
            </h2>
            <X
              className="h-4 w-4 cursor-pointer hover:text-gray-700"
              onClick={onClose}
            />
          </div>
          <Input
            type="text"
            placeholder="Search items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="mb-2"
            disabled={mode === "view"}
          />
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="select-all" className="text-sm">
                Select All
              </Label>
              <Switch
                id="select-all"
                checked={selectAll}
                onCheckedChange={handleSelectAllToggle}
                disabled={mode === "view"}
              />
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="show-selected" className="text-sm">
                Show Selected
              </Label>
              <Switch
                id="show-selected"
                checked={showSelected}
                onCheckedChange={setShowSelected}
                disabled={mode === "view"}
              />
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {filterloading ? (
            <div className="flex justify-center items-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="text-center text-muted-foreground py-10">
              No data found.
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-4">
              {filteredItems
                .reduce((acc: JSX.Element[][], item, index) => {
                  const columnIndex = Math.floor(index / 10);
                  if (!acc[columnIndex]) acc[columnIndex] = [];
                  acc[columnIndex].push(
                    <div
                      key={item.id}
                      className="flex items-center space-x-2 rounded p-2 hover:bg-gray-50"
                    >
                      <Checkbox
                        id={`item-${item.id}`}
                        checked={selectedItems.includes(item.id)}
                        onCheckedChange={() => toggleItem(item.id)}
                        disabled={mode === "view"}
                      />
                      <Label
                        htmlFor={`item-${item.id}`}
                        className="flex-1 cursor-pointer"
                      >
                        {item.name}
                      </Label>
                    </div>
                  );
                  return acc;
                }, [])
                .map((column, index) => (
                  <div key={index} className="space-y-2">
                    {column}
                  </div>
                ))}
            </div>
          )}
        </div>

        <div className="border-t p-4">
          <div className="flex justify-end gap-3">
            <Button
              onClick={onClose}
              className="text-white bg-primary hover:bg-primary"
              disabled={mode === "view"}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="text-white bg-primary hover:bg-primary"
              disabled={mode === "view"}
            >
              Save
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default FilterModal;
