"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ChevronDown, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Toaster } from "@/components/ui/toaster";

const DUMMY_RULES = [
  { name: "Similar Domains", level: "Low" },
  { name: "Parked Domains", level: "Medium" },
  { name: "Blogging Websites", level: "High" },
  { name: "Apps/Aps", level: "Low" },
  { name: "Sponsored Ads", level: "Medium" },
  { name: "Google Business Listings", level: "Low" },
  { name: "Suspicious Offers", level: "High" },
  { name: "Suspicious Handles", level: "High" },
  { name: "Job Promotions", level: "Medium" },
  { name: "Customer Care Number", level: "Low" },
  { name: "Misleading Information", level: "Medium" },
  { name: "Instant Messaging-Telegram/Whatsapp", level: "High" },
];

const RiskCategorization = () => {
  const [selectedItems, setSelectedItems] = useState({
    Low: {},
    Medium: {},
    High: {},
  });
  const [allItems, setAllItems] = useState([]);      
  const [pendingMoves, setPendingMoves] = useState([]);
  const [isPopoverOpen, setIsPopoverOpen] = useState({
    Low: false,
    Medium: false,
    High: false,
  });
  const [tempSelections, setTempSelections] = useState({
    Low: {},
    Medium: {},
    High: {},
  });
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const items = DUMMY_RULES.map((r) => ({ label: r.name, id: r.name }));
    setAllItems(items);

    const initial = { Low: {}, Medium: {}, High: {} };
    DUMMY_RULES.forEach((r) => {
      initial[r.level][r.name] = true;
    });
    setSelectedItems(initial);
  }, []);

  const groupedData = ["Low", "Medium", "High"].map((level) => ({
    level,
    items: allItems,
  }));

  const toggleSelection = (currentLevel, id) => {
    setTempSelections((prev) => ({
      ...prev,
      [currentLevel]: {
        ...prev[currentLevel],
        [id]: !prev[currentLevel]?.[id],
      },
    }));
  };

  const handlePopoverOpen = (level) => {
    setTempSelections((prev) => ({
      ...prev,
      [level]: { ...selectedItems[level] },
    }));
    setIsPopoverOpen((prev) => ({ ...prev, [level]: true }));
  };

  const handlePopoverClose = (level) => {
    const conflicts = [];
    Object.entries(tempSelections[level] || {}).forEach(([ruleId, isSelected]) => {
      if (isSelected) {
        Object.entries(selectedItems).forEach(([otherLevel, items]) => {
          if (otherLevel !== level && items[ruleId]) {
            conflicts.push({ ruleId, fromLevel: otherLevel, toLevel: level });
          }
        });
      }
    });

    if (conflicts.length > 0) {
      setPendingMoves(conflicts);
    } else {
      setSelectedItems((prev) => ({
        ...prev,
        [level]: {
          ...prev[level],
          ...tempSelections[level],
        },
      }));
    }

    setIsPopoverOpen((prev) => ({ ...prev, [level]: false }));
  };

  const handleMoveConfirmation = async () => {
    setIsUpdating(true);
    setSelectedItems((prev) => {
      const newState = { ...prev };
      pendingMoves.forEach(({ ruleId, fromLevel, toLevel }) => {
        newState[fromLevel][ruleId] = false;
        newState[toLevel][ruleId] = true;
      });
      return newState;
    });
    setTimeout(() => {
      setPendingMoves([]);
      setTempSelections({ Low: {}, Medium: {}, High: {} });
      setIsPopoverOpen({ Low: false, Medium: false, High: false });
      setIsUpdating(false);
    }, 500);
  };

  return (
    <div className="w-full max-w-4xl mx-auto py-10">
      <Card className="border p-4">
        <CardTitle className="mb-4 text-xl font-semibold text-black dark:text-white">
          Risk Categorization Status
        </CardTitle>
        <CardContent className="space-y-6 dark:text-white">
          {groupedData.map((section, index) => (
            <div key={section.level}>
              <div className="flex items-center gap-4 mb-4">
                <h3 className="text-lg font-medium text-gray-700 min-w-[100px] dark:text-white">
                  {section.level}
                </h3>
                <Popover
                  open={isPopoverOpen[section.level]}
                  onOpenChange={(open) => {
                    open ? handlePopoverOpen(section.level) : handlePopoverClose(section.level);
                  }}
                >
                  <PopoverTrigger asChild>
                    <Button variant="outline">
                      Select Rules <ChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full max-w-xl max-h-96 overflow-auto">
                    <div className="grid grid-cols-2 gap-2 p-2">
                      {section.items.map((item) => (
                        <div key={item.id} className="flex items-center gap-2">
                          <Checkbox
                            id={`${section.level}-${item.id}`}
                            checked={!!tempSelections[section.level]?.[item.id]}
                            onCheckedChange={() => toggleSelection(section.level, item.id)}
                          />
                          <label htmlFor={`${section.level}-${item.id}`} className="text-sm">
                            {item.label}
                          </label>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-end mt-4">
                      <Button
                        onClick={() => handlePopoverClose(section.level)}
                        className="text-white bg-primary hover:bg-primary"
                      >
                        Done
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                {section.items
                  .filter((item) => selectedItems[section.level]?.[item.id])
                  .map((item) => (
                    <span
                      key={item.id}
                      className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                    >
                      {item.label}
                    </span>
                  ))}
              </div>

              {index < groupedData.length - 1 && <hr className="border-t border-gray-300 my-6" />}
            </div>
          ))}
        </CardContent>
      </Card>

      <Dialog open={pendingMoves.length > 0} onOpenChange={(open) => !open && setPendingMoves([])}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Move Rules to Different Level</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {pendingMoves.length > 0 && (
              <>
                Are you sure you want to move the following rules?
                <ul className="mt-2 space-y-2">
                  {pendingMoves.map((move, index) => (
                    <li key={index} className="text-sm">
                      • "{move.ruleId}" from <b>{move.fromLevel}</b> to <b>{move.toLevel}</b>
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
          <DialogFooter>
            <Button
              onClick={() => setPendingMoves([])}
              className="text-white bg-primary hover:bg-primary"
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleMoveConfirmation}
              className="text-white bg-primary hover:bg-primary"
              disabled={isUpdating}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Updating...
                </>
              ) : (
                "Confirm Move"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
  );
};

export default RiskCategorization;
 