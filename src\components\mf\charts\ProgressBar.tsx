"use client";
import { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import HeaderRow from "@/components/mf/HeaderRow";
import { Loader2 } from "lucide-react";
import { useFullscreen } from "@/hooks/fullscreen";
interface ProgressBarChart1Item {
  label: string;
  visit: number; // This will be incidents count
  percentage: string; // Takedown percentage
  fill: string;
}
interface ProgressBarChart1Props {
  chartData: ProgressBarChart1Item[];
  title?: string;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  handleExport?: () => void;
  isLoading?: boolean;
  incidentsBarColor?: string;
  takedownBarColor?: string;
  incidentsLegendColor?: string;
  takedownLegendColor?: string;
  tooltipPosition?: "top" | "bottom";
  roundPercentage?: boolean; // New prop to control percentage rounding
  stickyLegend?: boolean; // New prop to make legends sticky
}
export default function ProgressBarCahrt1({
  chartData,
  title,
  onExpand = () => {},
  onExport,
  handleExport,
  isLoading,
  incidentsBarColor,
  takedownBarColor,
  incidentsLegendColor,
  takedownLegendColor,
  tooltipPosition = "top",
  roundPercentage = true, // Default to true for backward compatibility
  stickyLegend = false, // Default to false for backward compatibility
}: ProgressBarChart1Props) {
  const [animatedValues, setAnimatedValues] = useState<{
    [key: string]: number;
  }>({});
  const [hoveredLabel, setHoveredLabel] = useState<string | null>(null);
  const hasAnimated = useRef<boolean>(false);
  const isFullscreen = useFullscreen();

  // Dynamic spacing for fullscreen
  const getFullscreenSpacing = () => {
    if (window.innerWidth < 640) {
      return { gap: 2, padding: 2 }; // Mobile
    } else if (window.innerWidth >= 640 && window.innerWidth <= 1024) {
      return { gap: 3, padding: 3 }; // Tablet
    } else if (window.innerWidth > 1024 && window.innerWidth < 1280) {
      return { gap: 4, padding: 4 }; // Desktop
    } else {
      return { gap: 6, padding: 6 }; // Large screens
    }
  };

  const fullscreenSpacing = isFullscreen ? getFullscreenSpacing() : { gap: 4, padding: 4 };

  useEffect(() => {
    if (
      !chartData ||
      !Array.isArray(chartData) ||
      chartData.length === 0 ||
      hasAnimated.current
    ) {
      return;
    }
    const initialValues: { [key: string]: number } = {};
    chartData.forEach((item) => {
      initialValues[item.label] = 0;
    });
    setAnimatedValues(initialValues);
    chartData.forEach((item, index) => {
      setTimeout(
        () => {
          setAnimatedValues((prev) => {
            const percentageValue =
              item.percentage && typeof item.percentage === "string"
                ? parseFloat(item.percentage.replace("%", ""))
                : 0;
            return {
              ...prev,
              [item.label]: percentageValue,
            };
          });
        },
        300 + index * 150
      );
    });
    hasAnimated.current = true;
  }, [chartData]);
  // Update values without animation when chartData changes after initial render
  useEffect(() => {
    if (
      !chartData ||
      !Array.isArray(chartData) ||
      chartData.length === 0 ||
      !hasAnimated.current
    ) {
      return;
    }
    const newValues: { [key: string]: number } = {};
    chartData.forEach((item) => {
      const percentageValue =
        item.percentage && typeof item.percentage === "string"
          ? parseFloat(item.percentage.replace("%", ""))
          : 0;
      newValues[item.label] = percentageValue;
    });
    setAnimatedValues(newValues);
  }, [chartData]);
  return (
    <Card className={`border-none h-full ${isFullscreen ? 'h-[100vh]' : ''}`}>
      <HeaderRow
        title={title}
        onExpand={onExpand}
        onExport={onExport}
        handleExport={handleExport}
        titleFontSize="text-base"
      />
      <CardContent className={`h-[calc(100%-48px)] flex flex-col ${isFullscreen ? 'p-4' : ''}`}>
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : !chartData ||
          !Array.isArray(chartData) ||
          chartData.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <span className="text-sm dark:text-white">
                No Data Found !
              </span>
            </div>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* Scrollable Chart Content */}
            <div className={`flex-1 ${stickyLegend ? 'overflow-y-auto scrollbar' : ''} pt-4`}>
              <div className={`flex flex-col gap-${fullscreenSpacing.gap} mb-8 pt-2 pl-${fullscreenSpacing.padding} pr-${fullscreenSpacing.padding}`}>
              {chartData.map((item) => {
                const currentValue = animatedValues[item.label] || 0;
                const isHovered = hoveredLabel === item.label;
                const defaultGradient = takedownBarColor
                  ? `linear-gradient(135deg, ${takedownBarColor} 0%, ${takedownBarColor} 100%)`
                  : "linear-gradient(135deg, rgba(99, 102, 241, 0.85) 0%, rgba(79, 70, 229, 0.85) 100%)";
                const defaultColor =
                  takedownBarColor || "rgba(99, 102, 241, 0.85)";
                const gradient = item.fill || defaultGradient;
                const color = item.fill || defaultColor;
                const glowColor = item.fill || defaultColor;
                // Find max visit value in dataset for scaling
                const maxVisit = Math.max(...chartData.map((d) => d.visit));
                // Calculate width percentage for incidents bar
                const incidentsWidth = (item.visit / maxVisit) * 100;
                return (
                  <div
                    key={item.label}
                    className="flex items-center gap-1 group relative"
                    onMouseEnter={() => setHoveredLabel(item.label)}
                    onMouseLeave={() => setHoveredLabel(null)}
                  >
                    {/* Tooltip */}
                    <div
                      className={`absolute left-1/2 -translate-x-1/2 z-50 hidden group-hover:flex flex-col items-start bg-white text-gray-900 text-xs rounded-md px-3 py-2 shadow-lg transition-opacity duration-200 pointer-events-none w-max max-w-[200px] border border-gray-200 ${
                        tooltipPosition === "top" ? "-top-16" : "top-8"
                      }`}
                    >
                      <div>
                        <span className="font-semibold">Label:</span>{" "}
                        {item.label}
                      </div>
                      <div>
                        <span className="font-semibold">Incidents:</span>{" "}
                        {item.visit}
                      </div>
                      <div>
                        <span className="font-semibold">Takedown%:</span>{" "}
                        {roundPercentage ? currentValue.toFixed(2) : currentValue}%
                      </div>
                    </div>
                    {/* Label */}
                    <div className="min-w-[60px] text-xs font-normal text-gray-600 dark:text-white mr-1 text-right">
                      {item.label}
                    </div>
                    {/* Progress bars container */}
                    <div className="flex-1 relative perspective-1000">
                      {/* Incidents bar */}
                      <div
                        className={`h-2 transition-all duration-1000 ease-out ${
                          isHovered ? "shadow-lg" : "shadow-md"
                        }`}
                        style={{
                          width: `${incidentsWidth}%`,
                          marginTop: "2px",
                          backgroundColor: incidentsBarColor || "#9CA3AF",
                        }}
                      />
                      {/* Takedown% bar */}
                      <div
                        className={`h-3.5 transition-all duration-1000 ease-out absolute top-0 left-0 overflow-hidden ${
                          isHovered ? "shadow-2xl" : "shadow-lg"
                        }`}
                        style={{
                          width: `${currentValue * (incidentsWidth / 100)}%`,
                          background: gradient,
                          boxShadow: isHovered
                            ? `0 0 25px ${glowColor}60, 0 6px 20px ${glowColor}40`
                            : `0 0 15px ${glowColor}40, 0 4px 12px ${glowColor}30`,
                        }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-shimmer" />
                      </div>
                    </div>
                    {/* Value info */}
                    <div className="min-w-[90px] flex items-center justify-end gap-1 text-sm font-medium text-gray-600 dark:text-white">
                      <span>{item.visit}</span>
                      <span className="text-gray-400">•</span>
                      <span>{roundPercentage ? currentValue.toFixed(2) : currentValue}%</span>
                    </div>
                  </div>
                );
              })}
            </div>
            </div>
            
            {/* Sticky Legends */}
            {stickyLegend && (
              <div className="flex items-center justify-center gap-6 pt-2 text-xs bg-white">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: incidentsLegendColor || '#FFF9C4' }}></div>
                  <span style={{ color: 'black' }}>Incidents</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: takedownLegendColor || '#FFD600' }}></div>
                  <span style={{ color: 'black' }}>Takedown%</span>
                </div>
              </div>
            )}
            
            {/* Non-sticky Legends (for backward compatibility) */}
            {!stickyLegend && (
              <div className="flex items-center justify-center gap-6 pt-2 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: incidentsLegendColor || '#FFF9C4' }}></div>
                  <span style={{ color: 'black' }}>Incidents</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: takedownLegendColor || '#FFD600' }}></div>
                  <span style={{ color: 'black' }}>Takedown%</span>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
