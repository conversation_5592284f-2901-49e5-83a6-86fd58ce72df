'use client'
import React, { useState, useMemo, useRef, Dispatch, SetStateAction, useCallback, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Globe, Server, Database, LinkIcon } from "lucide-react";
import { FilterPill } from "@/components/mf/Filters";
import { useQuery } from "react-query";
import axios from "axios";
import ResizableTable from "@/components/mf/RulesTableComponent";
import Endpoint from "@/common/endpoint";

import { usePackage } from "@/components/mf/PackageContext";
import { FilterState } from "@/components/mf/Filters/FilterPill";
import { StackedBarWithLine } from "@/components/mf/charts/StackedBarwithLine";

import { PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import HeaderRow from "@/components/mf/HeaderRow";
import { onExpand, downloadURI, handleCSVDownloadFromResponse } from "@/lib/utils";
import DynamicBar<PERSON>hart from "@/components/mf/DynamicBarChart";
import Table from "@/components/mf/TableComponent";
import CustomCategoryCard from "@/components/mf/CustomCategoryCard";
import CenteredStatCard from "@/components/ui/CenteredStatCard";
import CustomKeyValueCard from "@/components/mf/CustomKeyValueCard";
import domToImage from "dom-to-image";



interface FilterResponse {
  data: string[];
  status: boolean;
}

interface IncidentData {
  category: string;
  [key: string]: string | number; // Make it flexible to accept any key from API
  label: string;
  value: number;
}


interface CategoryDataResponse {
  data: {
    category: string;
    count: number;
    percentage: number;
  }[];
  status: boolean;
}

interface HostingProviderDataResponse {
  data: {
    provider: string;
    incident_count: number;
    logo_url: string;
  }[];
  status: boolean;
}

interface TopCategoriesChartResponse {
  data: {
    category: string;
    [key: string]: string | number; 
  }[];
  status: boolean;
}

interface TopPlatformsChartResponse {
  data: {
    platform: string;
    [key: string]: string | number;
  }[];
  status: boolean;
}

interface TransformedCategoryData {
  title: string;
  count: string;
  percentage: string;
  bgColor: string;
  link: string;
  customBgColor: string;
}

// Interface for Takedown API response
interface TakedownResponse {
  data: {
    category: string;
    details: string;
    date: string;
    threat_type: string;
    screenshot_url: string;
  }[];
  status: boolean;
}

// Transform category data
const transformCategoryData = (response: CategoryDataResponse): TransformedCategoryData[] => {
  // Dynamic color palette for categories
  const colors = ["#274745", "#E8C468", "#147878", "#14B8A6", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4"];
  
  try {
    const data = response.data;
    console.log('Transforming category data:', data);
    return data.map((item, index) => {
      const color = colors[index % colors.length] || "#274745";
      
      return {
        title: item.category,
        count: new Intl.NumberFormat('en-US').format(item.count),
        percentage: item.percentage.toString(),
        bgColor: "bg-gradient-to-r from-gray-100 to-gray-200",
        link: "",
        customBgColor: color
      };
    });
  } catch (error) {
    console.error('Error transforming category data:', error);
    return [];
  }
};

// Transform hosting provider data
const transformHostingProviderData = (response: HostingProviderDataResponse): TransformedCategoryData[] => {
  const colors = ["#274745", "#E8C468", "#147878"];
  
  try {
    const data = response.data;
    console.log('Transforming hosting provider data:', data);
    return data.map((item, index) => {
      const color = colors[index] || "#274745";
      
      return {
        title: item.provider,
        count: new Intl.NumberFormat('en-US').format(item.incident_count),
        percentage: item.incident_count.toString(),
        bgColor: "bg-gradient-to-r from-gray-100 to-gray-200",
        link: "",
        customBgColor: color
      };
    });
  } catch (error) {
    console.error('Error transforming hosting provider data:', error);
    return [];
  }
};

const generateDynamicConfig = (data: any[]): Record<string, { label: string; color: string }> => {
  if (!data || data.length === 0) {
    // Return default config if no data
    return {
      "Last Month": { label: "Last Month", color: "#274745" },
      "Second Last Month": { label: "Second Last Month", color: "#E8C468" },
      "Third Last Month": { label: "Third Last Month", color: "#147878" },
      "MTD": { label: "MTD", color: "#14B8A6" }
    };
  }

  // Get all unique keys from the data (excluding category, label, value)
  const sampleItem = data[0];
  const excludeKeys = ['category', 'label', 'value'];
  const dynamicKeys = Object.keys(sampleItem).filter(key => !excludeKeys.includes(key));

  // Define color palette for dynamic keys
  const colorPalette = ["#274745", "#E8C468", "#147878", "#14B8A6", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4"];
  
  const config: Record<string, { label: string; color: string }> = {};
  
  dynamicKeys.forEach((key, index) => {
    config[key] = {
      label: key, // You can add custom label mapping here if needed
      color: colorPalette[index % colorPalette.length]
    };
  });

  return config;
};

// Transform top categories chart data - Updated to be dynamic
const transformTopCategoriesData = (response: TopCategoriesChartResponse): IncidentData[] => {
  try {
    const data = response.data;
    console.log('Transforming top categories chart data:', data);
    
    if (!data || data.length === 0) {
      return [];
    }

    return data.map((item) => {
      // Create a dynamic object that includes all properties from the API response
      const transformedItem: any = {
        category: item.category,
        label: item.category,
        value: item.MTD || 0 // Default to MTD or 0
      };

      // Dynamically add all keys from the API response (excluding category, label, value)
      Object.keys(item).forEach(key => {
        if (key !== 'category' && key !== 'label' && key !== 'value') {
          transformedItem[key] = item[key];
        }
      });

      return transformedItem as IncidentData;
    });
  } catch (error) {
    console.error('Error transforming top categories chart data:', error);
    return [];
  }
};


const columns = [
  // { key: "id", title: "ID" },
  { key: "subCategory", title: "Category" },
  { 
    key: "details", 
    title: "Details"
  },
  { key: "date", title: "Date" },
  { key: "threatType", title: "Threat Type" },
  {
    key: "screenshot",
    title: "Screenshot",
    render: (row: any) =>
      row.screenshot ? (
        <a
          href={typeof row.screenshot === "string" ? row.screenshot : "#"}
          target="_blank"
          rel="noopener noreferrer"
        >
          <div className="flex justify-center">
          <LinkIcon className="w-5 h-5 font-semibold text-primary hover:text-purple-700 hover:scale-110 hover:cursor-pointer transition-all duration-200" />
          </div>
        </a>
      ) : (
        "-"
      )
  }
];



// Transform top platforms chart data - Updated to be dynamic
const transformTopPlatformsData = (response: TopPlatformsChartResponse) => {
  try {
    const data = response.data;
    console.log('Transforming top platforms chart data:', data);
    
    if (!data || data.length === 0) {
      return [];
    }

    return data.map((item) => {
      // Create a dynamic object that includes all properties from the API response
      const transformedItem: any = {
        label: item.platform,
        value: item.MTD || 0 // Default to MTD or 0
      };

      // Dynamically add all keys from the API response (excluding platform, label, value)
      Object.keys(item).forEach(key => {
        if (key !== 'platform' && key !== 'label' && key !== 'value') {
          transformedItem[key] = item[key];
        }
      });

      return transformedItem;
    });
  } catch (error) {
    console.error('Error transforming top platforms chart data:', error);
    return [];
  }
};


const Insights = () => {
  // const { startDate: fromDate, endDate: toDate } = useDateRange();
  const { selectedPackage } = usePackage();
  const [loading, setLoading] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<string[]>(["all"]);
  const [selectedPriority, setSelectedPriority] = useState<string[]>(["all"]);
  const [selectedCountry, setSelectedCountry] = useState<string[]>(["all"]);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const cardRefs = useRef<HTMLElement[]>([]);

  // Add effect to handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && !(document as any).webkitFullscreenElement && expandedCard !== null) {
        // User exited fullscreen, reset the expanded card state
        setExpandedCard(null);
      }
    };

    // Add listeners for different browser implementations
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [expandedCard]);

  // Common query params for all APIs
  const queryParams = useMemo(() => {
    // Helper function to format filter values
    // Always send as array - both "all" and selected filters
    const formatFilterValue = (filters: string[]) => {
      // If no filters are selected or only "all" is selected, return "all" as array
      if (filters.length === 0 || (filters.length === 1 && filters.includes("all"))) {
        return ["all"];
      }
      // If "all" is included along with other selections, send "all" as array
      if (filters.includes("all")) {
        return ["all"];
      }
      // Otherwise send selected filters as array
      return filters.filter(item => item !== "all");
    };

    return {
      package_name: selectedPackage,
      brand: formatFilterValue(selectedBrand),
      priority: formatFilterValue(selectedPriority),
      country: formatFilterValue(selectedCountry)
    };
  }, [selectedBrand, selectedPriority, selectedCountry, selectedPackage]);

  // Fetch filter data
  const { data: brandFilterData } = useQuery(
    ["brandFilter", selectedPackage],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'brand'),
        {
          package_name: selectedPackage,
          menu: "website_app",
          insights:true
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  const { data: priorityFilterData } = useQuery(
    ["priorityFilter", selectedPackage],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'priority'),
        {
          package_name: selectedPackage,
          menu: "website_app",
          insights:true
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    {
      enabled: !!selectedPackage,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    }
  );

  const { data: countryFilterData } = useQuery(
    ["countryFilter", selectedPackage],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.BI_FILTERS.replace(':col', 'country'),
        {
          package_name: selectedPackage,
          menu: "website_app",
          insights:true
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage,
    }
  );

  // API call for category data
  const { data: categoryData, isLoading: categoryLoading } = useQuery(
    ["categoryCards", queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.CATEGROY_CARDS,
        {
          ...queryParams
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.package_name,
    }
  );

  // API call for hosting provider data
  const { data: hostingProviderData, isLoading: hostingProviderLoading } = useQuery(
    ["hostingProviders", queryParams],
    async () => {
      console.log('Hosting Provider query is running...');
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.HOSTING_PROVIDER,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      // console.log('Hosting Provider API Response:', response.data);
      // console.log('Hosting Provider API Status:', response.status);
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.package_name,
    }
  );

  // API call for top categories chart data
  const { data: topCategoriesData, isLoading: topCategoriesLoading } = useQuery(
    ["topCategories", queryParams],
    async () => {
      console.log('Top Categories query is running...');
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.TOP_CATEGORIES,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
        // console.log('Top Categories API Response:', response.data);
        // console.log('Top Categories API Status:', response.status);
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.package_name,
    }
  );

  // API call for top platforms chart data
  const { data: topPlatformsData, isLoading: topPlatformsLoading } = useQuery(
    ["topPlatforms", queryParams],
    async () => {
      console.log('Top Platforms query is running...');
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.TOP_PLATFORMS,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
        // console.log('Top Platforms API Response:', response.data);
        // console.log('Top Platforms API Status:', response.status);
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!queryParams.package_name,
    }
  );

  // API call for Takedown Samples
  const { data: takedownApiData, isLoading: isTakedownLoading } = useQuery(
    ["takedown", queryParams],
    async () => {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.TAKEDOWN,
        queryParams,
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      enabled: !!selectedPackage,
    }
  );


  // Transform filter data
  const brandFilters = useMemo(() =>
    (brandFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedBrand.includes(label) || selectedBrand.includes("all")
    })),
    [brandFilterData?.data, selectedBrand]
  );

  const priorityFilters = useMemo(() =>
    (priorityFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedPriority.includes(label) || selectedPriority.includes("all")
    })),
    [priorityFilterData?.data, selectedPriority]
  );

  const countryFilters = useMemo(() =>
    (countryFilterData?.data || []).map((label: string) => ({
      label,
      checked: selectedCountry.includes(label) || selectedCountry.includes("all")
    })),
    [countryFilterData?.data, selectedCountry]
  );

  // Handle filter changes
  const handleFilterSubmit = (id: string, data: FilterState) => {
    switch (id) {
      case 'brand':
        setSelectedBrand(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'priority':
        setSelectedPriority(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        break;
      case 'country':
        setSelectedCountry(data.is_select_all ? ["all"] : data.filters.filter(f => f.checked).map(f => f.label));
        break;
    }
  };

  // Handle filter search
  const handleFilterSearch = (id: string, query: string) => {
    console.log(`Searching ${id} with query: ${query}`);
  };

  // Transform all data sets
  const transformedCategoryData = categoryData ? transformCategoryData(categoryData) : [];
  console.log('Final transformed category data:', transformedCategoryData);
  const transformedHostingProviderData = hostingProviderData ? transformHostingProviderData(hostingProviderData) : [];
  console.log('Final transformed hosting provider data:', transformedHostingProviderData);
  console.log('Hosting provider loading state:', hostingProviderLoading);
  console.log('Hosting provider data:', hostingProviderData);
  const transformedTopCategoriesData = topCategoriesData ? transformTopCategoriesData(topCategoriesData) : [];
  console.log('Final transformed top categories chart data:', transformedTopCategoriesData);
  const transformedTopPlatformsData = topPlatformsData ? transformTopPlatformsData(topPlatformsData) : [];
  console.log('Final transformed top platforms chart data:', transformedTopPlatformsData);

  // Transform takedown API data
  const Tabledata = React.useMemo(() => {
    if (!takedownApiData?.data) return [];
    return takedownApiData.data.map((item: any) => ({
      subCategory: item.category,
      details: item.details,
      date: item.date,
      threatType: item.threat_type,
      screenshot: item.screenshot_url,
    }));
  }, [takedownApiData?.data]);

  const handleExpand = (index: number) => {
    onExpand(index, cardRefs, expandedCard, setExpandedCard);
  };

  // Add missing handleDownload function
  const handleDownload = (item: Record<string, string | number>) => {
    // Here you would implement the actual download logic
    console.log("Downloading report for:", item);
  };

  // Add CSV export functions for each chart
  const handleCategoryCardsCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.CATEGROY_CARDS,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          // responseType: 'blob'
        }
      );

      await handleCSVDownloadFromResponse(response, 'category_cards.csv');
    } catch (error) {
      console.error('Error exporting category cards CSV:', error);
    }
  }, [queryParams]);

  const handleHostingProviderCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.HOSTING_PROVIDER,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          // responseType: 'blob'
        }
      );

      await handleCSVDownloadFromResponse(response, 'hosting_providers.csv');
    } catch (error) {
      console.error('Error exporting hosting providers CSV:', error);
    }
  }, [queryParams]);

  const handleTopCategoriesCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.TOP_CATEGORIES,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          // responseType: 'blob'
        }
      );

      await handleCSVDownloadFromResponse(response, 'top_categories.csv');
    } catch (error) {
      console.error('Error exporting top categories CSV:', error);
    }
  }, [queryParams]);

  const handleTopPlatformsCSVExport = useCallback(async () => {
    try {
      const idToken = localStorage.getItem('IdToken') || localStorage.getItem('IDToken');
      if (!idToken) throw new Error('No authorization token found');

      const response = await axios.post(
        process.env.NEXT_PUBLIC_BI_TEST + Endpoint.BI.WEB_INSIGHTS.TOP_PLATFORMS,
        {
          ...queryParams,
          export_type: "csv"
        },
        {
          headers: {
            Authorization: idToken,
            'Content-Type': 'application/json'
          }
          // responseType: 'blob'
        }
      );

      await handleCSVDownloadFromResponse(response, 'platforms_trend.csv');
    } catch (error) {
      console.error('Error exporting top platforms CSV:', error);
    }
  }, [queryParams]);

  const onExport = useCallback(
    async (s: string, title: string, index: number) => {
      if (!cardRefs.current[index]) return;
      const ref = cardRefs.current[index];

      if (!ref) return;
      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    [cardRefs]
  );

  return (
    <div className="min-h-screen w-full bg-[#F3F4F6] shadow-md dark:bg-gray-700">
      {/* Filter Bar */}
       <div className="sticky top-0 z-50 flex flex-col md:flex-row items-start md:items-center justify-between gap-3 rounded-md bg-background px-4 py-3 m-2 shadow-[0_-1px_2px_rgba(0,0,0,0.1)]">
        <div className="flex flex-wrap gap-3">
        <FilterPill
          id="brand"
          title="Brand"
          filters={brandFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={!brandFilterData}
          isSearchable={true}
        />
        <FilterPill
          id="priority"
          title="Priority"
          filters={priorityFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={!priorityFilterData}
        />
        <FilterPill
          id="country"
          title="Country"
          filters={countryFilters}
          onSubmit={handleFilterSubmit}
          onSearch={handleFilterSearch}
          loading={!countryFilterData}
          isSearchable={true}
        />
        </div>
      </div>

      <div className="flex flex-col m-2 gap-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {/* Top 3 Categories Cards */}
          <Card 
            ref={(el) => {
              if (el) cardRefs.current[0] = el;
            }}
            className="shadow-md h-full"
          >
            <HeaderRow
              title="Top Categories"
              onExpand={() => handleExpand(0)}
              onExport={() => onExport("png", "Top Categories", 0)}
                              handleExport={handleCategoryCardsCSVExport}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="h-[calc(100%-50px)] p-4">
              {categoryLoading ? (
                <div className="flex items-center justify-center h-[160px]">
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !transformedCategoryData || transformedCategoryData.length === 0 ? (
                <div className="flex items-center justify-center h-[160px]">
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 h-full">
                  {transformedCategoryData.map((cat, idx) => (
                    <div key={idx} className="h-full">
                      <CenteredStatCard
                        value={cat.count}
                        label={cat.title}
                        barPercent={parseFloat(cat.percentage) || 0}
                        barColor={cat.customBgColor}
                        heightClass="h-full"
                        valueOnTop={true}
                        percentage={`${cat.percentage}%`}
                      />     
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        {/* Top 3 Hosting Providers Cards*/}
          <Card 
            ref={(el) => {
              if (el) cardRefs.current[1] = el;
            }}
            className="shadow-md h-full"
          >
            <HeaderRow
              title="Top Hosting Providers"
              onExpand={() => handleExpand(1)}
              onExport={() => onExport("png", "Top Hosting Providers", 1)}
                              handleExport={handleHostingProviderCSVExport}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
            />
            <CardContent className="h-[calc(100%-50px)] flex items-center">
              {hostingProviderLoading ? (
                <div className="flex items-center justify-center h-[160px] w-full">
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !transformedHostingProviderData || transformedHostingProviderData.length === 0 ? (
                <div className="flex items-center justify-center h-[160px] w-full">
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full px-1">
                  {transformedHostingProviderData.map((cat: any, idx: number) => {
                    // Get provider name from API data
                    const providerName = cat.title;
                    const colors = ["#274745", "#E8C468", "#147878"];
                    const backgroundColor = `${colors[idx]}15`; // 15% opacity
                    const borderColor = colors[idx];
                  
                  return (
                    <div 
                      key={idx} 
                      className="flex flex-col items-center justify-center p-3 rounded-lg border border-gray-100 shadow-sm"
                      style={{
                        backgroundColor: backgroundColor,
                        borderRight: `5px solid ${borderColor}`
                      }}
                    >
                      <div className="w-16 h-16 rounded-full mb-2 overflow-hidden flex items-center justify-center">
                        <img 
                          src={hostingProviderData?.data[idx]?.logo_url || 
                            (idx === 0 ? "https://img.logo.dev/hostinger.com?token=pk_X-1ZO13CQpG5ajBsYs5ojQ" : 
                             idx === 1 ? "https://img.logo.dev/cloudflare.com?token=pk_X-1ZO13CQpG5ajBsYs5ojQ" : 
                             "https://img.logo.dev/unifiedlayer.com?token=pk_X-1ZO13CQpG5ajBsYs5ojQ")
                          }
                          alt={`${providerName} Logo`}
                          className="w-12 h-12 object-cover rounded-full"
                          onError={(e) => {
                            // Fallback to alternative logo sources if primary fails
                            const target = e.target as HTMLImageElement;
                            if (idx === 0 && !target.src.includes('logo.clearbit')) {
                              target.src = "https://logo.clearbit.com/hostinger.com";
                            } else if (idx === 1 && !target.src.includes('logo.clearbit')) {
                              target.src = "https://logo.clearbit.com/cloudflare.com";
                            } else if (idx === 2 && !target.src.includes('logo.clearbit')) {
                              target.src = "https://logo.clearbit.com/unifiedlayer.com";
                            } else {
                              // Final fallback to Globe icon
                              target.style.display = 'none';
                              const globeIcon = target.parentElement?.querySelector('.fallback-globe');
                              if (globeIcon) globeIcon.classList.remove('hidden');
                            }
                          }}
                        />
                        <Globe className="w-10 h-10 text-primary fallback-globe hidden" />
                      </div>
                      <span className="text-sm text-gray-500 mb-1">{providerName}</span>
                      <h3 className="text-base font-medium text-gray-700 mb-1">Incidents Reported</h3>
                      <span className="text-lg font-semibold text-primary">{cat.count}</span>
                    </div>
                  );
                })}
              </div>
              )}
            </CardContent>
          </Card>
        </div>
        {/* Second Row - Bar Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">

          {/* Top 5 Categories Chart */}
          <Card 
            ref={(el) => {
              if (el) cardRefs.current[2] = el;
            }}
            className={`shadow-md ${expandedCard === 2 ? 'h-[100vh]' : ''}`}
          >
            <HeaderRow
              title="Top Categories Trend"
              onExpand={() => handleExpand(2)}
              onExport={() => onExport("png", "Top Categories Trend", 2)}
                              handleExport={handleTopCategoriesCSVExport}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
              isExpanded={expandedCard === 2}
            />
            <CardContent className={`${expandedCard === 2 ? 'h-[calc(100vh-48px)]' : 'h-[calc(100%-50px)]'}`}>
              {topCategoriesLoading ? (
                <div className={`flex items-center justify-center ${expandedCard === 2 ? 'h-[calc(100vh-120px)]' : 'h-[270px]'}`}>
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !transformedTopCategoriesData || transformedTopCategoriesData.length === 0 ? (
                <div className={`flex items-center justify-center ${expandedCard === 2 ? 'h-[calc(100vh-120px)]' : 'h-[270px]'}`}>
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
                <div className={`${expandedCard === 2 ? 'h-[calc(100vh-120px)]' : 'h-full'} w-full`}>
                  <DynamicBarChart
                    data={transformedTopCategoriesData}
                    config={generateDynamicConfig(transformedTopCategoriesData)}
                    isHorizontal={false}
                    onExpand={() => {}}
                    position="top"
                    isLoading={topCategoriesLoading}
                    formatterType="number"
                    isRadioButton={false}
                    isSelect={false}
                    selectoptions={[]}
                    showHeader={false}
                    isScrollable={false}
                    barSize={expandedCard === 2 ? 30 : 20}
                    xAxisTruncateLength={expandedCard === 2 ? 20 : 12}
                    height={expandedCard === 2 ? window.innerHeight - 120 : undefined}
                    isExpanded={expandedCard === 2}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Top 5 Platforms Chart */}
          <Card 
            ref={(el) => {
              if (el) cardRefs.current[3] = el;
            }}
            className={`shadow-md ${expandedCard === 3 ? 'h-[100vh]' : ''}`}
          >
            <HeaderRow
              title="Top Platforms Trend"
              onExpand={() => handleExpand(3)}
              onExport={() => onExport("png", "Top Platforms Trend", 3)}
                              handleExport={handleTopPlatformsCSVExport}
              isRadioButton={false}
              isSelect={false}
              titleFontSize="text-base"
              isExpanded={expandedCard === 3}
            />
            <CardContent className={`${expandedCard === 3 ? 'h-[calc(100vh-48px)]' : 'h-[calc(100%-50px)]'}`}>
              {topPlatformsLoading ? (
                <div className={`flex items-center justify-center ${expandedCard === 3 ? 'h-[calc(100vh-120px)]' : 'h-[270px]'}`}>
                  <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
                </div>
              ) : !transformedTopPlatformsData || transformedTopPlatformsData.length === 0 ? (
                <div className={`flex items-center justify-center ${expandedCard === 3 ? 'h-[calc(100vh-120px)]' : 'h-[270px]'}`}>
                  <span className="text-sm dark:text-white">No Data Found !</span>
                </div>
              ) : (
                <div className={`${expandedCard === 3 ? 'h-[calc(100vh-120px)]' : 'h-full'} w-full`}>
                  <DynamicBarChart
                    data={transformedTopPlatformsData}
                    config={generateDynamicConfig(transformedTopPlatformsData)}
                    isHorizontal={false}
                    onExpand={() => {}}
                    isRadioButton={false}
                    isSelect={false}
                    isLoading={topPlatformsLoading}
                    formatterType="number"
                    position="top"
                    showHeader={false}
                    isScrollable={false}
                    barSize={expandedCard === 3 ? 30 : 20}
                    height={expandedCard === 3 ? window.innerHeight - 120 : undefined}
                    isExpanded={expandedCard === 3}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Takedown Sample */}
        <div className="w-full bg-gray-200 text-base rounded-md font-bold grid justify-items-center p-3">
          Takedown Samples</div>
        <div className="flex-1 bg-white dark:bg-gray-700 min-h-0 flex flex-col">
        <div className="flex-1 overflow-hidden">
        {isTakedownLoading ? (
          <div className="flex items-center justify-center h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
          </div>
        ) : Tabledata.length === 0 ? (
          <div className="flex items-center justify-center h-[400px]">
            <span className="text-sm dark:text-white">No Data Found !</span>
          </div>
        ) : (
          <ResizableTable
            columns={columns}
            data={Tabledata}
            isSearchable={false}
            isPaginated={false}
            isSelectable={false}
            isEdit={false}
            isDelete={false}
            isView={false}
            isDownload={false}
            onDownload={handleDownload}
            initialItemsPerPage={12}
            showColumnSelector={false}
          />
        )}
        </div>
      </div>
      </div>
    </div>
  );
};

export default Insights;