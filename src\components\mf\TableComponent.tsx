"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  MdEdit,
  MdDelete,
  MdVisibility,
  MdFileDownload,
  MdArrowDropDown,
  MdSearch,
  MdRefresh,
  MdArrowDownward,
  MdArrowUpward,
  MdPause,
  MdPlayArrow,
  MdDownload,
} from "react-icons/md";
import { FiRefreshCw } from "react-icons/fi";
import { FaClone } from "react-icons/fa";
import { IoIosSend } from "react-icons/io";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import Pagination from "@/components/ui/pagination";
import {
  Select,
  SelectItem,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Import ShadCN Select components
import { Loader2 } from "lucide-react"; // Add this import
// import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import JSZip from "jszip";
import { Button } from "@/components/ui/button";

export type Column<T = void> =
  | { title: string; key: string }
  | { title: string; key: string; render: (data: T) => React.ReactNode };

interface ResizableTableProps<T> {
  buttonTextName?: string;
  columns: Column<T>[];
  data: T[];
  headerColor?: string;
  isEdit?: boolean;
  isDelete?: boolean;
  isClone?: boolean;
  isRefetch?: boolean;
  isSend?: boolean;
  isView?: boolean;
  isDownload?: boolean;
  onRefetch?: (params?: { startDate?: Date; endDate?: Date }) => void;
  isPaginated?: boolean;
  SearchTerm?:string;
  setSearchTerm: (term: string) => void;
  isSearchable?: boolean;
  isSelectable?: boolean;
  isCount?: boolean;
  isLoading?: boolean; // Add this prop
  isFile?:boolean;
  actionButton?: React.ReactNode | React.ReactNode[];
  onEdit?: (item: T) => void;
  onDownloadAll?: (item: T[]) => void;
  onDelete?: (item: T) => void;
  onView?: (item: T) => void;
  onDownload?: (item: T) => void;
  onRefresh?: () => void;
  onSelect?: (selectedItems: T[]) => void;
  itemCount?: (count: number) => void;
  isPause?: boolean;
  isPlay?: boolean;
  onPause?: (item: T) => void;
  onPlay?: (item: T) => void;
  onClone?: (item: T) => void;
  onSend?: (item: T) => void;
  onGenerateReport?: () => void;
  height?: number;
  onPageChangeP?: (page: number) => void;
  onLimitChange?: (page: number) => void;
  pageNo?: number;    
  totalPages?: number; 
}

const DropdownMenu: React.FC<{
  columns: Column<Record<string, string | number>>[];
  onToggle: (key: string) => void;
  visibleColumns: Column<Record<string, string | number>>[];
}> = ({ columns, onToggle, visibleColumns }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="flex w-56 items-center justify-between rounded border bg-card p-2 text-card-foreground">
          <span>Columns</span>
          <div className="ml-auto flex items-center space-x-2">
            {/* <div className="h-6 border-l-2 border-black" />  */}|
            <span className="ml-2 mt-1 text-sm text-[#540094]">
              {columns.length === visibleColumns.length
                ? "All"
                : visibleColumns.length}
            </span>
            <MdArrowDropDown className="ml-2" />
          </div>
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-56">
        {columns.map((column) => (
          <div key={column.key} className="flex items-center px-4 py-2">
            <Checkbox
              checked={visibleColumns.some((col) => col.key === column.key)}
              onCheckedChange={() => onToggle(column.key)}
            />
            <span className="ml-2">{column.title}</span>
          </div>
        ))}
      </PopoverContent>
    </Popover>
  );
};

const ResizableTable: React.FC<
  ResizableTableProps<Record<string, string | number>>
> = ({
  buttonTextName = "New Report",
  columns,
  data=[],
  headerColor = "#ccc",
  isEdit = false,
  isDelete = false,
  isClone = false,
  isRefetch = false,
  onRefetch,
  isSend = false,
  isView = false,
  isPaginated = true,
  isDownload = false,
  isSearchable = false,
  SearchTerm="",
  setSearchTerm,
  isSelectable = false,
  isCount = false,
  isLoading = false,
  actionButton,
  onEdit,
  onDelete,
  onView,
  onDownload,
  onSelect,
  onDownloadAll,
  onRefresh,
  itemCount,
  isPause = false,
  isPlay = false,
  onPause,
  onPlay,
  onClone,
  onSend,
  onGenerateReport,
  onPageChangeP,
  onLimitChange,
  pageNo,    // Total number of records
  totalPages =1,
  height, // Accept the height prop
}) => {
  const [selectedItems, setSelectedItems] = useState<
    Record<string, string | number>[]
  >([]);
  const [isMounted, setIsMounted] = useState(false);
  const [visibleColumns, setVisibleColumns] =
    useState<Column<Record<string, string | number>>[]>(columns);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: number }>(
    {},
  );
  const tableRef = useRef<HTMLTableElement>(null);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [containerHeight, setContainerHeight] = useState<number>(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRefetchModalOpen, setIsRefetchModalOpen] = useState(false);
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const displayData = isLoading ? [] : data; // Clear data when loading

  useEffect(() => {
    setIsMounted(true);
    const initialWidths: { [key: string]: number } = {};
    columns.forEach((col) => {
      initialWidths[col.key] = 150;
    });
    setColumnWidths(initialWidths);
  }, [columns]);
  
  useEffect(() => {
    if (onPageChangeP) {
      onPageChangeP(currentPage); // page change handler
    }
  }, [currentPage, onPageChangeP]);
  
  useEffect(() => {
    if (onLimitChange) {
      onLimitChange(itemsPerPage); // limit change handler
    }
  }, [itemsPerPage, onLimitChange]);
  
  useEffect(() => {
    const calculateHeight = () => {
      if (!height) {
        const vh = window.innerHeight;
        const otherElementsHeight = 200;
        setContainerHeight(vh - otherElementsHeight);
      } else {
        setContainerHeight(height);
      }
    };
   
    calculateHeight();
    window.addEventListener("resize", calculateHeight);

    return () => window.removeEventListener("resize", calculateHeight);
  }, [height]);

  const handleColumnToggle = (key: string) => {
    const newVisibleColumns = visibleColumns.some((col) => col.key === key)
      ? visibleColumns.filter((col) => col.key !== key)
      : [...visibleColumns, columns.find((col) => col.key === key)!];
    setVisibleColumns(newVisibleColumns);
  };

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "asc"
    ) {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const sortedData = React.useMemo(() => {
    if (!Array.isArray(data)) {
      console.error("Data is not an array:", data);
      return []; // Return an empty array to prevent the error
    }
    const sortableData = Array.isArray(data) ? [...data] : [];
    if (sortConfig !== null) {
      sortableData.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableData;
  }, [data, sortConfig]);

  const filteredData = sortedData.filter((item) => {
    return visibleColumns.some((column) =>
      String(item[column.key]).toLowerCase().includes(SearchTerm.toLowerCase()),
    );
  });

  // const paginatedData = React.useMemo(() => {
  //    const startIndex = (currentPage - 1) * itemsPerPage;
  //    return filteredData.slice(startIndex, startIndex + itemsPerPage);
  //  }, [filteredData, currentPage, itemsPerPage]);
  // console.log(sortedData,filteredData,itemsPerPage,"1stchild")



  const handleCheckboxChange = React.useCallback((item: Record<string, string | number>) => {
    const itemId = item.id || item.ID || item.Id;
    const isSelected = selectedItems.some((selectedItem) => {
      const selectedId = selectedItem.id || selectedItem.ID || selectedItem.Id;
      return selectedId === itemId;
    });
    
    if (isSelected) {
      const items = selectedItems.filter((selectedItem) => {
        const selectedId = selectedItem.id || selectedItem.ID || selectedItem.Id;
        return selectedId !== itemId;
      });
      setSelectedItems(items);
      if (onSelect) onSelect(items);
    } else {
      const items = [...selectedItems, item];
      setSelectedItems(items);
      if (onSelect) onSelect(items);
    }
  }, [selectedItems, onSelect]);

  const handleHeaderCheckboxChange = React.useCallback((checked: boolean) => {
    const allItems = checked ? filteredData : [];
    setSelectedItems(allItems);
    if (onSelect) {
      onSelect(allItems);
    }
  }, [filteredData, onSelect]);

  // Memoize the filtered data to prevent unnecessary re-renders
  const memoizedFilteredData = React.useMemo(() => filteredData, [filteredData]);

  // Column Resize Handlers
  const handleMouseDown = (e: React.MouseEvent, key: string) => {
    const startX = e.clientX;
    const startWidth = columnWidths[key];

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const newWidth = startWidth + moveEvent.clientX - startX;
      setColumnWidths((prevWidths) => ({
        ...prevWidths,
        [key]: newWidth,
      }));
    };

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  useEffect(() => {
    if (typeof itemCount === "function") itemCount(selectedItems.length);
  }, [selectedItems.length]);

  const handlePageChange = (newPage: number) => {
  //  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const validPage = Math.max(1, Math.min(newPage, totalPages));
    setCurrentPage(validPage);
    if (onPageChangeP) {
      onPageChangeP(validPage); // API call only on user change
    }
  };

  const handleRefetch = () => {
    if (startDate && endDate) {
      onRefetch?.({ startDate, endDate });
      setIsRefetchModalOpen(false);
    }
  };

  const downloadTableAsCSV = async () => {
    try {
      // Create a new instance of JSZip
      const zip = new JSZip();
      
      // Fetch the dummy.csv file
      const response = await fetch('/dummy.csv');
      const csvData = await response.blob();
      
      // Add the CSV to the zip file
      zip.file('dummy.csv', csvData);
      
      // Generate the zip file
      const zipContent = await zip.generateAsync({ type: 'blob' });
      
      // Create a download link
      const link = document.createElement('a');
      const currentDate = format(new Date(), 'yyyyMMdd');
      const fileName = `web.mfilterit.cpv_${currentDate}.zip`;
      
      link.href = URL.createObjectURL(zipContent);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  if (!isMounted) return null;

  return (
    <div className="w-full">
      <div className="flex w-full flex-wrap items-center space-x-2 rounded-lg border bg-card p-1.5 text-body">
        {isSearchable && (
          <div className="flex flex-grow items-center space-x-2 p-2 sm:w-full md:w-1/2 lg:w-1/2">
            <MdSearch className="text-xl text-[#540094] transition-colors duration-200 hover:text-gray-400" />
            <input
              type="text"
              placeholder="Search"
              value={SearchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-card text-card-foreground outline-none"
            />
          </div>
        )}
        {/* Separator */}
        {isSearchable && <div className="mx-2 h-6 border-l sm:hidden md:block lg:block" />}
        <DropdownMenu
          columns={columns}
          onToggle={handleColumnToggle}
          visibleColumns={visibleColumns}
        />
        {actionButton && (
          <div className="mx-2 h-6 border-l sm:hidden md:block lg:block" />
        )}
        {actionButton}
        {(actionButton || isCount) && <div className="mx-2 h-6 border-l sm:hidden md:block lg:block" />}
        {isCount && (
          <span
            title="Total Selected Rows"
            onClick={() =>
              typeof onDownloadAll === "function" ? onDownloadAll(data) : null
            }
            className="rounded-lg bg-purple-100 p-2 text-[#540094]"
          >
            <span>{selectedItems.length}</span>
          </span>
        )}
        {/* Only show separator if there are more elements after count */}
        {/* <Button variant="ghost" size="icon-xs" onClick={() => onRefresh()}>
          <MdRefresh
            title="Table Data lauds"
            className="cursor-pointer text-xl text-purple-600 transition-colors duration-200 hover:text-purple-700"
          />
        </Button> */}
          {/* <button
            onClick={downloadTableAsCSV}
            className="cursor-pointer text-xl text-[#540094] transition-colors duration-200 hover:text-gray-400"
            title="Download Table Data as CSV"
          >
            <MdFileDownload />
          </button> */}

        <div className="mx-2 h-6 border-l" />
        {buttonTextName && (
          <Button 
            variant="default" 
            className="text-white"
            onClick={onGenerateReport}
          >
            {buttonTextName}
          </Button>
        )}
      </div>
      
      

      <div
        className="relative"
        style={{
          height: `${containerHeight}px`,
          overflowY: "auto",
          overflowX: "auto",
        }}
      >
        <Table ref={tableRef} className="min-w-full border">
          <TableHeader className="sticky top-0 z-10">
            <TableRow>
              {isSelectable && (
                <TableHead
                  className="border-r text-center align-middle p-0"
                  style={{
                    width: "40px",
                    minWidth: "40px",
                    maxWidth: "40px",
                    backgroundColor: headerColor,
                  }}
                >
                  <div className="flex items-center justify-center w-full h-full">
                    <Checkbox
                      onCheckedChange={handleHeaderCheckboxChange}
                    />
                  </div>
                </TableHead>
              )}
              {visibleColumns.map((column) => (
                <TableHead
                  key={column.key}
                  className="relative border-r"
                  style={{
                    backgroundColor: headerColor,
                    color: "black",
                    width: `${columnWidths[column.key]}px`,
                    whiteSpace: "nowrap",
                  }}
                >
                  <div className="flex items-center justify-center text-center text-body font-semibold">
                    <span
                      onClick={() => handleSort(column.key)}
                      className="cursor-pointer text-center"
                      style={{ width: "100%" }}
                    >
                      {column.title}
                      {sortConfig?.key === column.key &&
                        (sortConfig?.direction === "asc" ? (
                          <MdArrowUpward className="inline-block" />
                        ) : sortConfig?.direction === "desc" ? (
                          <MdArrowDownward className="inline-block" />
                        ) : null)}
                    </span>
                    <div
                      onMouseDown={(e) => handleMouseDown(e, column.key)}
                      className="absolute right-0 top-0 h-full w-2 cursor-col-resize hover:bg-gray-400"
                      style={{ backgroundColor: "transparent" }}
                    />
                  </div>
                </TableHead>
              ))}

              {(isEdit ||
                isDelete ||
                isView ||
                isDownload ||
                isPause ||
                isPlay ||
                isRefetch ||
                isSend ||
                isClone) && (
                <TableHead
                  className="border-r text-center"
                  style={{
                    backgroundColor: headerColor,
                    color: "black",
                    width: `${columnWidths}px`,
                    whiteSpace: "nowrap",
                  }}
                >
                  Action
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell 
                  colSpan={visibleColumns.length + (isSelectable ? 1 : 0) + ((isEdit || isDelete || isView || isDownload || isPause || isPlay) ? 1 : 0)}
                  className="h-32 text-center"
                >
                  <div className="flex items-center justify-center w-full h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-[#540094]" />
                  </div>
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={visibleColumns.length + (isSelectable ? 1 : 0) + ((isEdit || isDelete || isView || isDownload || isPause || isPlay) ? 1 : 0)}
                  className="h-32 text-center"
                >
                  <div className="flex justify-center items-center h-full">
                    <span className="text-sm dark:text-white">No Data Found !</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((item, index) => (
                <TableRow 
                  key={index} 
                  className="h-8" // Add height class
                >
                  {isSelectable && (
                    <TableCell
                      className="border-r text-center align-middle p-0"
                      style={{
                        width: "40px",
                        minWidth: "40px",
                        maxWidth: "40px",
                        height: "24px",
                        lineHeight: "24px"
                      }}
                    >
                      <div className="flex items-center justify-center w-full h-full">
                        <Checkbox
                          key={`checkbox-${item.id || item.ID || item.Id || Math.random()}`}
                          checked={selectedItems.some((selectedItem) => {
                            const selectedId = selectedItem.id || selectedItem.ID || selectedItem.Id;
                            const itemId = item.id || item.ID || item.Id;
                            return selectedId === itemId;
                          })}
                          onCheckedChange={() => handleCheckboxChange(item)}
                        />
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.map((column) => (
                    <TableCell 
                      key={column.key} 
                      className="border-r text-center dark:text-white text-body p-2" // Changed from text-small-font to text-body for larger font size
                      style={{ height: "24px", lineHeight: "24px" }} // Explicit height
                    >
                      {"render" in column ? column.render(item) : item[column.key]}
                    </TableCell>
                  ))}
                  {(isEdit || isDelete || isView || isDownload || isPause || isPlay) && (
                    <TableCell 
                      className="border-r dark:text-white p-2 text-body" // Added text-body for consistent font size
                      style={{ height: "24px", lineHeight: "24px" }} // Explicit height
                    >
                      <div className="flex space-x-2 justify-center">
                        {isEdit && (
                          <button
                            onClick={() => onEdit?.(item)}
                            className="text-primary"
                          >
                            <MdEdit size={18} />
                          </button>
                        )}
                        {isDelete && (
                          <button
                            onClick={() => onDelete?.(item)}
                            className="text-primary"
                          >
                            <MdDelete size={18} />
                          </button>
                        )}
                        {isView && (
                          <button
                            onClick={() => onView?.(item)}
                            className="text-primary"
                          >
                            <MdVisibility size={18} />
                          </button>
                        )}
                        {/* {isDownload && (
                          <button
                            onClick={() => onDownload?.(item)}
                            className="text-primary hover:text-primary/80"
                          >
                            <MdFileDownload size={18} />
                          </button>
                        )} */}
                         <div className="w-6 h-6 flex items-center justify-center">
                              {isDownload && item.report_status === "No Data" && item.download === "yes" && (
                                <button
                                  disabled={true}
                                  title="No data"
                                  className="text-gray-500"
                                >
                                  <MdFileDownload size={18} />
                                </button>
                              )}
                              {isDownload && (item.report_status === "mail sent" || item.report_status === "url generated" || item.report_status === "uploaded to s3") && item.download === "yes" && (
                                <button
                                  onClick={() => onDownload?.(item)}
                                  className="text-primary hover:text-gray-500"
                                >
                                  <MdFileDownload size={18} />
                                </button>
                              )}
                              {/* {isDownload && item.report_status === "url generated" && item.download === "yes" && (
                                <button
                                  onClick={() => onDownload?.(item)}
                                  className="text-primary hover:text-gray-500"
                                >
                                  <MdFileDownload size={18} />
                                </button>
                              )} */}
                            </div>
 
                        {isPause && (
                          <button
                            onClick={() => onPause?.(item)}
                            className="text-[#540094] hover:text-gray-500"
                          >
                            <MdPause size={18} />
                          </button>
                        )}
                        {isPlay && (
                          <button
                            onClick={() => onPlay?.(item)}
                            className="text-[#540094] hover:text-gray-500"
                          >
                            <MdPlayArrow size={18} />
                          </button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {isPaginated &&  totalPages!== 0 &&(
        <div className="mt-1 flex items-center justify-between">
          <div className="flex items-center">
            <Select
              value={String(itemsPerPage)}
              onValueChange={(value) => {
                setItemsPerPage(Number(value));
                setCurrentPage(1);
              }}
              
            >
              <SelectTrigger className="w-[70px] outline-none focus:ring-0  text-small-font dark:text-white">
                <SelectValue placeholder="Rows" />
              </SelectTrigger>
              <SelectContent className="border-none outline-none focus:ring-0">
                {/* <SelectItem value="5">5</SelectItem> */}
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="200">200</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            showFirstLast={true}
           
          />
        </div>
      )}

      <Dialog open={isRefetchModalOpen} onOpenChange={setIsRefetchModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Select Date Range</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label>End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleRefetch} disabled={!startDate || !endDate}>
              Refetch Data
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ResizableTable;
