import { Loader2 } from 'lucide-react';
import React from 'react';

interface CardData {
  Total?: {
    count: number;
    title: string;
    color: string;
  };
  Active?: {
    count: number;
    title: string;
    color: string;
  };
  InProgress?: {
    count: number;
    title: string;
    color: string;
  };
  Closed?: {
    count: number;
    title: string;
    color: string;
    breakdown?: Array<{
      label: string;
      value: number;
    }>;
  };
}

interface StatsCardsProps {
  cardData: CardData;
  isLoading?: boolean;
  isFetching?: boolean; // new: fetching fresh data
  showWhileFetching?: boolean; // new: if true show loader while fetching
  isVertical?: boolean;
  compactHeight?: boolean;
}

const StatsCards: React.FC<StatsCardsProps> = ({
  cardData,
  isLoading,
  isFetching,
  showWhileFetching = false,
  isVertical = false,
  compactHeight = false,
}) => {
  const getCardHeight = (cardType: 'Total' | 'Active' | 'InProgress' | 'Closed') => {
    if (compactHeight && (cardType === 'Total' || cardType === 'Active' || cardType === 'InProgress')) {
      return 'h-[120px]';
    }
    return 'h-[140px]';
  };

  const shouldShowLoader = Boolean(isLoading || (showWhileFetching && isFetching));

  return (
    <div className="grid grid-cols-2 gap-2 w-full h-[300px]">
      {/* Top Left: Incidents Reported */}
      <div className="flex-1">
        <div
          className="card border-0 relative overflow-hidden h-full bg-white dark:bg-card rounded-lg shadow-md"
          style={{ borderRight: `5px solid ${cardData.Total?.color || '#540094'}`,
          borderBottom: `5px solid ${cardData.Total?.color || '#540094'}` }}
        >
          {shouldShowLoader ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="p-4 h-full flex flex-col">
              <div className="text-base font-semibold text-card-foreground text-center mb-2">
                {cardData?.Total?.title || 'Incidents Reported'}
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="text-3xl font-bold text-center" style={{ color: cardData.Total?.color || '#540094' }}>
                  {(Number(cardData?.Total?.count) || 0).toLocaleString('en-US')}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Top Right: Under Brand Review */}
      <div className="flex-1">
        <div
          className="card border-0 relative overflow-hidden h-full bg-white dark:bg-card rounded-lg shadow-md"
          style={{ borderRight: `5px solid ${cardData.Active?.color || '#FF0000'}`,
          borderBottom: `5px solid ${cardData.Active?.color || '#FF0000'}` }}
        >
          {shouldShowLoader ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="p-4 h-full flex flex-col">
              <div className="text-base font-semibold text-card-foreground text-center mb-2">
                {cardData?.Active?.title || 'Under Brand Review'}
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="text-3xl font-bold text-center" style={{ color: cardData.Active?.color || '#FF0000' }}>
                  {(Number(cardData?.Active?.count) || 0).toLocaleString('en-US')}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Left: Takedown Requested / Initiated */}
      <div className="flex-1">
        <div
          className="card border-0 relative overflow-hidden h-full bg-white dark:bg-card rounded-lg shadow-md"
          style={{ borderRight: `5px solid ${cardData.InProgress?.color || '#FFDB58'}`,
          borderBottom: `5px solid ${cardData.InProgress?.color || '#FFDB58'}` }}
        >
          {shouldShowLoader ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="p-4 h-full flex flex-col">
              <div className="text-base font-semibold text-card-foreground text-center mb-2">
                {cardData?.InProgress?.title || 'Takedown Requested / Initiated'}
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="text-3xl font-bold text-center" style={{ color: cardData.InProgress?.color || '#FFDB58' }}>
                  {(Number(cardData?.InProgress?.count) || 0).toLocaleString('en-US')}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Right: Closed Incidents */}
      <div className="flex-1">
        <div
          className="card border-0 relative overflow-hidden h-full bg-white dark:bg-card rounded-lg shadow-md"
          style={{ borderRight: `5px solid ${cardData.Closed?.color || '#2E8B57'}`,
          borderBottom: `5px solid ${cardData.Closed?.color || '#2E8B57'}` }}
        >
          {shouldShowLoader ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="p-4 h-full flex flex-col">
              <div className="text-base font-semibold text-card-foreground text-center mb-2">
                {cardData?.Closed?.title || 'Closed Incidents'}
              </div>
              <div className="flex-1 flex flex-col items-center justify-center gap-1">
                <div className="text-3xl font-bold text-center" style={{ color: cardData.Closed?.color || '#2E8B57' }}>
                  {(Number(cardData?.Closed?.count) || 0).toLocaleString('en-US')}
                </div>
                {cardData?.Closed?.breakdown && (
                  <div className="flex items-stretch justify-evenly w-full gap-1 mt-2 px-1">
                    <div className="flex flex-col items-center flex-1 min-w-0">
                      <span className="text-xs font-semibold text-card-foreground dark:text-white text-center break-words leading-tight">Taken Down</span>
                      <span className="text-xs text-green-600 font-semibold">
                        {(Number(cardData.Closed.breakdown[0]?.value) || 0).toLocaleString('en-US')}
                      </span>
                    </div>
                    <div className="w-[1px] bg-gray-300 dark:bg-gray-500 h-8 self-center flex-shrink-0"></div>
                    <div className="flex flex-col items-center flex-1 min-w-0">
                      <span className="text-xs font-semibold text-card-foreground dark:text-white text-center break-words leading-tight">No Action</span>
                      <span className="text-xs text-orange-500 font-semibold">
                        {(Number(cardData.Closed.breakdown[1]?.value) || 0).toLocaleString('en-US')}
                      </span>
                    </div>
                    <div className="w-[1px] bg-gray-300 dark:bg-gray-500 h-8 self-center flex-shrink-0"></div>
                    <div className="flex flex-col items-center flex-1 min-w-0">
                      <span className="text-xs font-semibold text-card-foreground dark:text-white text-center break-words leading-tight">Recommend to Legal</span>
                      <span className="text-xs text-blue-600 font-semibold">
                        {(Number(cardData.Closed.breakdown[2]?.value) || 0).toLocaleString('en-US')}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsCards;
