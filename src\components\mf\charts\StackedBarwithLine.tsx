import * as React from "react";
import { useRef, useEffect, useState } from "react";
import { Bar, CartesianGrid, XAxis, YA<PERSON>s, Line, Composed<PERSON><PERSON>, LabelList,ResponsiveContainer } from "recharts";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartLegend,ChartTooltip,ChartTooltipContent } from "@/components/ui/chart";
import { getXAxisAngle, formatNumber } from '@/lib/utils';
import HeaderRow from "../HeaderRow";
import { Loader2 } from "lucide-react";
import { useFullscreen } from "@/hooks/fullscreen";

interface XAxisConfig {
  dataKey?: string;
  tickLine?: boolean;
  tickMargin?: number;
  axisLine?: boolean;
  tickFormatter?: (value: string) => string;
  angle?: number;
  textAnchor?: string;
  dy: number;
  dx?: number;
  height?: number;
  tick?: any; // Allow custom tick renderer
}
interface YAxis1 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
}
interface YAxis2 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
}
interface TrafficTrendData {
  month?: string;
  Invalid?: number;
  Valid?: number;
  "Invalid %"?: number;
  
}


interface StackBarLineProps {
  chartData?: TrafficTrendData[];
  chartConfig?: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
  handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  handleFrequencyChange?: (value: string) => void; 
  selectedType?: string;
  selectoptions?:string[];
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  placeholder?:string;
  isHorizontal?:boolean;
  xAxisConfig?: XAxisConfig;
  YAxis1?: YAxis1;
  YAxis2?: YAxis2;
  isLoading?:boolean;
  selectedFrequency?:string;
  isLegend?:boolean;
  showTrendline?: boolean;
  trendlineKey?: string;
  isStacked?: boolean;
  barRadius?: number;
  barWidth?: number;
  chartHeight?: number;
  legendFontSize?: number; // Add this prop
  stickyLegend?: boolean; // Add sticky legend prop
  enableHorizontalScroll?: boolean; // Add horizontal scroll prop
  legendRows?: number; // Number of rows for legend layout
  legendItemsPerRow?: number; // Items per row for legend layout
  legendGap?: number; // Gap between legends in pixels
  legendMarginTop?: number; // Margin top for legend container
  legendHeight?: number; // Height reserved for legend area
  legendMarginBottom?: number; // Margin bottom for legend container
  isExpanded?: boolean; // New prop for fullscreen state
}

const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.25);
  }
`;

const StackedBarWithLine: React.FC<StackBarLineProps> = ({
  chartData = [],
    chartConfig = {},
    handleTypeChange,
    visitEventOptions,
    selectedType,
    selectedFrequency,
    handleFrequencyChange,
    selectoptions=[],
    handleExport,

    onExport,
    onExpand,
    isLoading,
    title ,
    isSelect= false,
    isRadioButton =false,
    placeholder="",
    isHorizontal=false,
    isLegend=true,
    xAxisConfig = {
    dataKey: "month", 
    tickLine: false,
    tickMargin: 5,
    axisLine: true,
    angle: 0,
    textAnchor: "middle",
    dy: 5,
    dx: 0,
    height: 35
  },
  YAxis1 = {
    yAxisId: "left",
    orientation: "left",
    stroke: "hsl(var(--chart-1))",
    tickFormatter: (value: number) => formatNumber(value)
  },
  YAxis2 = {
    yAxisId: "right",
    orientation: "right",
    stroke: "hsl(var(--chart-3))",
    tickFormatter: (value: number) => `${value}%`,
  },
  showTrendline,
  trendlineKey,
  isStacked = true,
  barRadius = 0,
  barWidth = 80,
  chartHeight = 320,
  legendFontSize = 10, // Default to 10px
  enableHorizontalScroll = false, // Default to false
  legendRows = 1, // Default to 1 row
  legendItemsPerRow = 0, // Default to 0 (auto-calculate)
  legendGap = 24, // Default gap between legends in pixels (24px = 1.5rem)
  legendMarginTop = 8, // Default margin top for legend container (8px = 0.5rem)
  legendHeight = 60, // Default height reserved for legend area
  legendMarginBottom = 0, // Default margin bottom for legend container
  isExpanded = false, // New prop for fullscreen state
}) => {
   const maxVisiblePoints = 7;
const calculatedBarWidth = barWidth;

  // Function to get chart height based on fullscreen state
  const getFullscreenChartHeight = () => {
    if (isExpanded) {
      return Math.max(window.innerHeight - 120, 400);
    }
    return chartHeight;
  };

  // Function to get bar width based on fullscreen state
  const getFullscreenBarWidth = () => {
    if (isExpanded) {
      return Math.max(barWidth * 1.2, 100);
    }
    return barWidth;
  };

  const currentChartHeight = getFullscreenChartHeight();
  const currentBarWidth = getFullscreenBarWidth();

const chartWidth =
  chartData.length > maxVisiblePoints
    ? chartData.length * calculatedBarWidth
    : undefined; 
  const labels = Object.values(chartConfig || {}).map(item => item.label);
  const colors = Object.values(chartConfig || {}).map(item => item.color);
  const months = chartData?.map(item => item.month).filter((month): month is string => month !== undefined) || [];
  const mergedXAxisConfig = { ...xAxisConfig, angle: 0, textAnchor: "middle" };
  const mergedYAxis1Props = { ...YAxis1 };
  const mergedYAxis2Props = { ...YAxis2 };

  const CustomLegendContent = ({ labels,colors }: { labels: string[] , colors: string[];}) => {
    // If legendRows is 1, show in a single scrollable line
    if (legendRows === 1) {
      return (
        <div className="overflow-x-auto custom-scrollbar px-3" style={{ maxWidth: '100%' }}>
          <div className="flex items-center justify-start pt-2 pb-3 px-3 dark:text-white dark:bg-gray-900" style={{ minWidth: 'max-content' }}>
            {labels.map((labelText: string, index: number) => (
              <div 
                className="flex items-center flex-shrink-0" 
                key={index}
                style={{ marginLeft: index === 0 ? 0 : legendGap / 2, marginRight: index === labels.length - 1 ? 0 : legendGap / 2 }}
              >
                <span style={{ backgroundColor: colors[index] }} className={`${isExpanded ? 'w-4 h-4' : 'w-3 h-3'} rounded-full`}></span>
                <span style={{ fontSize: isExpanded ? Math.max(legendFontSize + 4, 14) + 'px' : legendFontSize + 'px' }} className="ml-2">{labelText}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    
    // Calculate items per row if not specified (for multi-row layout)
    const itemsPerRow = legendItemsPerRow > 0 ? legendItemsPerRow : Math.ceil(labels.length / legendRows);
    
    // Create rows of legend items
    const legendRowsArray = [];
    for (let i = 0; i < labels.length; i += itemsPerRow) {
      const rowItems = labels.slice(i, i + itemsPerRow);
      const rowColors = colors.slice(i, i + itemsPerRow);
      
      legendRowsArray.push(
        <div key={i} className="flex justify-center pt-2 dark:text-white dark:bg-gray-900 ">
          {rowItems.map((labelText: string, rowIndex: number) => (
            <div 
              className="flex items-center" 
              key={rowIndex}
              style={{ marginLeft: rowIndex === 0 ? 0 : legendGap / 2, marginRight: rowIndex === rowItems.length - 1 ? 0 : legendGap / 2 }}
            >
              <span style={{ backgroundColor: rowColors[rowIndex] }} className={`${isExpanded ? 'w-4 h-4' : 'w-3 h-3'} rounded-full`}></span>
              <span style={{ fontSize: isExpanded ? Math.max(legendFontSize + 4, 14) + 'px' : legendFontSize + 'px' }} className="ml-2">{labelText}</span>
            </div>
          ))}
        </div>
      );
    }
    
    return (
      <div className="flex flex-col space-y-1">
        {legendRowsArray}
      </div>
    );
  };
  return (
    <>
      <style>{scrollbarStyles}</style>
      <Card className={`border-none w-full p-0 ${isExpanded ? 'h-[100vh]' : ''}`}>
      {/* <CardTitle className="p-2 ">
     <HeaderRow
      visitEventOptions={visitEventOptions}
     handleTypeChange={handleTypeChange}
     selectedType={selectedType}
     selectedFrequency={selectedFrequency}
     handleFrequencyChange={handleFrequencyChange}
     title={title}
     onExpand={onExpand}
      handleExport={handleExport}
     selectoptions={selectoptions}
     isRadioButton={isRadioButton}
     isSelect={isSelect}
     onExport={onExport}
     placeholder={placeholder}/>
     </CardTitle> */}
     {isLoading ? (
      <div className="flex items-center justify-center h-[260px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-white" />
       </div>
     ) : (
      <CardContent className=" sm:w-full p-0">
               <ChartContainer config={chartConfig} className={`relative sm:w-full lg:w-full p-0 ${isExpanded ? 'h-[100vh]' : `h-[${currentChartHeight}px]`}`}>
      
       <div className="flex flex-col h-full">
         {/* Scrollable Chart Area */}
         <div className={`flex-1 ${enableHorizontalScroll ? 'overflow-x-auto' : 'overflow-hidden'}`}>
           <div
             style={{
               height: `${isExpanded ? currentChartHeight - (isLegend ? legendHeight + 40 : 60) : currentChartHeight - (isLegend ? legendHeight : 20)}px`,
               ...(chartWidth ? { minWidth: `${chartWidth}px` } : { width: '100%' }),
             }}
             className="custom-scrollbar"
           >
             <ResponsiveContainer width="100%" height={isExpanded ? currentChartHeight - (isLegend ? legendHeight + 40 : 60) : "100%"}>
               {chartData.length > 0 ? (
                 <ComposedChart
                   accessibilityLayer
                   data={chartData}
                   margin={{ top: 20, right: 5, left: 5, bottom: 0 }}
                   barGap={isStacked ? 10 : 0}
                   barCategoryGap={isStacked ? "15%" : "40%"}
                 >
                   {/* Legend is shown below chart when isLegend is true */}
              <ChartTooltip 
                cursor={false} 
                content={({ active, payload, label }) => {
                  if (!active || !payload?.length) return null;
                  
                  return (
                    <div className="grid min-w-[10rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl">
                      <div className="font-medium bg-gray-100  dark:bg-gray-700 dark:text-white">{label}</div>
                      <div className="grid gap-1.5">
                        {payload.map((item, index) => {
                          const itemConfig = chartConfig[item.dataKey as keyof typeof chartConfig];
                          const indicatorColor = item.payload.fill || item.color;
                          
                          return (
                            <div
                              key={item.dataKey}
                              className="flex w-full flex-wrap items-center gap-2"
                            >
                              <div
                                className="shrink-0 rounded-full border-[--color-border] bg-[--color-bg] h-3 w-3"
                                style={
                                  {
                                    "--color-bg": indicatorColor,
                                    "--color-border": indicatorColor,
                                  } as React.CSSProperties
                                }
                              />
                              <div className="flex flex-1 justify-between leading-none text-small-font items-center">
                                <div className="grid gap-1.5">
                                  <span className="text-muted-foreground">
                                    {itemConfig?.label || item.name}
                                  </span>
                                </div>
                                {item.value !== undefined && (
                                  <span className="font-mono font-medium text-small-font tabular-nums text-foreground">
                                    {typeof item.value === 'number' ? (
                                      // Only format as percentage if it's the trend line (which uses trendlineKey)
                                      showTrendline && item.dataKey === trendlineKey ? 
                                        `${item.value.toFixed(2)}%` : 
                                        item.value.toLocaleString()
                                    ) : (
                                      "-"
                                    )}
                                  </span>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                }}
              />

              <XAxis  className="text-small-font" {...mergedXAxisConfig}
                interval={0}
                height={mergedXAxisConfig.height || 35}
                dx={mergedXAxisConfig.dx}
                fontSize={10}
              />
              <YAxis className="text-small-font" {...mergedYAxis1Props}
                tickFormatter={(value: number) => formatNumber(value)}
                fontSize={10}
              />
              <YAxis className="text-small-font"
                {...mergedYAxis2Props}
                fontSize={10}
              />
               
              {(() => {
                // Get all bar keys (excluding trendline if it should be a line)
                const barKeys = Object.keys(chartConfig).filter(key => 
                  !(showTrendline && key === trendlineKey)
                );
                
                return Object.keys(chartConfig).map((key, index) => {
                  // If this is the trendline and showTrendline is true, render as Line
                  if (showTrendline && key === trendlineKey) {
                    return (
                      <Line
                        key={`line-${index}`}
                        type="monotone"
                        dataKey={key}
                        stroke={chartConfig[key].color}
                        strokeWidth={2}
                        dot={{ fill: chartConfig[key].color, r: 4 }}
                        activeDot={{ r: 6, fill: chartConfig[key].color }}
                        yAxisId="right"
                      />
                    );
                  }
                  
                  // Otherwise render as Bar
                  const barIndex = barKeys.indexOf(key);
                  const isTopBar = barIndex === barKeys.length - 1;
                  
                  // Determine which Y-axis to use based on the key
                  const yAxisId = key === trendlineKey ? "right" : "left";
                  
                  return (
                    <Bar
                      key={`bar-${index}`}
                      dataKey={key}
                      fill={chartConfig[key].color}
                      radius={isStacked && isTopBar ? [barRadius, barRadius, 0, 0] : [0, 0, 0, 0]}
                      stackId={isStacked ? "a" : undefined}
                      barSize={currentBarWidth}
                      yAxisId={yAxisId}
                      style={{ 
                        fill: chartConfig[key].color,
                        backgroundColor: chartConfig[key].color 
                      }}
                    >
                    </Bar>
                  );
                });
              })()}
                 </ComposedChart>
                 
               ) : (
                 <div className="flex items-center justify-center h-full w-full">
                   <span className="text-sm dark:text-white">No Data Found !</span>
                 </div>
               )}
                       
             </ResponsiveContainer>
           </div>
         </div>
         
         {/* Legend below chart */}
         {isLegend && (
           <div className={`flex justify-center ${isExpanded ? 'mt-4 mb-2' : ''}`} style={{ 
             marginTop: isExpanded ? undefined : `${legendMarginTop}px`, 
             marginBottom: isExpanded ? undefined : `${legendMarginBottom}px` 
           }}>
             <CustomLegendContent labels={labels} colors={colors} />
           </div>
         )}
       </div>
     </ChartContainer>
   </CardContent>
   )}
 </Card>
 </>
);
};

export { StackedBarWithLine };
