"use client";
import React from "react";
import { Card } from "@/components/ui/card";

import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
 
interface AttractiveCardProps {
  title?: string;
  value?: React.ReactNode;
  width?: string;
  height?: string;
  borderColor?: string; // Can be Tailwind class or hex like "#D6D85D"
  titleColor?: string;
  titleFontSize?: string;
  valueColor?: string;
  valueFontSize?: string;
  className?: string;
  percentage?:string;
  children?: React.ReactNode;
  titlePadding?: string; // New prop for title padding
}
 
const AttractiveCard: React.FC<AttractiveCardProps> = ({
  title,
  value,
  width = "w-96",
  height = "h-48",
  borderColor = "#FACC15", // fallback to yellow-400
  titleColor = "text-black dark:text-white",
  titleFontSize = "text-md",
  valueColor = "text-black dark:text-white",
  valueFontSize = "text-xl",
  className = "",
  percentage,
  children,
  titlePadding = "mb-1", // Default padding
}) => {
  return (
    <Card
      className={`relative ${width} ${height} rounded-xl bg-white overflow-hidden dark:bg-card ${className}`}
      style={{ borderRight: `5px solid ${borderColor}`,
        borderBottom: `5px solid ${borderColor}`
       }}
    >
 
      <div className="p-4 space-y-2 h-full flex flex-col justify-between">
        <div>
          {title && (
            <div className={`${titleFontSize} font-semibold ${titleColor} ${titlePadding} flex justify-center items-center dark:text-white`}>
              {title}
            </div>
          )}
          <div
            className={`flex items-center dark:text-white ${
              value && percentage ? "justify-between" : "justify-center"
            }`}
          >
            {value && (
              <TooltipProvider>
                <Tooltip >
                  <TooltipTrigger asChild>
                    <div
                      className={`${valueFontSize} font-semibold dark:text-white`}
                      style={{ color: valueColor }}
                    >
                      {React.isValidElement(value) ? value : Number(value).toLocaleString('en-US')}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    {React.isValidElement(value) ? "Loading..." : Number(value).toLocaleString("en-US")}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {percentage && (
              <div
                className={`${valueFontSize} font-semibold dark:text-white`}
                style={{ color: valueColor }}
              >
                {percentage}<span className="text-sm p-1 font-semibold">%</span>
              </div>
            )}
          </div>
        </div>
        {children && (
          <div className="mt-2">{children}</div>
        )}
      </div>
    </Card>
  );
};
 
export default AttractiveCard;