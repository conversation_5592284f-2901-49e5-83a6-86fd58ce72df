@tailwind base;
@tailwind components;
@tailwind utilities;

.textFont {
  font-family: Arial, Helvetica, sans-serif;
}
body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .scrollbar {
    scrollbar-width: thick;
    transition: all 150ms ease-in-out;
    &::-webkit-scrollbar {
      width: 0.5rem;
      padding-bottom: 0%;
      height: 0.5rem; /* height of horizontal scrollbar ← You're missing this */
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
    }
    &::-webkit-scrollbar-thumb {
      background-color: lightgray;
      border-radius: 10px;
    }
  }
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
    &::-webkit-scrollbar {
      display: none;  /* Safari and Chrome */
    }
  }
  
  /* Horizontal scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    &::-webkit-scrollbar {
      height: 6px; /* height for horizontal scrollbar */
      width: 6px;  /* width for vertical scrollbar */
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      &:hover {
        background: #a8a8a8;
      }
    }
  }
  
  .scrollbar-thumb-gray-300 {
    &::-webkit-scrollbar-thumb {
      background: #d1d5db;
    }
  }
  
  .scrollbar-track-gray-100 {
    &::-webkit-scrollbar-track {
      background: #f3f4f6;
    }
  }
  
  /* Custom media query for screens smaller than 1600px */
  @media (max-width: 1599px) {
    .legend-scroll-small {
      overflow-y: auto;
      scrollbar-width: thick;
      transition: all 150ms ease-in-out;
    }
    .legend-scroll-small::-webkit-scrollbar {
      width: 0.5rem;
      height: 0.5rem;
    }
    .legend-scroll-small::-webkit-scrollbar-track {
      box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
    }
    .legend-scroll-small::-webkit-scrollbar-thumb {
      background-color: lightgray;
      border-radius: 10px;
    }
  }

  /* DonutChart responsive styles */
  .donut-chart-container {
    width: 100%;
    height: 100%;
    min-height: 0;
    overflow: hidden;
  }

  /* Responsive chart sizing */
  @media (max-width: 640px) {
    .donut-chart-container .recharts-wrapper {
      min-height: 150px !important;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .donut-chart-container .recharts-wrapper {
      min-height: 180px !important;
    }
  }

  @media (min-width: 1025px) {
    .donut-chart-container .recharts-wrapper {
      min-height: 200px !important;
    }
  }

  /* Prevent legend text overflow */
  .donut-legend-item {
    word-break: break-word;
    hyphens: auto;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 306, 82%, 35%;
    --primary-foreground: 0 0% 98%;
    --secondary: 274, 100%, 29%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 215.3,25%,26.7%;
    --foreground: 0 0% 98%;
    --card: 221, 39%, 11%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 306, 82%, 35%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.incident-table-scroll thead {
  background: white;
  z-index: 2;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
